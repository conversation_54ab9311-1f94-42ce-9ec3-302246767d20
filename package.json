{"name": "open-webui", "version": "0.6.21", "private": true, "scripts": {"dev": "npm run pyodide:fetch && vite dev --host", "dev:5050": "npm run pyodide:fetch && vite dev --port 5050", "build": "npm run pyodide:fetch && vite build", "build:watch": "npm run pyodide:fetch && vite build --watch", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "npm run lint:frontend ; npm run lint:types ; npm run lint:backend", "lint:frontend": "eslint . --fix", "lint:types": "npm run check", "lint:backend": "pylint backend/", "format": "prettier --plugin-search-dir --write \"**/*.{js,ts,svelte,css,md,html,json}\"", "format:backend": "black . --exclude \".venv/|/venv/\"", "i18n:parse": "i18next --config i18next-parser.config.ts && prettier --write \"src/lib/i18n/**/*.{js,json}\"", "cy:open": "cypress open", "test:frontend": "vitest --passWithNoTests", "pyodide:fetch": "node scripts/prepare-pyodide.js"}, "devDependencies": {"@sveltejs/adapter-auto": "3.2.2", "@sveltejs/adapter-static": "^3.0.2", "@sveltejs/kit": "^2.5.20", "@sveltejs/vite-plugin-svelte": "^3.1.1", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/postcss": "^4.0.0", "@tailwindcss/typography": "^0.5.13", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "cypress": "^13.15.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-cypress": "^3.4.0", "eslint-plugin-svelte": "^2.43.0", "i18next-parser": "^9.0.1", "postcss": "^8.4.31", "prettier": "^3.3.3", "prettier-plugin-svelte": "^3.2.6", "sass-embedded": "^1.81.0", "svelte": "^4.2.18", "svelte-check": "^3.8.5", "svelte-confetti": "^1.3.2", "tailwindcss": "^4.0.0", "tslib": "^2.4.1", "typescript": "^5.5.4", "vite": "^5.4.14", "vitest": "^1.6.1"}, "type": "module", "dependencies": {"@azure/msal-browser": "^4.5.0", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/lang-python": "^6.1.6", "@codemirror/language-data": "^6.5.1", "@codemirror/theme-one-dark": "^6.1.2", "@floating-ui/dom": "^1.7.2", "@huggingface/transformers": "^3.0.0", "@joplin/turndown-plugin-gfm": "^1.0.62", "@mediapipe/tasks-vision": "^0.10.17", "@pyscript/core": "^0.4.32", "@sveltejs/adapter-node": "^2.0.0", "@sveltejs/svelte-virtual-list": "^3.0.1", "@tiptap/core": "^3.0.7", "@tiptap/extension-bubble-menu": "^2.26.1", "@tiptap/extension-code-block-lowlight": "^3.0.7", "@tiptap/extension-drag-handle": "^3.0.7", "@tiptap/extension-file-handler": "^3.0.7", "@tiptap/extension-floating-menu": "^2.26.1", "@tiptap/extension-highlight": "^3.0.7", "@tiptap/extension-image": "^3.0.7", "@tiptap/extension-link": "^3.0.7", "@tiptap/extension-list": "^3.0.7", "@tiptap/extension-mention": "^3.0.9", "@tiptap/extension-table": "^3.0.7", "@tiptap/extension-typography": "^3.0.7", "@tiptap/extension-youtube": "^3.0.7", "@tiptap/extensions": "^3.0.7", "@tiptap/pm": "^3.0.7", "@tiptap/starter-kit": "^3.0.7", "@xyflow/svelte": "^0.1.19", "async": "^3.2.5", "bits-ui": "^0.21.15", "chart.js": "^4.5.0", "codemirror": "^6.0.1", "codemirror-lang-elixir": "^4.0.0", "codemirror-lang-hcl": "^0.1.0", "crc-32": "^1.2.2", "dayjs": "^1.11.10", "dompurify": "^3.2.5", "eventsource-parser": "^1.1.2", "file-saver": "^2.0.5", "focus-trap": "^7.6.4", "fuse.js": "^7.0.0", "heic2any": "^0.0.4", "highlight.js": "^11.9.0", "html-entities": "^2.5.3", "html2canvas-pro": "^1.5.11", "i18next": "^23.10.0", "i18next-browser-languagedetector": "^7.2.0", "i18next-resources-to-backend": "^1.2.0", "idb": "^7.1.1", "js-sha256": "^0.10.1", "jspdf": "^3.0.0", "katex": "^0.16.22", "kokoro-js": "^1.1.1", "leaflet": "^1.9.4", "lowlight": "^3.3.0", "marked": "^9.1.0", "mermaid": "^11.6.0", "paneforge": "^0.0.6", "panzoom": "^9.4.3", "pdfjs-dist": "^5.3.93", "prosemirror-collab": "^1.3.1", "prosemirror-commands": "^1.6.0", "prosemirror-example-setup": "^1.2.3", "prosemirror-history": "^1.4.1", "prosemirror-keymap": "^1.2.2", "prosemirror-markdown": "^1.13.1", "prosemirror-model": "^1.23.0", "prosemirror-schema-basic": "^1.2.3", "prosemirror-schema-list": "^1.5.1", "prosemirror-state": "^1.4.3", "prosemirror-tables": "^1.7.1", "prosemirror-view": "^1.34.3", "pyodide": "^0.27.3", "socket.io-client": "^4.2.0", "sortablejs": "^1.15.6", "svelte-sonner": "^0.3.19", "tippy.js": "^6.3.7", "turndown": "^7.2.0", "turndown-plugin-gfm": "^1.0.2", "undici": "^7.3.0", "uuid": "^9.0.1", "vite-plugin-static-copy": "^2.2.0", "y-prosemirror": "^1.3.7", "yaml": "^2.7.1", "yjs": "^13.6.27"}, "engines": {"node": ">=18.13.0 <=22.x.x", "npm": ">=6.0.0"}}