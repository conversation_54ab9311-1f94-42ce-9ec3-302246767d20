{"-1 for no limit, or a positive integer for a specific limit": "-1 für kein Limit oder eine positive Zahl für ein spezifisches Limit", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' oder '-1' für keine Ablaufzeit.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(z. B. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(z. B. `sh webui.sh --api`)", "(latest)": "(neueste)", "(leave blank for to use commercial endpoint)": "(leer lassen, um kommerziellen Endpunkt zu verwenden)", "[Last] dddd [at] h:mm A": "", "[Today at] h:mm A": "", "[Yesterday at] h:mm A": "", "{{ models }}": "{{ <PERSON><PERSON> }}", "{{COUNT}} Available Tools": "{{COUNT}} verfügbare Werkzeuge", "{{COUNT}} characters": "{{COUNT}} <PERSON><PERSON><PERSON>", "{{COUNT}} hidden lines": "{{COUNT}} versteckte Zeilen", "{{COUNT}} Replies": "{{COUNT}} Antworten", "{{COUNT}} words": "{{COUNT}} <PERSON><PERSON><PERSON>", "{{user}}'s Chats": "{{user}}s Chats", "{{webUIName}} Backend Required": "{{webUIName}}-<PERSON><PERSON> <PERSON><PERSON>", "*Prompt node ID(s) are required for image generation": "*Prompt-Node-ID(s) sind für die Bildgenerierung erforderlich", "A new version (v{{LATEST_VERSION}}) is now available.": "Eine neue Version (v{{LATEST_VERSION}}) ist jetzt verfügbar.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Aufgabenmodelle werden beispielsweise zur Generierung von Chat-Titeln oder Websuchanfragen verwendet", "a user": "ein <PERSON>", "About": "<PERSON><PERSON>", "Accept autocomplete generation / Jump to prompt variable": "Automatische Vervollständigung akzeptieren / Zur Prompt-Variable springen", "Access": "Zugang", "Access Control": "Zugangskontrolle", "Accessible to all users": "<PERSON><PERSON><PERSON> alle Benutzer zugä<PERSON>", "Account": "Ko<PERSON>", "Account Activation Pending": "Kontoaktivierung ausstehend", "Accurate information": "Präzise Information(en)", "Action": "Aktion", "Actions": "Aktionen", "Activate": "Aktivieren", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Aktivieren Sie diesen Befehl, indem Sie \"/{{COMMAND}}\" in die Chat-Eingabe eingeben.", "Active Users": "Aktive Benutzer", "Add": "Hinzufügen", "Add a model ID": "Modell-ID hinzufügen", "Add a short description about what this model does": "Fügen Sie eine kurze Beschreibung über dieses Modell hinzu", "Add a tag": "Tag hinzufügen", "Add Arena Model": "Arena-<PERSON><PERSON> hinzufügen", "Add Connection": "Verbindung hinzufügen", "Add Content": "Inhalt hinzufügen", "Add content here": "Inhalt hier hinzufügen", "Add Custom Parameter": "Benutzerdefinierten Parameter hinzufügen", "Add custom prompt": "Benutzerdefinierten Prompt hinzufügen", "Add Details": "", "Add Files": "<PERSON><PERSON>", "Add Group": "Gruppe hinzufügen", "Add Memory": "Erinnerung hinzufügen", "Add Model": "<PERSON><PERSON>", "Add Reaction": "Reaktion hinzufügen", "Add Tag": "Tag hinzufügen", "Add Tags": "Tags hinzufügen", "Add text content": "Textinhalt hinzufügen", "Add User": "Benutzer hinzufügen", "Add User Group": "Benutzergruppe hinzufügen", "Additional Config": "", "Additional configuration options for marker. This should be a JSON string with key-value pairs. For example, '{\"key\": \"value\"}'. Supported keys include: disable_links, keep_pageheader_in_output, keep_pagefooter_in_output, filter_blank_pages, drop_repeated_text, layout_coverage_threshold, merge_threshold, height_tolerance, gap_threshold, image_threshold, min_line_length, level_count, default_level": "", "Adjusting these settings will apply changes universally to all users.": "Das Anpassen dieser Einstellungen wird Änderungen universell auf alle Benutzer anwenden.", "admin": "Administrator", "Admin": "Administrator", "Admin Panel": "Administration", "Admin Settings": "Administration", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Administratoren haben jederzeit Zugriff auf alle Werkzeuge; Benutzern müssen Werkzeuge pro Modell im Arbeitsbereich zugewiesen werden.", "Advanced Parameters": "Erweiterte Parameter", "Advanced Params": "Erweiterte Parameter", "AI": "KI", "All": "Alle", "All Documents": "Alle Dokumente", "All models deleted successfully": "Alle Modelle erfolgreich gelöscht", "Allow Call": "Anruffunk<PERSON> erlauben", "Allow Chat Controls": "Chat-Steuerung erlauben", "Allow Chat Delete": "<PERSON><PERSON><PERSON> von Chats erlauben", "Allow Chat Deletion": "<PERSON><PERSON><PERSON> von Chats erlauben", "Allow Chat Edit": "<PERSON><PERSON><PERSON> von <PERSON> erlauben", "Allow Chat Export": "Erlaube Chat Export", "Allow Chat Params": "", "Allow Chat Share": "Erlaube Chat teilen", "Allow Chat System Prompt": "Erlaube Chat System Prompt", "Allow Chat Valves": "", "Allow File Upload": "<PERSON><PERSON><PERSON><PERSON> von <PERSON>", "Allow Multiple Models in Chat": "Multiple Modelle in Chat erlauben", "Allow non-local voices": "Nicht-lokale Stimmen erlauben", "Allow Speech to Text": "Sprache zu Text erlauben", "Allow Temporary Chat": "Temporäre Chats erlauben", "Allow Text to Speech": "Text zu Sprache erlauben", "Allow User Location": "Standort freigeben", "Allow Voice Interruption in Call": "Unterbrechung durch Stimme im Anruf zulassen", "Allowed Endpoints": "Erlaubte Endpunkte", "Allowed File Extensions": "Erlaubte Dateiendungen", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "Erlaubte Dateiendungen für den Upload. Trennen Sie mehrere Erweiterungen mit Kommas. <PERSON><PERSON>, um alle Dateiendungen zu erlauben.", "Already have an account?": "Haben Sie bereits einen Account?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "Eine Alternative zu top_p, die darauf abzielt, ein ausgewogenes Verhältnis von Qualität und Vielfalt zu gewährleisten. Der Parameter p stellt die Mindestwahrscheinlichkeit dar, mit der ein Token berücksichtigt wird, relativ zur Wahrscheinlichkeit des wahrscheinlichsten Token. Wenn beispielsweise p = 0,05 ist und das wahrscheinlichste Token eine Wahrscheinlichkeit von 0,9 hat, werden Logits mit einem Wert kleiner als 0,045 herausgefiltert.", "Always": "Immer", "Always Collapse Code Blocks": "Code-<PERSON><PERSON><PERSON><PERSON> immer zuklappen", "Always Expand Details": "Details immer aufklappen", "Always Play Notification Sound": "Benachrichtungston immer abspielen", "Amazing": "Fantastisch", "an assistant": "ein Assistent", "Analytics": "", "Analyzed": "<PERSON><PERSON><PERSON><PERSON>", "Analyzing...": "Analysiere...", "and": "und", "and {{COUNT}} more": "und {{COUNT}} mehr", "and create a new shared link.": "und erstellen Sie einen neuen freigegebenen Link.", "Android": "Android", "API": "API", "API Base URL": "API-Basis-URL", "API Base URL for Datalab Marker service. Defaults to: https://www.datalab.to/api/v1/marker": "", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "API-Schlüssel", "API Key created.": "API-Schlüssel erstellt.", "API Key Endpoint Restrictions": "API-Schlüssel Endpunkteinschränkungen", "API keys": "API-Schlüssel", "API Version": "API Version", "Application DN": "Anwendungs-DN", "Application DN Password": "Anwendungs-DN-Passwort", "applies to all users with the \"user\" role": "gilt für alle Benutzer mit der Rolle \"Benutzer\"", "April": "April", "Archive": "Archivieren", "Archive All Chats": "Alle Chats archivieren", "Archived Chats": "Archivierte Cha<PERSON>", "archived-chat-export": "archivierter-chat-export", "Are you sure you want to clear all memories? This action cannot be undone.": "Sind <PERSON> sicher, dass Sie alle Erinnerungen löschen möchten? Diese Handlung kann nicht rückgängig gemacht werden.", "Are you sure you want to delete this channel?": "Sind <PERSON> sicher, dass Si<PERSON> diesen Kanal löschen möchten?", "Are you sure you want to delete this message?": "Sind <PERSON> sicher, dass Sie diese Nachricht löschen möchten?", "Are you sure you want to unarchive all archived chats?": "Sind <PERSON> sicher, dass Sie alle archivierten Chats wiederherstellen möchten?", "Are you sure?": "Sind Sie sicher?", "Arena Models": "Arena-Modelle", "Artifacts": "Artefakte", "Ask": "Fragen", "Ask a question": "<PERSON><PERSON><PERSON> eine Fr<PERSON>", "Assistant": "Assistent", "Attach file from knowledge": "Datei aus Wissensspeicher anhängen", "Attention to detail": "Aufmerksamkeit für Details", "Attribute for Mail": "Attribut für E-Mail", "Attribute for Username": "Attribut für Benutzername", "Audio": "Audio", "August": "August", "Auth": "Authentifizierung", "Authenticate": "Authentifizieren", "Authentication": "Authentifizierung", "Auto": "Automatisch", "Auto-Copy Response to Clipboard": "Antwort automatisch in die Zwischenablage kopieren", "Auto-playback response": "Antwort automatisch abspielen", "Autocomplete Generation": "Automatische Vervollständigung", "Autocomplete Generation Input Max Length": "Maximale Eingabenlände für automatische Vervollständigung", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111-API-Authentifizierungszeichenfolge", "AUTOMATIC1111 Base URL": "AUTOMATIC1111-Basis-URL", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111-Basis-URL ist erforderlich.", "Available list": "Verfügbare Liste", "Available Tools": "Verfügbare Werkzeuge", "available!": "Verfügbar!", "Awful": "<PERSON><PERSON><PERSON><PERSON>", "Azure AI Speech": "Azure AI Speech", "Azure Region": "Azure-Region", "Back": "Zurück", "Bad Response": "Schlechte Antwort", "Banners": "Banner", "Base Model (From)": "<PERSON><PERSON><PERSON><PERSON> (From)", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "", "before": "bereits geteilt", "Being lazy": "Faulheit", "Beta": "Beta", "Bing Search V7 Endpoint": "Bing Search V7-Endpunkt", "Bing Search V7 Subscription Key": "Bing Search V7-Abonnement-Sc<PERSON><PERSON>ssel", "BM25 Weight": "", "Bocha Search API Key": "Bocha Search API-Schlüssel", "Bold": "<PERSON><PERSON>", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "Verstärkung oder Bestrafung spezifischer Token für eingeschränkte Antworten. Bias-Werte werden zwischen -100 und 100 (e<PERSON><PERSON><PERSON><PERSON><PERSON>) begrenzt. (Standard: keine)", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "Entweder müssen Docling OCR-Engine und Sprache(n) angegeben oder beide leer gelassen werden.", "Brave Search API Key": "Brave Search API-Schlüssel", "Bullet List": "Aufzählungsliste", "Button ID": "", "Button Label": "", "Button Prompt": "", "By {{name}}": "Von {{name}}", "Bypass Embedding and Retrieval": "Embedding und Retrieval umgehen", "Bypass Web Loader": "", "Cache Base Model List": "Basis Modell-Liste cachen", "Calendar": "<PERSON><PERSON><PERSON>", "Call": "Anrufen", "Call feature is not supported when using Web STT engine": "Die Anruffunktion wird nicht unterstützt, wenn die Web-STT-Engine verwendet wird.", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "Abbrechen", "Capabilities": "Fähigkeiten", "Capture": "Au<PERSON><PERSON><PERSON><PERSON>", "Capture Audio": "Audio aufzeichnen", "Certificate Path": "Zertifikatpfad", "Change Password": "Passwort ändern", "Channel Name": "Kanalname", "Channels": "<PERSON><PERSON><PERSON><PERSON>", "Character": "<PERSON><PERSON><PERSON>", "Character limit for autocomplete generation input": "Zeichenlimit für die Eingabe der automatischen Vervollständigung", "Chart new frontiers": "Neue Wege beschreiten", "Chat": "Gesprä<PERSON>", "Chat Background Image": "Hintergrundbild des Chat-Fensters", "Chat Bubble UI": "Sprechblasen-Layout", "Chat Controls": "Chat-Steuerung", "Chat direction": "Textrichtung", "Chat ID": "", "Chat Overview": "Chat-Übersicht", "Chat Permissions": "Chat-Berechtigungen", "Chat Tags Auto-Generation": "Automatische Generierung von Chat-Tags", "Chats": "Chats", "Check Again": "Erneut überprüfen", "Check for updates": "<PERSON>ch Updates suchen", "Checking for updates...": "Such<PERSON> nach Updates...", "Choose a model before saving...": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> e<PERSON> Modell, bevor <PERSON>...", "Chunk Overlap": "Blocküberlappung", "Chunk Size": "Blockgröße", "Ciphers": "Verschlüsselungen", "Citation": "Zitat", "Citations": "Zitate", "Clear memory": "Alle Erinnerungen entfernen", "Clear Memory": "Alle Erinnerungen entfernen", "click here": "hier klicken", "Click here for filter guides.": "Klicken Sie hier für Filteranleitungen.", "Click here for help.": "<PERSON>licken Sie hier für Hilfe.", "Click here to": "<PERSON><PERSON><PERSON> hier, um", "Click here to download user import template file.": "<PERSON><PERSON><PERSON> hier, um die Vorlage für den Benutzerimport herunterzuladen.", "Click here to learn more about faster-whisper and see the available models.": "<PERSON><PERSON><PERSON> Si<PERSON> hier, um mehr über faster-whisper zu erfahren und die verfügbaren Modelle zu sehen.", "Click here to see available models.": "<PERSON><PERSON><PERSON> hier, um die verfügbaren Modelle anzuzeigen.", "Click here to select": "<PERSON><PERSON><PERSON> Si<PERSON> hier zum Auswählen.", "Click here to select a csv file.": "Klicken Sie zum Auswählen einer CSV-<PERSON><PERSON> hier.", "Click here to select a py file.": "<PERSON>licken Si<PERSON> zum Auswählen einer py-Datei hier.", "Click here to upload a workflow.json file.": "<PERSON><PERSON><PERSON>, um eine workflow.json-<PERSON><PERSON> ho<PERSON>n.", "click here.": "hier klicken.", "Click on the user role button to change a user's role.": "<PERSON>licken Si<PERSON> auf die Benutzerrolle, um sie zu ändern.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Schreibberechtigung für die Zwischenablage verweigert. Bitte überprüfen Sie Ihre Browsereinstellungen, um den erforderlichen Zugriff zu erlauben.", "Clone": "Klonen", "Clone Chat": "Konversation klonen", "Clone of {{TITLE}}": "<PERSON><PERSON> {{TITLE}}", "Close": "Schließen", "Close Banner": "", "Close Configure Connection Modal": "", "Close modal": "", "Close settings modal": "", "Close Sidebar": "", "Code Block": "Codeblock", "Code execution": "Codeausführung", "Code Execution": "Codeausführung", "Code Execution Engine": "Programm zur Codeausführung", "Code Execution Timeout": "Timeout für Codeausführung", "Code formatted successfully": "Code erfolgreich formatiert", "Code Interpreter": "Code-Interpreter", "Code Interpreter Engine": "Code Interpreter-Engine", "Code Interpreter Prompt Template": "Code Interpreter Prompt Vorlage", "Collapse": "Zuklappen", "Collection": "Kollektion", "Color": "Farbe", "ComfyUI": "ComfyUI", "ComfyUI API Key": "ComfyUI-API-Schlüssel", "ComfyUI Base URL": "ComfyUI-Basis-URL", "ComfyUI Base URL is required.": "ComfyUI-Basis-URL wird <PERSON><PERSON><PERSON><PERSON>.", "ComfyUI Workflow": "ComfyUI-Workflow", "ComfyUI Workflow Nodes": "ComfyUI-Workflow-Knoten", "Command": "<PERSON><PERSON><PERSON>", "Comment": "Kommentar", "Completions": "Vervollständigungen", "Compress Images in Channels": "", "Concurrent Requests": "<PERSON><PERSON>hl gleichzeitiger Anfragen", "Configure": "Konfigurieren", "Confirm": "Bestätigen", "Confirm Password": "Passwort bestätigen", "Confirm your action": "Bestätigen Sie Ihre Aktion.", "Confirm your new password": "Neues Passwort bestätigen", "Confirm Your Password": "", "Connect to your own OpenAI compatible API endpoints.": "Verbinden Sie sich zu Ihren OpenAI-kompatiblen Endpunkten.", "Connect to your own OpenAPI compatible external tool servers.": "Verbinden Sie sich zu Ihren OpenAPI-kompatiblen Werkzeug-Servern.", "Connection failed": "Verbindung fehlgeschlagen", "Connection successful": "Verbindung erfolgreich", "Connection Type": "Verbindungstyp", "Connections": "Verbindungen", "Connections saved successfully": "Verbindungen erfolgreich gespeichert", "Connections settings updated": "Verbindungseinstellungen aktualisiert", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "Beschränkt den reasoning effort für Reasoning-Modelle. Nur anwendbar auf Reasoning-<PERSON>le von spezifischen Anbietern, die den reasoning effort Parameter unterstützen.", "Contact Admin for WebUI Access": "Kontaktieren Sie den Administrator für den Zugriff auf die Weboberfläche", "Content": "", "Content Extraction Engine": "", "Continue Response": "Antwort fortsetzen", "Continue with {{provider}}": "Mit {{provider}} fortfahren", "Continue with Email": "<PERSON><PERSON> <PERSON>", "Continue with LDAP": "Mit LDAP fortfahren", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "Kontrollieren Sie, wie Nachrichtentext für TTS-Anfragen aufgeteilt wird. 'Punctuation' teilt in Sätze auf, 'paragraphs' teilt in Abs<PERSON>ze auf und 'none' behält die Nachricht als einzelnen String.", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "Steuert die Wiederholung von Token-Sequenzen im generierten Text. Ein höherer Wert (z. B. 1,5) bestraft Wiederholungen stärker, während ein niedrigerer Wert (z. B. 1,1) toleranter ist. Bei 1 ist die Funktion deaktiviert.", "Controls": "Steuerung", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "", "Copied": "<PERSON><PERSON><PERSON>", "Copied link to clipboard": "Link in Zwischenablage kopiert", "Copied shared chat URL to clipboard!": "Freigabelink in die Zwischenablage kopiert!", "Copied to clipboard": "In die Zwischenablage kopiert", "Copy": "<PERSON><PERSON><PERSON>", "Copy Formatted Text": "Formatierten Text kopieren", "Copy last code block": "Letzten Codeblock kopieren", "Copy last response": "Letzte Antwort kopieren", "Copy link": "<PERSON>", "Copy Link": "<PERSON>", "Copy to clipboard": "In die Zwischenablage kopieren", "Copying to clipboard was successful!": "Das Kopieren in die Zwischenablage war erfolgreich!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "CORS muss vom Anbieter korrekt konfiguriert werden, um Anfragen von Open WebUI zuzulassen.", "Create": "<PERSON><PERSON><PERSON><PERSON>", "Create a knowledge base": "Wissensspeicher erstellen", "Create a model": "<PERSON><PERSON>", "Create Account": "<PERSON><PERSON> er<PERSON>", "Create Admin Account": "Administrator-Account er<PERSON><PERSON>", "Create Channel": "Ka<PERSON> erstellen", "Create Folder": "", "Create Group": "Gruppe er<PERSON>llen", "Create Knowledge": "Wissen erstellen", "Create new key": "Neuen Schlüssel erstellen", "Create new secret key": "Neuen API-Schlüssel erstellen", "Create Note": "<PERSON><PERSON> er<PERSON>", "Create your first note by clicking on the plus button below.": "<PERSON><PERSON><PERSON><PERSON> Sie Ihre erste Notiz, indem Sie unten auf die Plus-Schaltfläche klicken.", "Created at": "Erstellt am", "Created At": "Erstellt am", "Created by": "<PERSON><PERSON><PERSON><PERSON> von", "CSV Import": "CSV-Import", "Ctrl+Enter to Send": "Strg+Enter zum Senden", "Current Model": "Aktuelles Modell", "Current Password": "Aktuelles Passwort", "Custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Custom description enabled": "Benutzerdefinierte Beschreibung aktiviert", "Custom Parameter Name": "Benutzerdefinierter Parameter Name", "Custom Parameter Value": "Benutzerdefinierter Parameter Wert", "Danger Zone": "Gefahrenzone", "Dark": "<PERSON><PERSON><PERSON>", "Database": "Datenbank", "Datalab Marker API": "", "Datalab Marker API Key required.": "", "DD/MM/YYYY": "TT/MM/JJJJ", "December": "Dezember", "Default": "Standard", "Default (Open AI)": "Standard (Open AI)", "Default (SentenceTransformers)": "Standard (SentenceTransformers)", "Default action buttons will be used.": "", "Default description enabled": "Standard Beschreibung aktiviert", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "Der Standardmodus funktioniert mit einer breiteren Auswahl von Modellen, indem er Werkzeuge einmal vor der Ausführung aufruft. Der native Modus nutzt die integrierten Tool-Aufrufmöglichkeiten des Modells, er<PERSON><PERSON>, dass das Modell diese Funktion von Natur aus unterstützt.", "Default Model": "Standardmodell", "Default model updated": "Standardmodell aktualisiert", "Default Models": "Standardmodelle", "Default permissions": "Standardberechtigungen", "Default permissions updated successfully": "Standardberechtigungen erfolgreich aktualisiert", "Default Prompt Suggestions": "Prompt-Vorschläge", "Default to 389 or 636 if TLS is enabled": "Standardmä<PERSON><PERSON> auf 389 oder 636 setzen, wenn TLS aktiviert ist", "Default to ALL": "Standardmäßig auf ALLE setzen", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "Standardmäßig segmented retrieval für fokussierte und relevante Inhaltsestraktion verwenden, dies wird für die meisten Fälle empfohlen.", "Default User Role": "Standardbenutzerrolle", "Delete": "Löschen", "Delete a model": "Ein Modell löschen", "Delete All Chats": "Alle Chats löschen", "Delete All Models": "Alle Modelle löschen", "Delete chat": "<PERSON><PERSON> l<PERSON>", "Delete Chat": "<PERSON><PERSON> l<PERSON>", "Delete chat?": "Chat l<PERSON>?", "Delete folder?": "Ordner löschen?", "Delete function?": "Funktion löschen?", "Delete Message": "Nachricht löschen", "Delete message?": "Na<PERSON>richt löschen?", "Delete note?": "<PERSON><PERSON>?", "Delete prompt?": "Prompt löschen?", "delete this link": "diesen Link löschen", "Delete tool?": "Werkzeug löschen?", "Delete User": "Benutzer löschen", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} gelöscht", "Deleted {{name}}": "{{name}} <PERSON><PERSON><PERSON><PERSON>", "Deleted User": "<PERSON>utz<PERSON> gelöscht", "Deployment names are required for Azure OpenAI": "Deployment-Namen sind für Azure OpenAI erforderlich.", "Describe Pictures in Documents": "Bilder in Dokumenten beschreiben", "Describe your knowledge base and objectives": "Beschreiben Sie Ihren Wissensspeicher und Ihre Ziele", "Description": "Beschreibung", "Detect Artifacts Automatically": "Artefakte automatisch erkennen", "Dictate": "Diktieren", "Didn't fully follow instructions": "Nicht genau den Answeisungen gefolgt", "Direct": "Direkt", "Direct Connections": "Direktverbindungen", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "Direktverbindungen ermöglichen es Benutzern, sich mit ihren eigenen OpenAI-kompatiblen API-Endpunkten zu verbinden.", "Direct Tool Servers": "Direkt verbundene Werkzeug-Server", "Disable Code Interpreter": "Deaktivierte Code Interpreter", "Disable Image Extraction": "Deaktivierte Bildextraktion", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "", "Disabled": "Deaktiviert", "Discover a function": "Entdecken Sie weitere Funktionen", "Discover a model": "Entdecken Sie weitere Modelle", "Discover a prompt": "Entdecken Sie weitere Prompts", "Discover a tool": "Entdecken Sie weitere Werkzeuge", "Discover how to use Open WebUI and seek support from the community.": "<PERSON>t<PERSON>cken Si<PERSON>, wie Sie Open WebUI nutzen und Unterstützung von der Community erhalten können.", "Discover wonders": "Entdecken Si<PERSON>", "Discover, download, and explore custom functions": "Entdecken und beziehen Sie benutzerdefinierte Funktionen", "Discover, download, and explore custom prompts": "Ent<PERSON><PERSON>n und beziehen Sie benutzerdefinierte Prompts", "Discover, download, and explore custom tools": "Entdecken und beziehen Sie benutzerdefinierte Werkzeuge", "Discover, download, and explore model presets": "Entdecken und beziehen Sie benutzerdefinierte Modellvorlagen", "Display": "Anzeigen", "Display Emoji in Call": "Emojis im Anruf anzeigen", "Display Multi-model Responses in Tabs": "", "Display the username instead of You in the Chat": "<PERSON>l \"Sie\" durch Ihren Benutzernamen ersetzt werden?", "Displays citations in the response": "<PERSON><PERSON><PERSON> in der Antwort an", "Dive into knowledge": "<PERSON><PERSON> Si<PERSON> in das Wissen ein", "Do not install functions from sources you do not fully trust.": "Installieren Sie keine Funktionen aus Quellen, denen Si<PERSON> nicht vollständig vertrauen.", "Do not install tools from sources you do not fully trust.": "Installieren Sie keine Werkzeuge aus Quellen, denen Si<PERSON> nicht vollständig vertrauen.", "Docling": "<PERSON><PERSON>", "Docling Server URL required.": "Docling Server URL er<PERSON>", "Document": "Dokument", "Document Intelligence": "", "Document Intelligence endpoint and key required.": "Endpunkt und Schlüssel für document intelligence erforderlich.", "Documentation": "Dokumentation", "Documents": "Dokumente", "does not make any external connections, and your data stays securely on your locally hosted server.": "stellt keine externen Verbindungen her, und Ihre Daten bleiben sicher auf Ihrem lokal gehosteten Server.", "Domain Filter List": "Domain Filter-Liste", "Don't have an account?": "Haben Si<PERSON> noch kein Benutzerkonto?", "don't install random functions from sources you don't trust.": "installieren Sie keine Funktionen aus Quellen, denen Si<PERSON> nicht vertrauen.", "don't install random tools from sources you don't trust.": "installieren Sie keine Werkzeuge aus Quellen, denen Si<PERSON> nicht vertrauen.", "Don't like the style": "<PERSON><PERSON><PERSON><PERSON>", "Done": "<PERSON><PERSON><PERSON><PERSON>", "Download": "Exportieren", "Download as SVG": "Exportieren als SVG", "Download canceled": "Exportierung abgebrochen", "Download Database": "Datenbank exportieren", "Drag and drop a file to upload or select a file to view": "<PERSON><PERSON><PERSON> Sie eine Datei zum Hochladen oder wählen Sie eine Datei zum Anzeigen aus", "Draw": "<PERSON><PERSON><PERSON><PERSON>", "Drop any files here to upload": "<PERSON><PERSON> hierher ziehen, um sie hochzuladen", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "z. B. '30s','10m'. Gültige Zeiteinheiten sind 's', 'm', 'h'.", "e.g. \"json\" or a JSON schema": "<PERSON><PERSON> <PERSON><PERSON> \"j<PERSON>\" oder ein JSON-Schema", "e.g. 60": "z. B. 60", "e.g. A filter to remove profanity from text": "z. B<PERSON> Filter, um Schimpfwörter aus Text zu entfernen", "e.g. en": "z. B. en", "e.g. My Filter": "<PERSON><PERSON> <PERSON><PERSON>", "e.g. My Tools": "z. B. Meine Werkzeuge", "e.g. my_filter": "z<PERSON> <PERSON><PERSON> mein_filter", "e.g. my_tools": "z. B. meine_werkzeuge", "e.g. pdf, docx, txt": "z. B. pdf, docx, txt", "e.g. Tools for performing various operations": "z. B. Werkzeuge für verschiedene Operationen", "e.g., 3, 4, 5 (leave blank for default)": "z. B. 3, 4, 5 (leer lassen für Standard)", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "z. B. en-US,de-DE (freilassen für automatische Erkennung)", "e.g., westus (leave blank for eastus)": "<PERSON><PERSON> <PERSON><PERSON> westus (leer lassen für eastus)", "Edit": "<PERSON><PERSON><PERSON>", "Edit Arena Model": "Arena-<PERSON><PERSON> bear<PERSON>ten", "Edit Channel": "Kanal bearbeiten", "Edit Connection": "Verbindung bearbeiten", "Edit Default Permissions": "Standardberechtigungen bearbeiten", "Edit Folder": "Ordner bearbeiten", "Edit Memory": "Erinnerungen bearbeiten", "Edit User": "<PERSON><PERSON><PERSON> bearbeiten", "Edit User Group": "Benutzergruppe bearbeiten", "Edited": "Bearbeitet", "Editing": "Bearbei<PERSON>", "Eject": "Auswerfen", "ElevenLabs": "ElevenLabs", "Email": "E-Mail", "Embark on adventures": "Abenteuer erleben", "Embedding": "Embedding", "Embedding Batch Size": "Embedding-Stapelgröße", "Embedding Model": "Embedding-Modell", "Embedding Model Engine": "Embedding-Modell-Engine", "Embedding model set to \"{{embedding_model}}\"": "Embedding-Modell auf \"{{embedding_model}}\" gesetzt", "Enable API Key": "API-Schlüssel aktivieren", "Enable autocomplete generation for chat messages": "Automatische Vervollständigung für Chat-Nachrichten aktivieren", "Enable Code Execution": "Codeausführung aktivieren", "Enable Code Interpreter": "Code-Interpreter aktivieren", "Enable Community Sharing": "Community-Freigabe aktivieren", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Aktivieren Sie Memory Locking (mlock), um zu verhindern, dass Modelldaten aus dem RAM ausgelagert werden. Diese Option sperrt die Arbeitsseiten des Modells im RAM, um sicherzustellen, dass sie nicht auf die Festplatte ausgelagert werden. Dies kann die Leistung verbessern, indem Page Faults vermieden und ein schneller Datenzugriff sichergestellt werden.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Aktivieren Sie Memory Mapping (mmap), um Modelldaten zu laden. Diese Option ermöglicht es dem System, den Festplattenspeicher als Erweiterung des RAM zu verwenden, indem Festplattendateien so behandelt werden, als ob sie im RAM wären. Dies kann die Modellleistung verbessern, indem ein schnellerer Datenzugriff ermöglicht wird. Es kann jedoch nicht auf allen Systemen korrekt funktionieren und einen erheblichen Teil des Festplattenspeichers beanspruchen.", "Enable Message Rating": "Nachrichtenbewertung aktivieren", "Enable Mirostat sampling for controlling perplexity.": "", "Enable New Sign Ups": "Registrierung erlauben", "Enabled": "Aktiviert", "Endpoint URL": "Endpunkt-URL", "Enforce Temporary Chat": "Temporären Chat erzwingen", "Enhance": "Verbessern", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "<PERSON><PERSON><PERSON>, dass Ihre CSV-Datei 4 Spalten in dieser Reihenfolge enthält: Name, E-Mail, Passwort, Rolle.", "Enter {{role}} message here": "<PERSON><PERSON><PERSON> die {{role}}-<PERSON><PERSON><PERSON><PERSON> hier ein", "Enter a detail about yourself for your LLMs to recall": "<PERSON><PERSON><PERSON> Si<PERSON> ein Detail über sich selbst ein, das Ihre Sprachmodelle (LLMs) sich merken sollen", "Enter a title for the pending user info overlay. Leave empty for default.": "Geben Si<PERSON> einen Titel für das Overlay 'Ausstehende Kontoaktivierung' ein. <PERSON><PERSON> lassen für Standard.", "Enter a watermark for the response. Leave empty for none.": "<PERSON><PERSON>en Si<PERSON> ein Wasserzeichen für die Antwort ein. <PERSON>ür keines leer lassen.", "Enter api auth string (e.g. username:password)": "Geben Sie die API-Authentifizierungszeichenfolge ein (z. B. Benutzername:Passwort)", "Enter Application DN": "Geb<PERSON> Sie die Anwendungs-DN ein", "Enter Application DN Password": "Geben Sie das Anwendungs-DN-Passwort ein", "Enter Bing Search V7 Endpoint": "<PERSON><PERSON><PERSON> Sie den Bing Search V7-<PERSON>punkt ein", "Enter Bing Search V7 Subscription Key": "<PERSON>eben Sie den Bing Search V7-Abonnement-Schl<PERSON><PERSON> ein", "Enter Bocha Search API Key": "Geben Sie den Bocha Search API-Schlüssel ein", "Enter Brave Search API Key": "Geben Sie den Brave Search API-Schlüssel ein", "Enter certificate path": "Geben Sie den Zertifikatpfad ein", "Enter CFG Scale (e.g. 7.0)": "Geben Sie die CFG-Skala ein (z. B. 7.0)", "Enter Chunk Overlap": "<PERSON><PERSON><PERSON> Si<PERSON> die Blocküberlappung ein", "Enter Chunk Size": "<PERSON><PERSON><PERSON> Si<PERSON> die Blockgröße ein", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "<PERSON><PERSON><PERSON> Si<PERSON> kommagetrennte \"token:bias_value\"-<PERSON><PERSON> e<PERSON> (Beispiel: 5432:100, 413:-100)", "Enter Config in JSON format": "Konfiguration in JSON Format eingeben", "Enter content for the pending user info overlay. Leave empty for default.": "Geben Sie Inhalt für das Overlay 'Ausstehende Kontoaktivierung' ein. Für Standard leer lassen.", "Enter Datalab Marker API Base URL": "", "Enter Datalab Marker API Key": "", "Enter description": "Geben Sie eine Beschreibung ein", "Enter Docling OCR Engine": "Docling OCR-Engine eingeben", "Enter Docling OCR Language(s)": "Docling OCR-Sprache(n) eingeben", "Enter Docling Server URL": "Docling Server-URL eingeben", "Enter Document Intelligence Endpoint": "ndpunkt für Dokumentenintelligenz eingeben", "Enter Document Intelligence Key": "Schlüssel für Dokumentenintelligenz eingeben", "Enter domains separated by commas (e.g., example.com,site.org)": "Geben Sie die Domains durch Kommas separiert ein (z. B. example.com,site.org)", "Enter Exa API Key": "Geben Sie den Exa-API-Schlüssel ein", "Enter External Document Loader API Key": "API-Schlüssel für externen Dokumenten-Loader eingeben", "Enter External Document Loader URL": "URL für externen Dokumenten-Loader eingeben", "Enter External Web Loader API Key": "API-Schlüssel für externen Web-Loader eingeben", "Enter External Web Loader URL": "URL für externen Web-Loader eingeben", "Enter External Web Search API Key": "API-Schlüssel für externe Websuche eingeben", "Enter External Web Search URL": "URL für externe Websuche eingeben", "Enter Firecrawl API Base URL": "<PERSON>eb<PERSON> Sie die Firecrawl Basis-URL ein", "Enter Firecrawl API Key": "Geben Sie den Firecrawl API-Schlüssel ein", "Enter folder name": "Ordnernamen eingeben", "Enter Github Raw URL": "<PERSON><PERSON><PERSON> Sie die Github Raw-URL ein", "Enter Google PSE API Key": "Geben Sie den Google PSE-API-Schlüssel ein", "Enter Google PSE Engine Id": "<PERSON>eben Sie die Google PSE-Engine-ID ein", "Enter Image Size (e.g. 512x512)": "<PERSON>eben Sie die Bildgröße ein (z. B. 512x512)", "Enter Jina API Key": "Geben Sie den Jina-API-Schl<PERSON>ssel ein", "Enter JSON config (e.g., {\"disable_links\": true})": "", "Enter Jupyter Password": "Geben Sie das Jupyter-Passwort ein", "Enter Jupyter Token": "<PERSON><PERSON><PERSON> Si<PERSON> den Jupyter-Token ein", "Enter Jupyter URL": "<PERSON><PERSON><PERSON> Sie die Jupyter-URL ein", "Enter Kagi Search API Key": "Geben Sie den Kagi Search API-Schlüssel ein", "Enter Key Behavior": "<PERSON><PERSON><PERSON><PERSON> von '<PERSON>'", "Enter language codes": "Geben Sie die Sprachcodes ein", "Enter Mistral API Key": "Geben Sie den Mistral API-Schlüssel ein", "Enter Model ID": "<PERSON><PERSON><PERSON> Sie die Modell-ID ein", "Enter model tag (e.g. {{modelTag}})": "<PERSON><PERSON><PERSON> Sie den Model-Tag ein", "Enter Mojeek Search API Key": "Geben Sie den Mojeek Search API-Schlüssel ein", "Enter name": "Name e<PERSON>ben", "Enter New Password": "Neues Passwort eingeben", "Enter Number of Steps (e.g. 50)": "<PERSON>eb<PERSON> Sie die Anzahl an Schritten ein (z. B. 50)", "Enter Perplexity API Key": "Geben Sie den Perplexity API-Schlüssel ein", "Enter Playwright Timeout": "Playwright <PERSON><PERSON> e<PERSON><PERSON>", "Enter Playwright WebSocket URL": "<PERSON><PERSON><PERSON> Sie die Playwright WebSocket-URL ein", "Enter proxy URL (e.g. **************************:port)": "<PERSON><PERSON><PERSON> sie die Proxy-URL ein (z. B. **************************:port)", "Enter reasoning effort": "Geben Sie den Schlussfolgerungsaufwand ein", "Enter Sampler (e.g. Euler a)": "<PERSON>eb<PERSON> Sie den Sampler ein (z. B. Euler a)", "Enter Scheduler (e.g. Karras)": "<PERSON><PERSON><PERSON> e<PERSON> (z. B. <PERSON>)", "Enter Score": "<PERSON><PERSON><PERSON>", "Enter SearchApi API Key": "Geben Sie den SearchApi-API-Schlüssel ein", "Enter SearchApi Engine": "<PERSON><PERSON><PERSON> Sie die SearchApi-Engine ein", "Enter Searxng Query URL": "Geben Sie die Searxng-Abfrage-URL ein", "Enter Seed": "<PERSON><PERSON><PERSON> Si<PERSON> den Seed ein", "Enter SerpApi API Key": "Geben Sie den SerpApi API-Schlüssel ein", "Enter SerpApi Engine": "SerpApi-Engine eingeben", "Enter Serper API Key": "Geben Sie den Serper-API-Schlüssel ein", "Enter Serply API Key": "Geben Sie den Serply API-Schlüssel ein", "Enter Serpstack API Key": "Geben Sie den Serpstack-API-Schlüssel ein", "Enter server host": "<PERSON><PERSON><PERSON>-Host ein", "Enter server label": "<PERSON><PERSON><PERSON> Sie das Server-Label ein", "Enter server port": "<PERSON>eben Sie den Server-Port ein", "Enter Sougou Search API sID": "Sougou Search API sID eingeben", "Enter Sougou Search API SK": "Sougou Search API SK eingeben", "Enter stop sequence": "Stop-Sequenz eingeben", "Enter system prompt": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>", "Enter system prompt here": "<PERSON><PERSON><PERSON><PERSON> hier e<PERSON>ben", "Enter Tavily API Key": "Geben Sie den Tavily-API-Schlüssel ein", "Enter Tavily Extract Depth": "Tavily Extract De<PERSON>h eingeben", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "G<PERSON>en sie die öffentliche URL Ihrer WebUI ein. Diese URL wird verwendet, um Links in den Benachrichtigungen zu generieren.", "Enter the URL of the function to import": "Geben Sie die URL der Funktion ein, die importiert werden soll", "Enter the URL to import": "URL für den Import eingeben", "Enter Tika Server URL": "<PERSON><PERSON><PERSON> die Tika-Server-URL ein", "Enter timeout in seconds": "Geben Sie den Timeout in Sekunden ein", "Enter to Send": "'Enter' zum Senden", "Enter Top K": "<PERSON><PERSON><PERSON> Sie Top K ein", "Enter Top K Reranker": "Geben Sie Top K für Reranker ein", "Enter URL (e.g. http://127.0.0.1:7860/)": "<PERSON><PERSON><PERSON> Sie die URL ein (z. B. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "<PERSON>eb<PERSON> Sie die URL ein (z. B. http://localhost:11434)", "Enter Yacy Password": "Yacy-Passwort eingeben", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "Ya<PERSON>-<PERSON><PERSON> e<PERSON> (z. B. http://yacy.example.com:8090 )", "Enter Yacy Username": "Yacy-Benutzernamen eingeben", "Enter your current password": "Geben Sie Ihr aktuelles Passwort ein", "Enter Your Email": "Geben Sie Ihre E-Mail-Adresse ein", "Enter Your Full Name": "Geben Sie Ihren vollständigen Namen ein", "Enter your message": "<PERSON><PERSON><PERSON> Si<PERSON> Ihre Nachricht ein", "Enter your name": "<PERSON><PERSON><PERSON> Si<PERSON> Ihren Namen ein", "Enter Your Name": "<PERSON><PERSON><PERSON> Si<PERSON> Ihren Namen ein", "Enter your new password": "<PERSON><PERSON><PERSON> Sie Ihr neues Passwort ein", "Enter Your Password": "Geben Sie Ihr Passwort ein", "Enter Your Role": "<PERSON><PERSON><PERSON> Si<PERSON> Ihre Rolle ein", "Enter Your Username": "<PERSON><PERSON>en Sie Ihren Benutzernamen ein", "Enter your webhook URL": "<PERSON><PERSON><PERSON> Sie Ihre Webhook-URL ein", "Error": "<PERSON><PERSON>", "ERROR": "FEHLER", "Error accessing Google Drive: {{error}}": "<PERSON><PERSON> beim <PERSON> auf Google Drive: {{error}}", "Error accessing media devices.": "Fehler beim Zugreifen auf Mediengeräte", "Error starting recording.": "Fehler beim Starten der Aufnahme", "Error unloading model: {{error}}": "", "Error uploading file: {{error}}": "<PERSON><PERSON> beim Ho<PERSON>laden der Datei: {{error}}", "Evaluations": "<PERSON><PERSON>", "Everyone": "", "Exa API Key": "Exa-API-Schlüssel", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Beispiel: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Beispiel: ALL", "Example: mail": "Beispiel: mail", "Example: ou=users,dc=foo,dc=example": "Beispiel: ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "Beispiel: sAMAccountName or uid or userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "Die Anzahl der Nutzer in Ihrer Lizenz wurde überschritten. Bitte kontaktieren Sie den Support, um die Anzahl der Plätze zu erhöhen.", "Exclude": "Ausschließen", "Execute code for analysis": "Code für Analyse ausführen", "Executing **{{NAME}}**...": "**{{NAME}}** wird ausgeführt...", "Expand": "Aufklappen", "Experimental": "Experimentell", "Explain": "Erklären", "Explore the cosmos": "Erforschen Sie das Universum", "Export": "Exportieren", "Export All Archived Chats": "Alle archivierten Chats exportieren", "Export All Chats (All Users)": "Alle Chats exportieren (alle Benutzer)", "Export chat (.json)": "Chat exportieren (.json)", "Export Chats": "Chats exportieren", "Export Config to JSON File": "Exportiere Konfiguration als JSON-Datei", "Export Functions": "Funktionen exportieren", "Export Models": "Modelle exportieren", "Export Presets": "Voreinstellungen exportieren", "Export Prompt Suggestions": "Prompt Vorschläge exportieren", "Export Prompts": "Prompts exportieren", "Export to CSV": "Als CSV exportieren", "Export Tools": "Werkzeuge exportieren", "Export Users": "", "External": "Extern", "External Document Loader URL required.": "URL für externen Dokumenten-Loader erforderlich.", "External Task Model": "Extern<PERSON> Aufgabenmodell", "External Web Loader API Key": "Externer Web-Loader API-Schlüssel", "External Web Loader URL": "Externer Web-Loader URL", "External Web Search API Key": "Externe Websuche API-Schlüssel", "External Web Search URL": "Externe Websuche URL", "Fade Effect for Streaming Text": "", "Failed to add file.": "Fehler beim Hinzufügen der Datei.", "Failed to connect to {{URL}} OpenAPI tool server": "Verbindung zum OpenAPI-Toolserver {{URL}} fehlgeschlagen", "Failed to copy link": "Fehler beim kopieren des Links", "Failed to create API Key.": "Fehler beim Erstellen des API-Schlüssels.", "Failed to delete note": "Notiz konnte nicht gelöscht werden", "Failed to extract content from the file: {{error}}": "<PERSON><PERSON> beim extrahieren des Inhalts aus der Datei: {{error}}", "Failed to extract content from the file.": "Fehler beim extrahieren des Inhalts aus der Datei.", "Failed to fetch models": "Fehler beim Abrufen der Modelle", "Failed to generate title": "Fehler beim generieren des Titels", "Failed to load chat preview": "", "Failed to load file content.": "Fehler beim Laden des Dateiinhalts.", "Failed to read clipboard contents": "Fehler beim Lesen des Inhalts der Zwischenablage.", "Failed to save connections": "Verbindungen konnten nicht gespeichert werden", "Failed to save models configuration": "Fehler beim Speichern der Modellkonfiguration", "Failed to update settings": "Fehler beim Aktualisieren der Einstellungen", "Failed to upload file.": "Fehler beim Hochladen der Datei.", "Features": "Funktionalitäten", "Features Permissions": "Funktionen-Berechtigungen", "February": "<PERSON><PERSON><PERSON>", "Feedback Details": "", "Feedback History": "Fe<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "Feedbacks": "Feedbacks", "Feel free to add specific details": "Fühlen Si<PERSON> sich frei, spezifische Details hinzuzufügen", "File": "<PERSON><PERSON>", "File added successfully.": "<PERSON>i erfolgreich hinzugefügt.", "File content updated successfully.": "Dateiinhalt erfolgreich aktualisiert.", "File Mode": "Datei-Modus", "File not found.": "Datei nicht gefunden.", "File removed successfully.": "Datei erfolgreich entfernt.", "File size should not exceed {{maxSize}} MB.": "<PERSON>i darf nicht größer als {{maxSize}} MB sein.", "File Upload": "Dateiupload", "File uploaded successfully": "Datei erfolgreich hoch<PERSON>aden", "Files": "<PERSON><PERSON>", "Filter": "Filter", "Filter is now globally disabled": "Filter ist jetzt global deaktiviert", "Filter is now globally enabled": "Filter ist jetzt global aktiviert", "Filters": "Filter", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Fingerabdruck-Spoofing erkannt: Initialen können nicht als Avatar verwendet werden. Standard-Avatar wird verwendet.", "Firecrawl API Base URL": "Firecrawl API Basis-URL", "Firecrawl API Key": "Firecrawl API-Schlüssel", "Floating Quick Actions": "", "Focus chat input": "Chat-Eingabe fokussieren", "Folder deleted successfully": "Ordner erfolgreich <PERSON>t", "Folder Name": "Ordner-Name", "Folder name cannot be empty.": "Ordnername darf nicht leer sein.", "Folder name updated successfully": "Ordnername erfolgreich aktualisiert", "Folder updated successfully": "Ordner erfolgreich aktualisiert", "Follow up": "Folgefragen", "Follow Up Generation": "Folgefragen Generierung", "Follow Up Generation Prompt": "Prompt für Folgefragen Generierung", "Follow-Up Auto-Generation": "Auto-Generierung für Folgefragen", "Followed instructions perfectly": "Anweisungen perfekt befolgt", "Force OCR": "OCR erzwingen", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "Neue Wege beschreiten", "Form": "Formular", "Format Lines": "", "Format the lines in the output. Defaults to False. If set to True, the lines will be formatted to detect inline math and styles.": "", "Format your variables using brackets like this:": "Formatieren Sie Ihre Variablen mit Klammern, wie hier:", "Forwards system user session credentials to authenticate": "Leitet Anmeldedaten der user session zur Authentifizierung weiter", "Full Context Mode": "Voll-Kontext Modus", "Function": "Funktion", "Function Calling": "Funktionsaufruf", "Function created successfully": "Funktion erfolgreich erstellt", "Function deleted successfully": "Funktion erfolgreich <PERSON>", "Function Description": "Funktionsbeschreibung", "Function ID": "Funktions-ID", "Function imported successfully": "Funktion erfolgreich importiert", "Function is now globally disabled": "Die Funktion ist jetzt global deaktiviert", "Function is now globally enabled": "Die Funktion ist jetzt global aktiviert", "Function Name": "Funktionsname", "Function updated successfully": "Funktion erfolgreich aktualisiert", "Functions": "Funktionen", "Functions allow arbitrary code execution.": "Funktionen ermöglichen die Ausführung beliebigen Codes.", "Functions imported successfully": "Funktionen erfolgreich importiert", "Gemini": "Gemini", "Gemini API Config": "Gemini API-Konfiguration", "Gemini API Key is required.": "Gemini API-Schlü<PERSON> ist erforderlich.", "General": "Allgemein", "Generate": "<PERSON><PERSON><PERSON>", "Generate an image": "<PERSON>ild er<PERSON>", "Generate Image": "<PERSON>ild er<PERSON>", "Generate prompt pair": "Prompt-<PERSON><PERSON>", "Generating search query": "Suchan<PERSON>ge wird erstellt", "Generating...": "Generiere...", "Get information on {{name}} in the UI": "", "Get started": "Loslegen", "Get started with {{WEBUI_NAME}}": "Loslegen mit {{WEBUI_NAME}}", "Global": "Global", "Good Response": "Gute Antwort", "Google Drive": "Google Drive", "Google PSE API Key": "Google PSE-API-Schlüssel", "Google PSE Engine Id": "Google PSE-Engine-ID", "Group created successfully": "Gruppe erfolgreich erstellt", "Group deleted successfully": "Gruppe erfolgreich <PERSON>", "Group Description": "Gruppenbeschreibung", "Group Name": "Gruppenname", "Group updated successfully": "Gruppe erfolgreich aktualisiert", "Groups": "Gruppen", "H1": "", "H2": "", "H3": "", "Haptic Feedback": "Haptisches Feedback", "Hello, {{name}}": "<PERSON><PERSON>, {{name}}", "Help": "<PERSON><PERSON><PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "<PERSON><PERSON><PERSON>, die beste Community-Bestenliste zu erstellen, indem Sie Ihren Feedback-<PERSON><PERSON><PERSON><PERSON> te<PERSON>!", "Hex Color": "Hex-Farbe", "Hex Color - Leave empty for default color": "Hex-Farbe - <PERSON><PERSON> lassen für Standardfarbe", "Hide": "Verbergen", "Hide from Sidebar": "Von Seitenleiste entfernen", "Hide Model": "<PERSON><PERSON>", "High Contrast Mode": "Modus für hohen Kontrast", "Home": "Startseite", "Host": "Host", "How can I help you today?": "Wie kann ich Ihnen heute helfen?", "How would you rate this response?": "Wie würden Sie diese Antwort bewerten?", "HTML": "HTML", "Hybrid Search": "<PERSON><PERSON>", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "<PERSON>ch bestätige, dass ich gelesen habe und die Auswirkungen meiner Aktion verstehe. Mir sind die Risiken bewusst, die mit der Ausführung beliebigen Codes verbunden sind, und ich habe die Vertrauenswürdigkeit der Quelle überprüft.", "ID": "ID", "iframe Sandbox Allow Forms": "iFrame-Sandbox: <PERSON><PERSON> er<PERSON>", "iframe Sandbox Allow Same Origin": "iFrame-Sandbox: Gleichen Origin erlauben", "Ignite curiosity": "Neugier entfachen", "Image": "Bild", "Image Compression": "Bildkomprimierung", "Image Compression Height": "Bildkompression Länge", "Image Compression Width": "Bildkompression Breite", "Image Generation": "Bildgenerierung", "Image Generation (Experimental)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (experimentell)", "Image Generation Engine": "Bildgenerierungs-Engine", "Image Max Compression Size": "Maximale Bildkomprimierungsgröße", "Image Max Compression Size height": "Bildkompression maximale Länge", "Image Max Compression Size width": "Bildkompression maximale Breite", "Image Prompt Generation": "Bild-Prompt-Generierung", "Image Prompt Generation Prompt": "Prompt für die Bild-Prompt-Generierung", "Image Settings": "Bildeinstellungen", "Images": "Bilder", "Import": "Importieren", "Import Chats": "Chats importieren", "Import Config from JSON File": "Konfiguration aus JSON-Datei importieren", "Import From Link": "<PERSON> importieren", "Import Functions": "Funktionen importieren", "Import Models": "Modelle importieren", "Import Notes": "Notizen importieren", "Import Presets": "Voreinstellungen importieren", "Import Prompt Suggestions": "Prompt Vorschläge importieren", "Import Prompts": "Prompts importieren", "Import Tools": "Werkzeuge importieren", "Include": "Einschließen", "Include `--api-auth` flag when running stable-diffusion-webui": "<PERSON><PERSON>gen Sie beim Ausführen von stable-diffusion-webui die Option `--api-auth` hinzu", "Include `--api` flag when running stable-diffusion-webui": "<PERSON><PERSON>gen Sie beim Ausführen von stable-diffusion-webui die Option `--api` hinzu", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, wie schnell der Algorithmus auf Feedback aus dem generierten Text reagiert. Eine niedrigere Lernrate führt zu langsameren Anpassungen, während eine höhere Lernrate den Algorithmus reaktionsschneller macht.", "Info": "Info", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "Gesamten Inhalt als Kontext für umfassende Verarbeitung einfügen, dies wird für komplexe Abfragen empfohlen.", "Input": "", "Input commands": "Eingabebefehle", "Input Variables": "Eingabe Variablen", "Insert": "Einfügen", "Insert Follow-Up Prompt to Input": "", "Insert Prompt as Rich Text": "Prompt als Rich Text einfügen", "Install from Github URL": "<PERSON>-URL installieren", "Instant Auto-Send After Voice Transcription": "Spracherkennung direkt absenden", "Integration": "Integration", "Interface": "Oberfläche", "Invalid file content": "Ungültiger <PERSON>t", "Invalid file format.": "Ungültiges Dateiformat.", "Invalid JSON file": "Ungültige JSON Datei", "Invalid JSON format in Additional Config": "", "Invalid Tag": "Ungültiger Tag", "is typing...": "schreibt ...", "Italic": "<PERSON><PERSON><PERSON>", "January": "<PERSON><PERSON><PERSON>", "Jina API Key": "Jina-API-Schlüssel", "join our Discord for help.": "Treten Si<PERSON> unserem Discord bei, um Hilfe zu erhalten.", "JSON": "JSON", "JSON Preview": "JSON-Vorschau", "July": "<PERSON><PERSON>", "June": "<PERSON><PERSON>", "Jupyter Auth": "Jupyter-Authentifizierung", "Jupyter URL": "<PERSON><PERSON><PERSON>-URL", "JWT Expiration": "JWT-A<PERSON>uf", "JWT Token": "JWT-Token", "Kagi Search API Key": "Kagi Search API-Schlüssel", "Keep Follow-Up Prompts in Chat": "", "Keep in Sidebar": "In Seitenleiste anzeigen", "Key": "Schlüssel", "Keyboard shortcuts": "Tastenkombinationen", "Knowledge": "Wissen", "Knowledge Access": "Wissenszugriff", "Knowledge Base": "Wissensdatenbank", "Knowledge created successfully.": "Wissen erfolgreich erstellt.", "Knowledge deleted successfully.": "Wissen erfolgreich gelöscht.", "Knowledge Public Sharing": "Öffentliche Freigabe von W<PERSON>n", "Knowledge reset successfully.": "Wissen erfolgreich zurückgesetzt.", "Knowledge updated successfully": "Wissen erfolgreich aktualisiert", "Kokoro.js (Browser)": "", "Kokoro.js Dtype": "", "Label": "Label", "Landing Page Mode": "Startseitenmodus", "Language": "<PERSON><PERSON><PERSON>", "Language Locales": "", "Last Active": "Zuletzt aktiv", "Last Modified": "Zuletzt bearbeitet", "Last reply": "Letzte Antwort", "LDAP": "LDAP", "LDAP server updated": "LDAP-Server aktualisiert", "Leaderboard": "Bestenlist<PERSON>", "Learn More": "", "Learn more about OpenAPI tool servers.": "Erfahren Sie mehr über OpenAPI-Toolserver.", "Leave empty for no compression": "<PERSON><PERSON> lassen für keine Kompression", "Leave empty for unlimited": "<PERSON><PERSON> lassen für unbegrenzt", "Leave empty to include all models from \"{{url}}\" endpoint": "<PERSON><PERSON>, um alle Modelle vom Endpunkt \"{{url}}\" einzuschließen", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "<PERSON><PERSON>, um alle Modelle vom Endpunkt \"{{url}}/api/tags\" einzuschließen", "Leave empty to include all models from \"{{url}}/models\" endpoint": "<PERSON><PERSON>, um alle Modelle vom Endpunkt \"{{url}}/models\" einzuschließen", "Leave empty to include all models or select specific models": "<PERSON><PERSON>, um alle Modelle einzuschließen oder spezifische Modelle auszuwählen", "Leave empty to use the default prompt, or enter a custom prompt": "<PERSON><PERSON>, um den Standardprompt zu verwenden, oder geben Sie einen benutzerdefinierten Prompt ein", "Leave model field empty to use the default model.": "<PERSON><PERSON>, um das Standardmodell zu verwenden.", "lexical": "", "License": "<PERSON><PERSON><PERSON>", "Lift List": "", "Light": "Hell", "Listening...": "<PERSON><PERSON><PERSON> zu...", "Llama.cpp": "Llama.cpp", "LLMs can make mistakes. Verify important information.": "LLMs können Fehler machen. Überprüfen Sie wichtige Informationen.", "Loader": "", "Loading Kokoro.js...": "Lade Kokoro.js...", "Local": "<PERSON><PERSON>", "Local Task Model": "Lokales Aufgabenmodell", "Location access not allowed": "Standortzugriff nicht erlaubt", "Lost": "Verloren", "LTR": "LTR", "Made by Open WebUI Community": "<PERSON> der OpenWebUI-Community", "Make password visible in the user interface": "Passwort im Benutzerinterface sichtbar machen", "Make sure to enclose them with": "Umschließen Sie Variablen mit", "Make sure to export a workflow.json file as API format from ComfyUI.": "<PERSON><PERSON><PERSON>, dass sie eine workflow.json-Datei im API-Format von ComfyUI exportieren.", "Manage": "<PERSON><PERSON><PERSON><PERSON>", "Manage Direct Connections": "Direkte Verbindungen verwalten", "Manage Models": "<PERSON><PERSON> ver<PERSON><PERSON>", "Manage Ollama": "<PERSON><PERSON><PERSON> verwalten", "Manage Ollama API Connections": "Ollama-API-Verbindungen verwalten", "Manage OpenAI API Connections": "OpenAI-API-Verbindungen verwalten", "Manage Pipelines": "Pipelines verwalten", "Manage Tool Servers": "Tool Server verwalten", "March": "<PERSON><PERSON><PERSON>", "Markdown": "<PERSON><PERSON>", "Markdown (Header)": "", "Max Speakers": "", "Max Upload Count": "Maximale Anzahl der Uploads", "Max Upload Size": "Maximale Uploadgröße", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Es können maximal 3 Modelle gleichzeitig heruntergeladen werden. Bitte versuchen Sie es später erneut.", "May": "<PERSON>", "Memories accessible by LLMs will be shown here.": "Erinnerungen, die für Modelle zugänglich sind, werden hier angezeigt.", "Memory": "Erinnerungen", "Memory added successfully": "Erinnerung erfolgreich hinzugefügt", "Memory cleared successfully": "Erinnerung erfolgreich gelöscht", "Memory deleted successfully": "Erinnerung erfolgreich gelöscht", "Memory updated successfully": "Erinnerung erfolgreich aktualisiert", "Merge Responses": "Antworten zusammenführen", "Merged Response": "Zusammengeführte Antwort", "Message rating should be enabled to use this feature": "Antwortbewertung muss aktiviert sein, um diese Funktion zu verwenden", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "<PERSON><PERSON><PERSON><PERSON>, die Sie nach der Erstellung Ihres Links senden, werden nicht geteilt. Nutzer mit der URL können den freigegebenen Chat einsehen.", "Microsoft OneDrive": "", "Microsoft OneDrive (personal)": "Microsoft OneDrive (<PERSON><PERSON><PERSON><PERSON>lich)", "Microsoft OneDrive (work/school)": "Microsoft OneDrive (Arbeit/Schule)", "Mistral OCR": "", "Mistral OCR API Key required.": "Mistral OCR API-Sc<PERSON><PERSON><PERSON> erforderlich.", "Model": "<PERSON><PERSON>", "Model '{{modelName}}' has been successfully downloaded.": "Modell '{{modelName}}' wurde erfolgreich heruntergeladen.", "Model '{{modelTag}}' is already in queue for downloading.": "Modell '{{modelTag}}' befindet sich bereits in der Warteschlange zum Herunterladen.", "Model {{modelId}} not found": "Modell {{modelId}} nicht gefunden", "Model {{modelName}} is not vision capable": "Das Modell {{modelName}} ist nicht für die Bildverarbeitung geeignet", "Model {{name}} is now {{status}}": "Modell {{name}} ist jetzt {{status}}", "Model {{name}} is now hidden": "Modell {{name}} ist jetzt versteckt.", "Model {{name}} is now visible": "Modell {{name}} ist jetzt sichtbar.", "Model accepts file inputs": "<PERSON><PERSON> a<PERSON><PERSON><PERSON><PERSON><PERSON>", "Model accepts image inputs": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Model can execute code and perform calculations": "Modell kann Code ausführen und Berechnungen durchführen", "Model can generate images based on text prompts": "<PERSON>l kann Bilder basierend auf Text-Prompts generieren", "Model can search the web for information": "<PERSON>l kann das Web nach Informationen durchsuchen", "Model created successfully!": "Modell erfolgreich erstellt!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Modell-Dateisystempfad erkannt. Modellkurzname ist für das Update erforderlich, Fortsetzung nicht möglich.", "Model Filtering": "Modellfilterung", "Model ID": "Modell-ID", "Model ID is required.": "", "Model IDs": "Modell-IDs", "Model Name": "Modell-Name", "Model Name is required.": "", "Model not selected": "Modell nicht ausgewählt", "Model Params": "Modell-Parameter", "Model Permissions": "Modellberechtigungen", "Model unloaded successfully": "", "Model updated successfully": "Modell erfolgreich aktualisiert", "Model(s) do not support file upload": "Modell(e) unterstützen keinen Dateiupload", "Modelfile Content": "Modelfile-Inhalt", "Models": "<PERSON><PERSON>", "Models Access": "Modell-<PERSON><PERSON><PERSON>", "Models configuration saved successfully": "Modellkonfiguration erfolgreich gespeichert", "Models Public Sharing": "Öffentliche Freigabe von Modellen", "Mojeek Search API Key": "Mojeek Search API-Schlüssel", "more": "mehr", "More": "<PERSON><PERSON>", "More Concise": "", "More Options": "", "Name": "Name", "Name your knowledge base": "Benennen Sie Ihren Wissensspeicher", "Native": "Nativ", "New Button": "", "New Chat": "<PERSON><PERSON><PERSON>", "New Folder": "<PERSON><PERSON><PERSON> Ordner", "New Function": "Neue Funktion", "New Note": "Neue Notiz", "New Password": "Neues Passwort", "New Tool": "Neues Werkzeug", "new-channel": "neuer-kanal", "Next message": "Nächste Nachricht", "No chats found": "", "No chats found for this user.": "<PERSON><PERSON> Chats für diesen Nutzer gefunden.", "No chats found.": "<PERSON><PERSON> gefunden.", "No content": "<PERSON><PERSON>", "No content found": "<PERSON><PERSON>halt gefunden", "No content found in file.": "<PERSON><PERSON> in Datei gefunden.", "No content to speak": "<PERSON>in Inhalt zum Vorlesen", "No distance available": "<PERSON><PERSON>", "No feedbacks found": "<PERSON><PERSON> gefunden", "No file selected": "<PERSON><PERSON> ausgewählt", "No groups with access, add a group to grant access": "<PERSON>ine Gruppen mit Zugriff, fügen Sie eine Gruppe hinzu, um Zugriff zu gewähren", "No HTML, CSS, or JavaScript content found.": "<PERSON><PERSON>-, CSS- oder JavaScript-Inhalte gefunden.", "No inference engine with management support found": "<PERSON><PERSON> Inferenz-Engine mit Management-Unterstützung gefunden", "No knowledge found": "<PERSON><PERSON>n gefunden", "No memories to clear": "Keine Erinnerungen zum Entfernen", "No model IDs": "<PERSON><PERSON>-IDs", "No models found": "<PERSON><PERSON> gefunden", "No models selected": "<PERSON><PERSON>le ausgewählt", "No Notes": "<PERSON><PERSON>", "No results found": "<PERSON><PERSON> gefunden", "No search query generated": "<PERSON><PERSON>", "No source available": "<PERSON><PERSON>", "No users were found.": "<PERSON><PERSON> gefunden.", "No valves to update": "<PERSON><PERSON> Val<PERSON> zum Aktualisieren", "None": "<PERSON><PERSON><PERSON>", "Not factually correct": "<PERSON>cht sachlich korrekt", "Not helpful": "<PERSON><PERSON> hilfreich", "Note deleted successfully": "Notiz erfolgreich <PERSON>", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Hinweis: <PERSON><PERSON><PERSON> eine Mindestpunktzahl festlegen, werden in der Suche nur Dokumente mit einer Punktzahl größer oder gleich der Mindestpunktzahl zurückgegeben.", "Notes": "Notizen", "Notification Sound": "Benachrichtigungston", "Notification Webhook": "Benachrichtigungs-Webhook", "Notifications": "Benachrichtigungen", "November": "November", "OAuth ID": "OAuth-ID", "October": "Oktober", "Off": "Aus", "Okay, Let's Go!": "Okay, los geht's!", "OLED Dark": "OLED-Dunkel", "Ollama": "Ollama", "Ollama API": "Ollama-API", "Ollama API settings updated": "Ollama-API-Einstellungen aktualisiert", "Ollama Version": "Ollama-Version", "On": "Ein", "OneDrive": "OneDrive", "Only active when \"Paste Large Text as File\" setting is toggled on.": "Nur aktiv, wenn die \"Großen Text als Datei einfügen\" Option aktiviert ist.", "Only active when the chat input is in focus and an LLM is generating a response.": "<PERSON>ur aktiv, wenn die Eingabe im Chat ausgewählt ist und ein LLM gerade eine Antwort generiert.", "Only alphanumeric characters and hyphens are allowed": "Nur alphanumerische Zeichen und Bindestriche sind erlaubt", "Only alphanumeric characters and hyphens are allowed in the command string.": "In der Befehlszeichenfolge sind nur alphanumerische Zeichen und Bindestriche erlaubt.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "Nur Sammlungen können bearbeitet werden. Erstellen Si<PERSON> eine neue Wissensbasis, um Dokumente zu bearbeiten/hinzuzufügen.", "Only markdown files are allowed": "<PERSON><PERSON>-<PERSON><PERSON> sind erlaubt", "Only select users and groups with permission can access": "Nur ausgewählte Benutzer und Gruppen mit Berechtigung können darauf zugreifen", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Hoppla! E<PERSON> scheint, dass die URL ungültig ist. Bitte überprüfen Sie diese und versuchen Si<PERSON> es erneut.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Hoppla! Es werden noch Dateien hochgeladen. <PERSON>te warten Si<PERSON>, bis der Upload abgeschlossen ist.", "Oops! There was an error in the previous response.": "Hoppla! Es gab einen <PERSON>hler in der vorherigen Antwort.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Hoppla! Sie verwenden eine nicht unterstützte Methode (nur Frontend). Bitte stellen Sie die WebUI vom Backend bereit.", "Open file": "<PERSON><PERSON>", "Open in full screen": "<PERSON><PERSON> Vollbild<PERSON>", "Open modal to configure connection": "", "Open Modal To Manage Floating Quick Actions": "", "Open new chat": "Neuen Chat <PERSON>", "Open Sidebar": "", "Open User Profile Menu": "", "Open WebUI can use tools provided by any OpenAPI server.": "Open WebUI kann Werkzeuge verwenden, die von irgendeinem OpenAPI-Server bereitgestellt werden.", "Open WebUI uses faster-whisper internally.": "Open WebUI verwendet intern faster-whisper.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI verwendet SpeechT5 und CMU Arctic-Sprecher-Embeddings.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Die installierte Open-WebUI-Version (v{{OPEN_WEBUI_VERSION}}) ist niedriger als die erforderliche Version (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "OpenAI-API", "OpenAI API Config": "OpenAI-API-Konfiguration", "OpenAI API Key is required.": "OpenAI-API-<PERSON><PERSON><PERSON><PERSON> erforderlich.", "OpenAI API settings updated": "OpenAI-API-Einstellungen aktualisiert", "OpenAI URL/Key required.": "OpenAI-URL/Schlüssel erford<PERSON>lich.", "openapi.json URL or Path": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "oder", "Ordered List": "Geordnete Liste", "Organize your users": "Organisieren Sie Ihre <PERSON>er", "Other": "<PERSON><PERSON>", "OUTPUT": "AUSGABE", "Output format": "Ausgabeformat", "Output Format": "Ausgabe Format", "Overview": "Übersicht", "page": "Seite", "Paginate": "", "Parameters": "Parameter", "Password": "Passwort", "Passwords do not match.": "", "Paste Large Text as File": "Großen Text als Datei einfügen", "PDF document (.pdf)": "PDF-Dokument (.pdf)", "PDF Extract Images (OCR)": "Text von Bildern aus PDFs extrahieren (OCR)", "pending": "ausstehend", "Pending": "<PERSON><PERSON><PERSON><PERSON>", "Pending User Overlay Content": "Inhalt des Overlays 'Ausstehende Kontoaktivierung'", "Pending User Overlay Title": "Titel des Overlays 'Ausstehende Kontoaktivierung'", "Permission denied when accessing media devices": "Zugriff auf Mediengeräte verweigert", "Permission denied when accessing microphone": "Zugriff auf das Mikrofon verweigert", "Permission denied when accessing microphone: {{error}}": "Zugriff auf das Mikrofon verweigert: {{error}}", "Permissions": "Berechtigungen", "Perplexity API Key": "Perplexity API-Schlüssel", "Perplexity Model": "Perplexity Modell", "Perplexity Search Context Usage": "", "Personalization": "Personalisierung", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "Anheften", "Pinned": "Angeheftet", "Pioneer insights": "Bahnbrechende Erkenntnisse", "Pipe": "", "Pipeline deleted successfully": "Pipeline erfolgreich <PERSON>", "Pipeline downloaded successfully": "Pipeline erfolgreich heruntergeladen", "Pipelines": "Pipelines", "Pipelines Not Detected": "Pipelines nicht erkannt", "Pipelines Valves": "Pipeline <PERSON>", "Plain text (.md)": "Nur Text (.md)", "Plain text (.txt)": "Nur Text (.txt)", "Playground": "Testumgebung", "Playwright Timeout (ms)": "Playwright Timeout (ms)", "Playwright WebSocket URL": "Playwright WebSocket-URL", "Please carefully review the following warnings:": "Bitte überprüfen Sie die folgenden Warnungen sorgfältig:", "Please do not close the settings page while loading the model.": "Bitte schließen die Einstellungen-Seite nicht, während das Modell lädt.", "Please enter a prompt": "<PERSON>te geben Sie einen Prompt ein", "Please enter a valid path": "Bitte geben Si<PERSON> einen gültigen Pfad ein", "Please enter a valid URL": "<PERSON>te geben Si<PERSON> eine gültige URL ein", "Please fill in all fields.": "Bitte füllen Sie alle Felder aus.", "Please select a model first.": "Bitte wählen Si<PERSON> zu<PERSON>t ein Modell aus.", "Please select a model.": "Bitte wählen Si<PERSON> ein Modell aus.", "Please select a reason": "Bitte wählen Si<PERSON> einen Grund aus", "Please wait until all files are uploaded.": "", "Port": "Port", "Positive attitude": "Positive Einstellung", "Prefix ID": "Präfix-ID", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "Prefix-ID wird verwendet, um Konflikte mit anderen Verbindungen zu vermeiden, indem ein Präfix zu den Modell-IDs hinzugefügt wird - leer lassen, um zu deaktivieren", "Prevent file creation": "Dateierstellung verhindern", "Preview": "Vorschau", "Previous 30 days": "Vorherige 30 Tage", "Previous 7 days": "Vorherige 7 Tage", "Previous message": "Vorherige Nachricht", "Private": "Privat", "Profile Image": "Profilbild", "Prompt": "Prompt", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (z. B. \"<PERSON><PERSON><PERSON><PERSON>e mir eine interessante Tatsache über das Römische Reich\")", "Prompt Autocompletion": "Prompt Autovervollständigung", "Prompt Content": "Prompt-Inhalt", "Prompt created successfully": "Prompt erfolg<PERSON>ich erstellt", "Prompt suggestions": "Prompt-Vorschläge", "Prompt updated successfully": "Prompt erfolgreich aktualisiert", "Prompts": "Prompts", "Prompts Access": "Prompt-Zugriff", "Prompts Public Sharing": "Öffentliche Freigabe von Pro<PERSON>s", "Public": "<PERSON><PERSON><PERSON><PERSON>", "Pull \"{{searchValue}}\" from Ollama.com": "\"{{searchValue}}\" von Ollama.com beziehen", "Pull a model from Ollama.com": "Modell von Ollama.com beziehen", "Query Generation Prompt": "Abfragegenerierungsprompt", "Quick Actions": "", "RAG Template": "RAG-Vorlage", "Rating": "Bewertung", "Re-rank models by topic similarity": "Modelle nach thematischer Ähnlichkeit neu ordnen", "Read": "<PERSON><PERSON>", "Read Aloud": "Vorlesen", "Reason": "Nachdenken", "Reasoning Effort": "Nachdenk-<PERSON><PERSON><PERSON><PERSON>", "Record": "Au<PERSON><PERSON><PERSON><PERSON>", "Record voice": "<PERSON><PERSON><PERSON> au<PERSON><PERSON><PERSON>", "Redirecting you to Open WebUI Community": "Sie werden zur OpenWebUI-Community weitergeleitet", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "<PERSON><PERSON><PERSON> Sie sich auf sich selbst als \"Benutzer\" (z. B. \"Benutzer lernt Spanisch\")", "References from": "Referenzen aus", "Refused when it shouldn't have": "<PERSON><PERSON><PERSON><PERSON><PERSON>, obwohl es nicht hätte abgelehnt werden sollen", "Regenerate": "<PERSON><PERSON> gene<PERSON>", "Reindex": "Neu indexieren", "Reindex Knowledge Base Vectors": "Vektoren der Wissensdatenbank neu indizieren", "Release Notes": "Veröffentlichungshinweise", "Releases": "<PERSON>en", "Relevance": "<PERSON><PERSON><PERSON><PERSON>", "Relevance Threshold": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Remember Dismissal": "", "Remove": "Entfernen", "Remove {{MODELID}} from list.": "{{MODELID}} von der Liste entfernen.", "Remove file": "<PERSON><PERSON> ent<PERSON>nen", "Remove File": "<PERSON><PERSON> ent<PERSON>nen", "Remove image": "Bild entfernen", "Remove Model": "<PERSON><PERSON>", "Remove this tag from list": "<PERSON>sen Tag von der Liste entfernen", "Rename": "Umbenennen", "Reorder Models": "<PERSON><PERSON> neu anordnen", "Reply in Thread": "<PERSON><PERSON><PERSON><PERSON> antworten", "Reranking Engine": "Reranking-Engine", "Reranking Model": "Reranking-Modell", "Reset": "Z<PERSON>ücksetzen", "Reset All Models": "Alle Modelle zurücksetzen", "Reset Upload Directory": "Upload-Verzeichnis zurücksetzen", "Reset Vector Storage/Knowledge": "Vektorspeicher/Wissen zurücksetzen", "Reset view": "<PERSON><PERSON><PERSON>", "Response": "Antwort", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Benachrichtigungen können nicht aktiviert werden, da die Website-Berechtigungen abgelehnt wurden. Bitte besuchen Sie Ihre Browser-Einstellungen, um den erforderlichen Zugriff zu gewähren.", "Response splitting": "Antwortaufteilung", "Response Watermark": "Antwort Wasserzeichen", "Result": "<PERSON><PERSON><PERSON><PERSON>", "Retrieval": "", "Retrieval Query Generation": "Abfragegenerierung", "Rich Text Input for Chat": "Rich-Text-Eingabe für Chats", "RK": "RK", "Role": "<PERSON><PERSON>", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "Ausführen", "Running": "Läuft", "Save": "Speichern", "Save & Create": "<PERSON><PERSON><PERSON><PERSON>", "Save & Update": "Aktualisieren", "Save As Copy": "Als Ko<PERSON> s<PERSON>ichern", "Save Tag": "<PERSON> speichern", "Saved": "Gespe<PERSON>rt", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Das direkte Speichern von Chats im Browser-Speicher wird nicht mehr unterstützt. Bitte nehmen Sie einen Moment Zeit, um Ihre Chats zu exportieren und zu löschen, indem Sie auf die Schaltfläche unten klicken. <PERSON><PERSON>, Sie können Ihre Chats problemlos über das Backend wieder importieren.", "Scroll On Branch Change": "", "Search": "<PERSON><PERSON>", "Search a model": "<PERSON><PERSON> suchen", "Search Base": "<PERSON><PERSON><PERSON>", "Search Chats": "Chats durchsuchen...", "Search Collection": "Sammlung durchsuchen", "Search Filters": "Suchfilter", "search for archived chats": "", "search for folders": "", "search for pinned chats": "", "search for shared chats": "", "search for tags": "nach Tags suchen", "Search Functions": "Funktionen durchsuchen...", "Search In Models": "", "Search Knowledge": "Wissen durchsuchen", "Search Models": "Modelle durchsuchen...", "Search Notes": "Notizen durchsuchen...", "Search options": "Suchoptionen", "Search Prompts": "Prompts durchsuchen...", "Search Result Count": "Anzahl der Suchergebnisse", "Search the internet": "Im Internet suchen", "Search Tools": "Werkzeuge durchsuchen...", "SearchApi API Key": "SearchApi-API-Schlüssel", "SearchApi Engine": "SearchApi-Engine", "Searched {{count}} sites": "{{count}} Websites durchsucht", "Searching \"{{searchQuery}}\"": "<PERSON><PERSON> nach \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "<PERSON>e im Wissen nach \"{{searchQuery}}\"", "Searching the web...": "Durchsuche das Web...", "Searxng Query URL": "Searxng-Abfrage-URL", "See readme.md for instructions": "Anleitung in readme.md anzeigen", "See what's new": "Entdecken Sie die Neuigkeiten", "Seed": "Seed", "Select a base model": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> ein Basismodell", "Select a conversation to preview": "", "Select a engine": "Wählen Sie eine Engine", "Select a function": "Wählen Sie eine Funktion", "Select a group": "Wählen Sie eine Gruppe", "Select a model": "<PERSON>ählen Sie ein Modell", "Select a pipeline": "Wählen Sie eine Pipeline", "Select a pipeline url": "Wählen Sie eine Pipeline-URL", "Select a tool": "Wählen Sie ein Werkzeug", "Select an auth method": "Wählen Sie eine Authentifizierungsmethode", "Select an Ollama instance": "Wählen Sie eine Ollama-Instanz", "Select Engine": "Engine auswählen", "Select Knowledge": "Wissensdatenbank auswählen", "Select only one model to call": "<PERSON><PERSON><PERSON>en Si<PERSON> nur ein Modell zum Anrufen aus", "Selected model(s) do not support image inputs": "Ihre ausgewählten Modelle unterstützen keine Bildeingaben", "semantic": "", "Semantic distance to query": "Semantische Distanz zur Abfrage", "Send": "Senden", "Send a Message": "Eine Nachricht senden", "Send message": "Nachricht senden", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Sendet `stream_options: { include_usage: true }` in der Anfrage.\nUnterstützte Anbieter geben Token-Nutzungsinformationen in der Antwort zurück, wenn dies festgelegt ist.", "September": "September", "SerpApi API Key": "SerpApi API-Schlüssel", "SerpApi Engine": "SerpApi-Engine", "Serper API Key": "Serper-API-Schlüssel", "Serply API Key": "Serply-API-Schlüssel", "Serpstack API Key": "Serpstack-API-Schlüssel", "Server connection verified": "Serververbindung überprüft", "Set as default": "Als Standard festlegen", "Set CFG Scale": "CFG-Skala festlegen", "Set Default Model": "Standardmodell festlegen", "Set embedding model": "Einbettungsmodell festlegen", "Set embedding model (e.g. {{model}})": "Einbettungsmodell festlegen (z. B. {{model}})", "Set Image Size": "Bildgröße festlegen", "Set reranking model (e.g. {{model}})": "Rerankingmodell festlegen (z. B. {{model}})", "Set Sampler": "Sampler festlegen", "Set Scheduler": "Scheduler festlegen", "Set Steps": "Schrittgröße festlegen", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "Legen Sie die Anzahl der Schichten fest, die auf die GPU ausgelagert werden. Eine Erhöhung dieses Wertes kann die Leistung für Modelle, die für die GPU-Beschleunigung optimiert sind, erhe<PERSON><PERSON> verbessern, kann jedoch auch mehr Strom und GPU-Ressourcen verbrauchen.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "Legt die Anzahl der für die Berechnung verwendeten Worker-Threads fest. Diese Option steuert, wie viele Threads zur gleichzeitigen Verarbeitung eingehender Anfragen verwendet werden. Eine Erhöhung dieses Wertes kann die Leistung bei hoher Parallelität verbessern, kann aber mehr CPU-Ressourcen verbrauchen.", "Set Voice": "Stimme festlegen", "Set whisper model": "Whisper-<PERSON><PERSON> festlegen", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets how far back for the model to look back to prevent repetition.": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "", "Sets the size of the context window used to generate the next token.": "Legt die Größe des Kontextfensters fest, das zur Generierung des nächsten Token verwendet wird.", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "Legt die zu verwendenden Stoppsequenzen fest. Wenn dieses Muster erkannt wird, stoppt das LLM die Textgenerierung und gibt zurück. Mehrere Stoppmuster können festgelegt werden, indem mehrere separate Stopp-Parameter in einer Modelldatei angegeben werden.", "Settings": "Einstellungen", "Settings saved successfully!": "Einstellungen erfolgreich gespeichert!", "Share": "Teilen", "Share Chat": "<PERSON><PERSON> teilen", "Share to Open WebUI Community": "Mit OpenWebUI Community teilen", "Sharing Permissions": "Berechtigungen teilen", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "Anzeigen", "Show \"What's New\" modal on login": "\"Was gib<PERSON>'s Neues\"-<PERSON><PERSON> beim Anmelden anzeigen", "Show Admin Details in Account Pending Overlay": "Admin-Details im Account-Pending-Overlay anzeigen", "Show All": "Alle anzeigen", "Show Formatting Toolbar": "", "Show image preview": "Bildvorschau anzeigen", "Show Less": "<PERSON><PERSON> anzeigen", "Show Model": "<PERSON><PERSON> anzeigen", "Show shortcuts": "Verknüpfungen anzeigen", "Show your support!": "Zeigen Sie Ihre Unterstützung!", "Showcased creativity": "Kreativität gezeigt", "Sign in": "Anmelden", "Sign in to {{WEBUI_NAME}}": "Bei {{WEBUI_NAME}} anmelden", "Sign in to {{WEBUI_NAME}} with LDAP": "Bei {{WEBUI_NAME}} mit LDAP anmelden", "Sign Out": "Abmelden", "Sign up": "Registrieren", "Sign up to {{WEBUI_NAME}}": "Bei {{WEBUI_NAME}} registrieren", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to False.": "", "Signing in to {{WEBUI_NAME}}": "Wird bei {{WEBUI_NAME}} angemeldet", "Sink List": "", "sk-1234": "sk-1234", "Skip Cache": "<PERSON><PERSON>", "Skip the cache and re-run the inference. Defaults to False.": "", "Sougou Search API sID": "Sougou Search API sID", "Sougou Search API SK": "Sougou Search API SK", "Source": "<PERSON><PERSON>", "Speech Playback Speed": "Sprachwiedergabegeschwindigkeit", "Speech recognition error: {{error}}": "Spracherkennungsfehler: {{error}}", "Speech-to-Text": "<PERSON><PERSON><PERSON>-zu-Text", "Speech-to-Text Engine": "<PERSON><PERSON><PERSON>-zu-Text-Engine", "Stop": "Stop", "Stop Generating": "Generierung stoppen", "Stop Sequence": "Stop-Sequenz", "Stream Chat Response": "Chat-Antwort streamen", "Stream Delta Chunk Size": "", "Strikethrough": "Durchgestrichen", "Strip Existing OCR": "", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "", "STT Model": "STT-Modell", "STT Settings": "STT-Einstellungen", "Stylized PDF Export": "Stilisierter PDF-Export", "Subtitle (e.g. about the Roman Empire)": "Untertitel (z. B. über das Römische Reich)", "Success": "Erfolg", "Successfully updated.": "Erfolgreich aktualisiert.", "Suggest a change": "", "Suggested": "Vorgeschlagen", "Support": "Unterstützung", "Support this plugin:": "Unterstützen Sie dieses Plugin:", "Supported MIME Types": "", "Sync directory": "Verzeichnis synchronisieren", "System": "System", "System Instructions": "Systemanweisungen", "System Prompt": "System-Prompt", "Tags": "Tags", "Tags Generation": "Tag-Generierung", "Tags Generation Prompt": "Prompt für Tag-Generierung", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "Tail-Free Sampling wird verwendet, um den Einfluss weniger wahrscheinlicher Token auf die Ausgabe zu reduzieren. Ein höherer Wert (z. B. 2.0) reduziert den Einfluss stärker, während ein Wert von 1.0 diese Einstellung deaktiviert. (Standard: 1)", "Talk to model": "<PERSON>u einem Modell sprechen", "Tap to interrupt": "Zum Unterbrechen tippen", "Task List": "Aufgabenliste", "Task Model": "<PERSON>f<PERSON><PERSON><PERSON><PERSON>", "Tasks": "Aufgaben", "Tavily API Key": "Tavily-API-Schlüssel", "Tavily Extract Depth": "Tavily Extraktionstiefe", "Tell us more:": "<PERSON><PERSON><PERSON><PERSON><PERSON> Si<PERSON> uns mehr:", "Temperature": "Temperatur", "Temporary Chat": "Temporäre Unterhaltung", "Text Splitter": "Text-Splitter", "Text-to-Speech": "Text-<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text-to-Speech Engine": "Text-zu-S<PERSON>che-Engine", "Thanks for your feedback!": "Danke für Ihr Feedback!", "The Application Account DN you bind with for search": "Der Anwendungs-Konto-DN, mit dem Sie für die Suche binden", "The base to search for users": "Die Basis, in der nach Benutzern gesucht wird", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Die Entwickler hinter diesem Plugin sind leidenschaftliche Freiwillige aus der Community. Wenn Sie dieses Plugin hilfreich finden, erwägen <PERSON>, zu seiner Entwicklung beizutragen.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "Die Bewertungs-Bestenliste basiert auf dem Elo-Bewertungssystem und wird in Echtzeit aktualisiert.", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "Das LDAP-Attribut, das der Mail zugeordnet ist, die Benutzer zum Anmelden verwenden.", "The LDAP attribute that maps to the username that users use to sign in.": "Das LDAP-Attribut, das dem Benutzernamen zugeordnet ist, den Benutzer zum Anmelden verwenden.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "Die Bestenliste befindet sich derzeit in der Beta-Phase, und es ist möglich, dass wir die Bewertungsberechnungen anpassen, während wir den Algorithmus verfeinern.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "Die maximale Dateigröße in MB. Wenn die Dateigröße dieses Limit überschreitet, wird die Datei nicht hochgeladen.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "Die maximale An<PERSON><PERSON> von <PERSON>, die gleichzeitig im Chat verwendet werden können. Wenn die Anzahl der Dateien dieses Limit überschreitet, werden die Dateien nicht hochgeladen.", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Die Punktzahl sollte ein Wert zwischen 0,0 (0 %) und 1,0 (100 %) sein.", "The stream delta chunk size for the model. Increasing the chunk size will make the model respond with larger pieces of text at once.": "", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "Die Temperatur des Modells. Eine Erhöhung der Temperatur führt zu kreativeren Antworten des Modells.", "The Weight of BM25 Hybrid Search. 0 more lexical, 1 more semantic. Default 0.5": "", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "Design", "Thinking...": "Denke nach...", "This action cannot be undone. Do you wish to continue?": "Diese Aktion kann nicht rückgängig gemacht werden. Möchten Si<PERSON> fortfahren?", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "Dieser Kanal wurde am {{createdAt}} erstellt. Dies ist der Beginn des {{channelName}} Kanals.", "This chat won't appear in history and your messages will not be saved.": "Diese Unterhaltung erscheint nicht in Ihrem Chat-Verlauf. Alle Nachrichten sind privat und werden nicht gespeichert.", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Dies stellt sicher, dass Ihre wertvollen Chats sicher in Ihrer Backend-Datenbank gespeichert werden. Vielen Dank!", "This feature is experimental and may be modified or discontinued without notice.": "", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Dies ist eine experimentelle Funktion, sie funktioniert möglicherweise nicht wie erwartet und kann jederzeit geändert werden.", "This model is not publicly available. Please select another model.": "Dieses Modell ist nicht öffentlich verfügbar. Bitte wählen Si<PERSON> ein anderes Modell aus.", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "Diese Option steuert, wie viele Token beim Aktualisieren des Kontexts beibehalten werden. Wen<PERSON> beispielsweise 2 eingestellt ist, werden die letzten 2 Tokens des Gesprächskontexts beibehalten. Das Beibehalten des Kontexts kann helfen, die Kontinuität eines Gesprächs zu wahren, kann aber die Fähigkeit, auf neue Themen zu reagieren, einschränken.", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "Diese Option legt die maximale Anzahl von <PERSON> fest, die das Modell in seiner Antwort generieren kann. Eine Erhöhung dieses Limits ermöglicht längere Antworten, kann aber auch die Wahrscheinlichkeit erhöhen, dass unbrauchbare oder irrelevante Inhalte generiert werden.", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Diese Option löscht alle vorhandenen Dateien in der Sammlung und ersetzt sie durch neu hochgeladene Dateien.", "This response was generated by \"{{model}}\"": "<PERSON><PERSON>rt wurde von \"{{model}}\" generiert", "This will delete": "<PERSON><PERSON>", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "<PERSON><PERSON> l<PERSON> <strong>{{NAME}}</strong> und <strong>alle Inhalte</strong>.", "This will delete all models including custom models": "Dies wird alle Modelle einschließlich benutzerdefinierter Modelle löschen", "This will delete all models including custom models and cannot be undone.": "Dies wird alle Modelle einschließlich benutzerdefinierter Modelle löschen und kann nicht rückgängig gemacht werden.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Dad<PERSON>ch wird die Wissensdatenbank zurückgesetzt und alle Dateien synchronisiert. Möchten Sie fortfahren?", "Thorough explanation": "Ausführliche Erklärung", "Thought for {{DURATION}}": "Nachgedacht für {{DURATION}}", "Thought for {{DURATION}} seconds": "Nachgedacht für {{DURATION}} Sekunden", "Thought for less than a second": "", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "Tika-Server-U<PERSON>.", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Tipp: Aktualisieren Sie mehrere Variablenfelder nacheinander, indem Si<PERSON> nach jedem Ersetzen die Tabulatortaste im Eingabefeld des Chats drücken.", "Title": "Titel", "Title (e.g. Tell me a fun fact)": "Titel (z. B. Erzähl mir einen lustigen Fakt)", "Title Auto-Generation": "Chat-Titel automatisch generieren", "Title cannot be an empty string.": "<PERSON>itel darf nicht leer sein.", "Title Generation": "Titelgenerierung", "Title Generation Prompt": "Prompt für Titelgenerierung", "TLS": "TLS", "To access the available model names for downloading,": "Um auf die verfügbaren Modellnamen zuzugreifen,", "To access the GGUF models available for downloading,": "Um auf die verfügbaren GGUF-Modelle zuzugreifen,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Um auf das WebUI zugreifen zu können, wenden <PERSON> sich bitte an einen Administrator. Administratoren können den Benutzerstatus über das Admin-Panel verwalten.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "Um Wissensdatenbanken hier anzuh<PERSON>ngen, fügen Si<PERSON> sie zunächst dem Arbeitsbereich \"Wissen\" hinzu.", "To learn more about available endpoints, visit our documentation.": "Um mehr über verfügbare Endpunkte zu erfahren, besuchen Sie unsere Dokumentation.", "To learn more about powerful prompt variables, click here": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "Um Ihre Privatsphäre zu schützen, werden nur Bewertungen, Modell-IDs, Tags und Metadaten aus Ihrem Feedback geteilt – Ihre Chats bleiben privat und werden nicht einbezogen.", "To select actions here, add them to the \"Functions\" workspace first.": "Um Aktionen auszuwählen, fügen Sie diese zunächst dem Arbeitsbereich „Funktionen“ hinzu.", "To select filters here, add them to the \"Functions\" workspace first.": "Um Filter auszuw<PERSON>hlen, fügen Si<PERSON> diese zunächst dem Arbeitsbereich „Funktionen“ hinzu.", "To select toolkits here, add them to the \"Tools\" workspace first.": "Um Toolkits auszuwählen, fügen Sie sie zunächst dem Arbeitsbereich „Werkzeuge“ hinzu.", "Toast notifications for new updates": "Toast-Benachrichtigungen für neue Updates", "Today": "<PERSON><PERSON>", "Toggle search": "Suche umschalten", "Toggle settings": "Einstellungen umschalten", "Toggle sidebar": "Seitenleiste umschalten", "Toggle whether current connection is active.": "", "Token": "Token", "Too verbose": "<PERSON><PERSON> au<PERSON>", "Tool created successfully": "Werkzeug erfolgreich erstellt", "Tool deleted successfully": "Werkzeug erfolgreich gelöscht", "Tool Description": "Werkzeugbeschreibung", "Tool ID": "Werkzeug-ID", "Tool imported successfully": "Werkzeug erfolgreich importiert", "Tool Name": "Werkzeugname", "Tool Servers": "Werkzeugserver", "Tool updated successfully": "Werkzeug erfolgreich aktualisiert", "Tools": "Werkzeuge", "Tools Access": "Werkzeugzugriff", "Tools are a function calling system with arbitrary code execution": "Wekzeuge sind ein Funktionssystem mit beliebiger Codeausführung", "Tools Function Calling Prompt": "Prompt für Funktionssystemaufrufe", "Tools have a function calling system that allows arbitrary code execution.": "Werkzeuge verfügen über ein Funktionssystem, das die Ausführung beliebigen Codes ermöglicht.", "Tools Public Sharing": "Öffentliche Freigabe von Werkzeugen", "Top K": "Top-K", "Top K Reranker": "Top-<PERSON>", "Transformers": "Transformers", "Trouble accessing Ollama?": "Probleme beim Zug<PERSON> auf Ollama?", "Trust Proxy Environment": "Proxy-Umgebung vertrauen", "Try Again": "", "TTS Model": "TTS-Modell", "TTS Settings": "TTS-Einstellungen", "TTS Voice": "TTS-Stimme", "Type": "Art", "Type Hugging Face Resolve (Download) URL": "<PERSON><PERSON><PERSON> Sie die Hugging Face Resolve-URL ein", "Uh-oh! There was an issue with the response.": "Oh nein! Es gab ein Problem mit der Antwort.", "UI": "Oberfläche", "Unarchive All": "Alle wiederherstellen", "Unarchive All Archived Chats": "Alle archivierten Chats wiederherstellen", "Unarchive Chat": "<PERSON><PERSON> wied<PERSON><PERSON><PERSON><PERSON>", "Underline": "Unterstreichen", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "Geheimnisse entsperren", "Unpin": "<PERSON><PERSON><PERSON>", "Unravel secrets": "Geheimnisse lüften", "Unsupported file type.": "", "Untagged": "<PERSON><PERSON><PERSON><PERSON>", "Untitled": "Unbenannt", "Update": "Aktualisieren", "Update and Copy Link": "Aktualisieren und Link kopieren", "Update for the latest features and improvements.": "Aktualisieren Sie für die neuesten Funktionen und Verbesserungen.", "Update password": "Passwort aktualisieren", "Updated": "<PERSON>ktual<PERSON><PERSON>", "Updated at": "Aktualisiert am", "Updated At": "Aktualisiert am", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "Upgrade auf einen lizenzierten Plan für erweiterte Funktionen wie individuelles Design, Branding und dedizierten Support.", "Upload": "Hochladen", "Upload a GGUF model": "GGUF-Model hochladen", "Upload Audio": "Audio hochladen", "Upload directory": "Upload-Verzeichnis", "Upload files": "<PERSON><PERSON>", "Upload Files": "Datei(en) hochladen", "Upload Pipeline": "Pipeline hochladen", "Upload Progress": "Hochladefortschritt", "URL": "URL", "URL Mode": "URL-Modus", "Usage": "Nutzungsinfos", "Use '#' in the prompt input to load and include your knowledge.": "Nutzen Sie '#' in der Prompt-Eingabe, um Ihr Wissen zu laden und einzuschließen.", "Use Gravatar": "Gravatar verwenden", "Use groups to group your users and assign permissions.": "Nutzen Sie Gruppen, um Ihre Benutzer zu gruppieren und Berechtigungen zuzuweisen.", "Use Initials": "<PERSON><PERSON> verwenden", "Use LLM": "Verwende LLM", "Use no proxy to fetch page contents.": "<PERSON>inen Proxy zum Abrufen von Seiteninhalten verwenden.", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "Den durch die Umgebungsvariablen http_proxy und https_proxy festgelegten Proxy zum Abru<PERSON> von Seiteninhalten verwenden.", "user": "<PERSON><PERSON><PERSON>", "User": "<PERSON><PERSON><PERSON>", "User Groups": "", "User location successfully retrieved.": "Benutzerstandort erfolgreich ermittelt.", "User menu": "Benutzermenü", "User Webhooks": "Benutzer Webhooks", "Username": "<PERSON><PERSON><PERSON><PERSON>", "Users": "<PERSON><PERSON><PERSON>", "Using Entire Document": "", "Using Focused Retrieval": "", "Using the default arena model with all models. Click the plus button to add custom models.": "Verwendung des Standard-Arena-Modells mit allen Modellen. Klicken Sie auf die Plus-Schaltfläche, um benutzerdefinierte Modelle hinzuzufügen.", "Valid time units:": "Gültige Zeiteinheiten:", "Valves": "Valves", "Valves updated": "<PERSON><PERSON> aktual<PERSON>", "Valves updated successfully": "<PERSON>ves erfolgreich aktualisiert", "variable": "Variable", "Verify Connection": "Verbindung verifizieren", "Verify SSL Certificate": "SSL Zertifikat prüfen", "Version": "Version", "Version {{selectedVersion}} of {{totalVersions}}": "Version {{selectedVersion}} von {{totalVersions}}", "View Replies": "Antworten anzeigen", "View Result from **{{NAME}}**": "<PERSON><PERSON><PERSON><PERSON> von **{{NAME}}** anzeigen", "Visibility": "Sichtbarkeit", "Vision": "Bilderkennung", "Voice": "Stimme", "Voice Input": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Voice mode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Warning": "<PERSON><PERSON><PERSON>", "Warning:": "Warnung:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Warnung: <PERSON><PERSON> dies aktivieren, können Benutzer beliebigen Code auf dem Server hochladen.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Warnung: <PERSON><PERSON> Si<PERSON> das Einbettungsmodell aktualisieren oder ändern, müssen Sie alle Dokumente erneut importieren.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "Warnung: Die Jupyter-Ausführung ermöglicht beliebige Codeausführung und birgt erhebliche Sicherheitsrisiken – gehen Sie mit äußerster Vorsicht vor.", "Web": "Web", "Web API": "Web-API", "Web Loader Engine": "", "Web Search": "Websuche", "Web Search Engine": "Suchmaschine", "Web Search in Chat": "Websuche im Chat", "Web Search Query Generation": "Abfragegenerierung für Websuche", "Webhook URL": "Webhook URL", "WebUI Settings": "WebUI-Einstellungen", "WebUI URL": "WebUI-URL", "WebUI will make requests to \"{{url}}\"": "WebUI wird Anfragen an \"{{url}}\" senden", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI wird Anfragen an \"{{url}}/api/chat\" senden", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI wird Anfragen an \"{{url}}/chat/completions\" senden", "What are you trying to achieve?": "Was versuchen Si<PERSON> zu erreichen?", "What are you working on?": "Woran arbeiten Sie?", "What's New in": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "<PERSON><PERSON><PERSON><PERSON>, antwo<PERSON><PERSON> das Modell in Echtzeit auf jede Chat-Nachricht und generiert eine Antwort, sobald der Benutzer eine Nachricht sendet. Dieser Modus ist nützlich für Live-Chat-Anwendungen, kann jedoch die Leistung auf langsamerer Hardware beeinträchtigen.", "wherever you are": "wo immer Si<PERSON> sind", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "", "Whisper (Local)": "Whisper (lokal)", "Why?": "Warum?", "Widescreen Mode": "Breitbildmodus", "Won": "Gewonnen", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "Funktioniert zusammen mit Top-K. Ein höherer Wert (z. B. 0,95) führt zu vielfältigerem Text, während ein niedrigerer Wert (z. B. 0,5) fokussierteren und konservativeren Text erzeugt.", "Workspace": "Arbeitsbereich", "Workspace Permissions": "Arbeitsbereichsberechtigungen", "Write": "Schreiben", "Write a prompt suggestion (e.g. Who are you?)": "Schreiben Sie einen Promptvorschlag (z. B. Wer sind Sie?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Schreibe eine kurze Zusammenfassung in 50 Wörtern, die [Thema oder Schlüsselwort] zusammenfasst.", "Write something...": "Schreiben Sie etwas...", "Yacy Instance URL": "Yacy-Instanz-URL", "Yacy Password": "Yacy-Passwort", "Yacy Username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Yesterday": "Gestern", "You": "<PERSON><PERSON>", "You are currently using a trial license. Please contact support to upgrade your license.": "Sie benutzen zurzeit eine Testlizenz. Bitte kontaktieren Sie den Support für ein Lizenzupgrade.", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "<PERSON>e können nur mit maximal {{maxCount}} Datei(en) gleichzeitig chatten.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Personalisieren Sie Interaktionen mit LLMs, indem Sie über die Schaltfläche \"Verwalten\" Erinnerungen hinzufügen.", "You cannot upload an empty file.": "<PERSON>e können keine leere Datei hochladen.", "You do not have permission to upload files.": "Sie haben keine Berechtigung zum Hochladen von <PERSON>.", "You have no archived conversations.": "<PERSON>e haben keine Chats archiviert.", "You have shared this chat": "<PERSON>e haben diesen <PERSON><PERSON> get<PERSON>t", "You're a helpful assistant.": "Du bist ein hilfreicher Assistent.", "You're now logged in.": "Sie sind jetzt eingeloggt.", "Your account status is currently pending activation.": "Ihr Kontostatus ist derzeit ausstehend und wartet auf Aktivierung.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "Ihr gesamter Beitrag geht direkt an den Plugin-Entwickler; Open WebUI behält keinen Prozentsatz ein. Die gewählte Finanzierungsplattform kann jedoch eigene Gebühren haben.", "Youtube": "YouTube", "Youtube Language": "YouTube Sprache", "Youtube Proxy URL": "YouTube Proxy URL"}