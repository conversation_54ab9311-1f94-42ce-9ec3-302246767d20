{"-1 for no limit, or a positive integer for a specific limit": "-1 per a cap límit, o un nombre positiu per a un límit específic", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' o '-1' perquè no caduqui mai.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(p. ex. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(p. ex. `sh webui.sh --api`)", "(latest)": "(últim)", "(leave blank for to use commercial endpoint)": "(deixa-ho buit per utilitzar un punt d'accés comercial)", "[Last] dddd [at] h:mm A": "[Darrer] dddd [a] h:mm A", "[Today at] h:mm A": "[Avui a les] h:mm A", "[Yesterday at] h:mm A": "[Ahir a les] h:mm A", "{{ models }}": "{{ models }}", "{{COUNT}} Available Tools": "{{COUNT}} eines disponibles", "{{COUNT}} characters": "{{COUNT}} caràcters", "{{COUNT}} hidden lines": "{{COUNT}} línies ocultes", "{{COUNT}} Replies": "{{COUNT}} respostes", "{{COUNT}} words": "{{COUNT}} paraules", "{{user}}'s Chats": "Els xats de {{user}}", "{{webUIName}} Backend Required": "El Backend de {{webUIName}} és necessari", "*Prompt node ID(s) are required for image generation": "*Els identificadors de nodes d'indicacions són necessaris per a la generació d'imatges", "A new version (v{{LATEST_VERSION}}) is now available.": "Hi ha una nova versió disponible (v{{LATEST_VERSION}}).", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Un model de tasca s'utilitza quan es realitzen tasques com ara generar títols per a xats i consultes de cerca per a la web", "a user": "un usuari", "About": "Sobre", "Accept autocomplete generation / Jump to prompt variable": "Acceptar la generació autocompletada / Saltar a la variable d'indicació", "Access": "Accés", "Access Control": "Control d'accés", "Accessible to all users": "Accessible a tots els usuaris", "Account": "<PERSON><PERSON><PERSON>", "Account Activation Pending": "Activació del compte pendent", "Accurate information": "Informació precisa", "Action": "<PERSON><PERSON><PERSON>", "Actions": "Accions", "Activate": "Activar", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Activa aquest comanda escrivint \"{{COMMAND}}\" en el xat", "Active Users": "<PERSON><PERSON><PERSON> actius", "Add": "<PERSON><PERSON>gi<PERSON>", "Add a model ID": "Afegir un ID de model", "Add a short description about what this model does": "Afegeix una breu descripció sobre què fa aquest model", "Add a tag": "Afegir una etiqueta", "Add Arena Model": "Afegir model de l'Arena", "Add Connection": "Afegir connexió", "Add Content": "Afegir contingut", "Add content here": "Afegir contingut aquí", "Add Custom Parameter": "Afegir paràmetre personalitzat ", "Add custom prompt": "Afegir una indicació personalitzada", "Add Details": "<PERSON><PERSON><PERSON><PERSON>", "Add Files": "<PERSON><PERSON><PERSON><PERSON> arxius", "Add Group": "Afegir grup", "Add Memory": "<PERSON><PERSON><PERSON><PERSON>", "Add Model": "Afegir un model", "Add Reaction": "<PERSON><PERSON><PERSON><PERSON>", "Add Tag": "Afegir etiqueta", "Add Tags": "Afegir etiquetes", "Add text content": "Afegir contingut de text", "Add User": "Afegir un usuari", "Add User Group": "Afegir grup d'usuaris", "Additional Config": "Configuració addicional", "Additional configuration options for marker. This should be a JSON string with key-value pairs. For example, '{\"key\": \"value\"}'. Supported keys include: disable_links, keep_pageheader_in_output, keep_pagefooter_in_output, filter_blank_pages, drop_repeated_text, layout_coverage_threshold, merge_threshold, height_tolerance, gap_threshold, image_threshold, min_line_length, level_count, default_level": "Opcions de configuració addicionals per al marcador. Hauria de ser una cadena JSON amb parelles clau-valor. Per exemple, '{\"key\": \"value\"}'. Les claus compatibles inclouen: disable_links, keep_pageheader_in_output, keep_pagefooter_in_output, filter_blank_pages, drop_repeated_text, layout_coverage_threshold, merge_threshold, height_tolerance, gap_threshold, image_threshold, min_line_length, level_count, default_level", "Adjusting these settings will apply changes universally to all users.": "Si ajustes aquesta preferència, els canvis s'aplicaran de manera universal a tots els usuaris.", "admin": "administrador", "Admin": "Administrador", "Admin Panel": "Panell d'administració", "Admin Settings": "Preferències d'administració", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Els administradors tenen accés a totes les eines en tot moment; els usuaris necessiten eines assignades per model a l'espai de treball.", "Advanced Parameters": "Paràmetres avançats", "Advanced Params": "Paràmetres avançats", "AI": "IA", "All": "<PERSON><PERSON>", "All Documents": "Tots els documents", "All models deleted successfully": "<PERSON><PERSON> els models s'han eliminat correctament", "Allow Call": "Permetre la trucada", "Allow Chat Controls": "Permetre els controls de xat", "Allow Chat Delete": "Permetre eliminar el xat", "Allow Chat Deletion": "Permetre la supressió del xat", "Allow Chat Edit": "Permetre editar el xat", "Allow Chat Export": "Permetre exportar el xat", "Allow Chat Params": "Permetre els paràmetres de xat", "Allow Chat Share": "Permetre compartir el xat", "Allow Chat System Prompt": "Permet la indicació de sistema al xat", "Allow Chat Valves": "Permetre Valves al xat", "Allow File Upload": "Permetre la pujada d'arxius", "Allow Multiple Models in Chat": "Permetre múltiple models al xat", "Allow non-local voices": "Permetre veus no locals", "Allow Speech to Text": "Permetre Parla a Text", "Allow Temporary Chat": "Permetre el xat temporal", "Allow Text to Speech": "Permetre Text a Parla", "Allow User Location": "Permetre la ubicació de l'usuari", "Allow Voice Interruption in Call": "Permetre la interrupció de la veu en una trucada", "Allowed Endpoints": "Punts d'accés permesos", "Allowed File Extensions": "Extensions de fitxer permeses", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "Extensions de fitxer permeses per a la càrrega. Separa múltiples extensions amb comes. Deixa buit per a tots els tipus de fitxer.", "Already have an account?": "Ja tens un compte?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "Alternativa al top_p, i pretén garantir un equilibri de qualitat i varietat. El paràmetre p representa la probabilitat mínima que es consideri un token, en relació amb la probabilitat del token més probable. Per exemple, amb p=0,05 i el token més probable amb una probabilitat de 0,9, es filtren els logits amb un valor inferior a 0,045.", "Always": "Sempre", "Always Collapse Code Blocks": "Reduir sempre els blocs de codi", "Always Expand Details": "Expandir sempre els detalls", "Always Play Notification Sound": "Reproduir sempre un so de notificació", "Amazing": "Al·lucinant", "an assistant": "un assistent", "Analytics": "Analítica", "Analyzed": "Analitzat", "Analyzing...": "Analitzant...", "and": "i", "and {{COUNT}} more": "i {{COUNT}} més", "and create a new shared link.": "i crear un nou enllaç compartit.", "Android": "Android", "API": "API", "API Base URL": "URL Base de l'API", "API Base URL for Datalab Marker service. Defaults to: https://www.datalab.to/api/v1/marker": "URL base de l'API per al servei de marcadors de Datalab. Per defecte: https://www.datalab.to/api/v1/marker", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "Detalls de l'API per utilitzar un model de llenguatge amb visió a la descripció de la imatge. Aquest paràmetre és mutuament excloent amb picture_description_local.", "API Key": "clau <PERSON>", "API Key created.": "clau <PERSON> c<PERSON>a.", "API Key Endpoint Restrictions": "Restriccions del punt d'accés de la Clau API", "API keys": "Claus de l'API", "API Version": "Versió de l'API", "Application DN": "DN d'aplicació", "Application DN Password": "Contrasenya del DN d'aplicació", "applies to all users with the \"user\" role": "s'aplica a tots els usuaris amb el rol \"usuari\"", "April": "Abril", "Archive": "<PERSON><PERSON><PERSON><PERSON>", "Archive All Chats": "Arxiva tots els xats", "Archived Chats": "Xats arxivats", "archived-chat-export": "archived-chat-export", "Are you sure you want to clear all memories? This action cannot be undone.": "Estàs segur que vols netejar totes les memòries? Aquesta acció no es pot desfer.", "Are you sure you want to delete this channel?": "Estàs segur que vols eliminar aquest canal?", "Are you sure you want to delete this message?": "Estàs segur que vols eliminar aquest missatge?", "Are you sure you want to unarchive all archived chats?": "Est<PERSON>s segur que vols desarxivar tots els xats arxivats?", "Are you sure?": "<PERSON><PERSON><PERSON><PERSON> segur?", "Arena Models": "Models de l'Arena", "Artifacts": "Artefactes", "Ask": "Preguntar", "Ask a question": "Fer una pregunta", "Assistant": "Assistent", "Attach file from knowledge": "Associar arxiu del coneixement", "Attention to detail": "Atenció al detall", "Attribute for Mail": "Atribut per al Correu", "Attribute for Username": "Atribut per al Nom d'usuari", "Audio": "<PERSON><PERSON><PERSON>", "August": "Agost", "Auth": "Autenticació", "Authenticate": "Autenticar", "Authentication": "Autenticació", "Auto": "Automàtic", "Auto-Copy Response to Clipboard": "Copiar la resposta automàticament al porta-retalls", "Auto-playback response": "Reproduir la resposta automàticament", "Autocomplete Generation": "Generació automàtica", "Autocomplete Generation Input Max Length": "Entrada màxima de la generació automàtica", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "Cadena d'autenticació de l'API d'AUTOMATIC1111", "AUTOMATIC1111 Base URL": "URL Base d'AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "Es requereix l'URL Base d'AUTOMATIC1111.", "Available list": "Llista de disponibles", "Available Tools": "Eines disponibles", "available!": "disponible!", "Awful": "Terrible", "Azure AI Speech": "Azure AI Speech", "Azure Region": "Regió <PERSON>", "Back": "<PERSON><PERSON><PERSON>", "Bad Response": "Resposta errònia", "Banners": "Banners", "Base Model (From)": "Model base (des de)", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "La memòria cau de la llista de models base accelera l'accés obtenint els models base només a l'inici o en desar la configuració; és més ràpid, però és possible que no mostri els canvis recents del model base.", "before": "abans", "Being lazy": "<PERSON><PERSON> mandr<PERSON>", "Beta": "Beta", "Bing Search V7 Endpoint": "Punt de connexió a Bing Search V7", "Bing Search V7 Subscription Key": "Clau de subscripció a Bing Search V7", "BM25 Weight": "Pes BM25", "Bocha Search API Key": "Clau API de Bocha Search", "Bold": "Negreta", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "Potenciar o penalitzar tokens específics per a respostes limitades. Els valors de biaix es fixaran entre -100 i 100 (inclosos). (Per defecte: cap)", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "Cal proporcionar tant el motor OCR de Docling com els idiomes o bé deixar-los buits.", "Brave Search API Key": "Clau API de Brave Search", "Bullet List": "Llista indexada", "Button ID": "ID del botó", "Button Label": "Etiqueta del botó", "Button Prompt": "Indicació del botó", "By {{name}}": "Per {{name}}", "Bypass Embedding and Retrieval": "Desactivar l'Embedding i el Retrieval", "Bypass Web Loader": "Ometre el càrregador web", "Cache Base Model List": "Llista de models base en memòria cau", "Calendar": "Calendari", "Call": "Trucada", "Call feature is not supported when using Web STT engine": "La funció de trucada no s'admet quan s'utilitza el motor Web STT", "Camera": "Càmera", "Cancel": "Cancel·lar", "Capabilities": "Capacitats", "Capture": "Captura", "Capture Audio": "<PERSON><PERSON><PERSON>", "Certificate Path": "Camí del certificat", "Change Password": "Canviar la contrasenya", "Channel Name": "Nom del canal", "Channels": "Canals", "Character": "Personatge", "Character limit for autocomplete generation input": "Límit de caràcters per a l'entrada de generació automàtica", "Chart new frontiers": "Traça noves fronteres", "Chat": "Xat", "Chat Background Image": "Imatge de fons del xat", "Chat Bubble UI": "Chat Bubble UI", "Chat Controls": "Controls de xat", "Chat direction": "Direcció del xat", "Chat ID": "ID del xat", "Chat Overview": "Vista general del xat", "Chat Permissions": "Permisos del xat", "Chat Tags Auto-Generation": "Generació automàtica d'etiquetes del xat", "Chats": "Xats", "Check Again": "Comprovar-ho de nou", "Check for updates": "Comprovar si hi ha actualitzacions", "Checking for updates...": "Comprovant actualitzacions...", "Choose a model before saving...": "Triar un model abans de desar...", "Chunk Overlap": "Solapament de blocs", "Chunk Size": "Mida del bloc", "Ciphers": "Xi<PERSON><PERSON><PERSON>", "Citation": "Cita", "Citations": "Cites", "Clear memory": "Esborrar la memòria", "Clear Memory": "Esborrar la memòria", "click here": "prem aquí", "Click here for filter guides.": "Clica aquí per filtrar les guies.", "Click here for help.": "Clica aquí per obtenir ajuda.", "Click here to": "Clic aquí per", "Click here to download user import template file.": "Fes clic aquí per descarregar l'arxiu de plantilla d'importació d'usuaris", "Click here to learn more about faster-whisper and see the available models.": "Clica aquí per obtenir més informació sobre faster-whisper i veure els models disponibles.", "Click here to see available models.": "Clica aquí per veure els models disponibles.", "Click here to select": "Clica aquí per seleccionar", "Click here to select a csv file.": "Clica aquí per seleccionar un fitxer csv.", "Click here to select a py file.": "Clica aquí per seleccionar un fitxer py.", "Click here to upload a workflow.json file.": "Clica aquí per pujar un arxiu workflow.json", "click here.": "clica aquí.", "Click on the user role button to change a user's role.": "Clica sobre el botó de rol d'usuari per canviar el rol d'un usuari.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Permís d'escriptura al porta-retalls denegat. Comprova els ajustos de navegador per donar l'accés necessari.", "Clone": "Clonar", "Clone Chat": "Clonar el xat", "Clone of {{TITLE}}": "Clon de {{TITLE}}", "Close": "<PERSON><PERSON>", "Close Banner": "<PERSON><PERSON>", "Close Configure Connection Modal": "Tancar la finestra de configuració de la connexió", "Close modal": "Tancar el modal", "Close settings modal": "Tancar el modal de configuració", "Close Sidebar": "Tancar la barra lateral", "Code Block": "Bloc de codi", "Code execution": "Execució de codi", "Code Execution": "Execució de Codi", "Code Execution Engine": "Motor d'execució de codi", "Code Execution Timeout": "Temps màxim d'execució de codi", "Code formatted successfully": "Codi formatat correctament", "Code Interpreter": "Intèrpret de codi", "Code Interpreter Engine": "Motor de l'intèrpret de codi", "Code Interpreter Prompt Template": "Plantilla de la indicació de l'intèrpret de codi", "Collapse": "Col·lapsar", "Collection": "Col·lecció", "Color": "Color", "ComfyUI": "ComfyUI", "ComfyUI API Key": "Configurar la clau API de ComfyUI", "ComfyUI Base URL": "URL base de ComfyUI", "ComfyUI Base URL is required.": "L'URL base de ComfyUI és obligatòria.", "ComfyUI Workflow": "Flux de treball de ComfyUI", "ComfyUI Workflow Nodes": "Nodes del flux de treball de ComfyUI", "Command": "<PERSON><PERSON><PERSON>", "Comment": "Comentari", "Completions": "Completaments", "Compress Images in Channels": "Comprimir imatges en els canals", "Concurrent Requests": "Peticions simultànies", "Configure": "Configurar", "Confirm": "Confirmar", "Confirm Password": "Confirmar la contrasenya", "Confirm your action": "Confirma la teva acció", "Confirm your new password": "Confirma la teva nova contrasenya", "Confirm Your Password": "Confirma la teva contrasenya", "Connect to your own OpenAI compatible API endpoints.": "Connecta als teus propis punts de connexió de l'API compatible amb OpenAI", "Connect to your own OpenAPI compatible external tool servers.": "Connecta als teus propis servidors d'eines externs compatibles amb OpenAPI", "Connection failed": "La connexió ha fallat", "Connection successful": "Connexió correcta", "Connection Type": "Tipus de connexió", "Connections": "Connexions", "Connections saved successfully": "Les connexions s'han desat correctament", "Connections settings updated": "Les preferències de les connexions s'han actualitzat", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "Restringeix l'esforç de raonament dels models de raonament. Només aplicable a models de raonament de proveïdors específics que donen suport a l'esforç de raonament.", "Contact Admin for WebUI Access": "Posa't en contacte amb l'administrador per accedir a WebUI", "Content": "Contingut", "Content Extraction Engine": "Motor d'extracció de contingut", "Continue Response": "Continuar la resposta", "Continue with {{provider}}": "Continuar amb {{provider}}", "Continue with Email": "Continuar amb el correu", "Continue with LDAP": "Continuar amb LDAP", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "Controlar com es divideix el text del missatge per a les sol·licituds TTS. 'Puntuació' divideix en frases, 'paràgrafs' divideix en paràgrafs i 'cap' manté el missatge com una cadena única.", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "Controlar la repetició de seqüències de tokens en el text generat. Un valor més alt (p. ex., 1,5) penalitzarà les repeticions amb més força, mentre que un valor més baix (p. ex., 1,1) serà més indulgent. A l'1, està desactivat.", "Controls": "Controls", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "Controla l'equilibri entre la coherència i la diversitat de la sortida. Un valor més baix donarà lloc a un text més enfocat i coherent.", "Copied": "Copiat", "Copied link to clipboard": "Enllaç copiat al portaretalls", "Copied shared chat URL to clipboard!": "S'ha copiat l'URL compartida al porta-retalls!", "Copied to clipboard": "Copiat al porta-retalls", "Copy": "Copiar", "Copy Formatted Text": "Copiar el text formatat", "Copy last code block": "Copiar l'últim bloc de codi", "Copy last response": "Copiar l'última resposta", "Copy link": "Copiar l'enllaç", "Copy Link": "Copiar l'enllaç", "Copy to clipboard": "Copiar al porta-retalls", "Copying to clipboard was successful!": "La còpia al porta-retalls s'ha realitzat correctament", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "CORS ha de ser configurat correctament pel proveïdor per permetre les sol·licituds d'Open WebUI", "Create": "<PERSON><PERSON><PERSON>", "Create a knowledge base": "Crear una base de coneixement", "Create a model": "Crear un model", "Create Account": "Crear un compte", "Create Admin Account": "Crear un compte d'Administrador", "Create Channel": "Crear un canal", "Create Folder": "<PERSON><PERSON><PERSON> carpeta", "Create Group": "<PERSON><PERSON><PERSON> grup", "Create Knowledge": "<PERSON><PERSON>r Coneixement", "Create new key": "<PERSON>rear una nova clau", "Create new secret key": "<PERSON>rear una nova clau secreta", "Create Note": "<PERSON><PERSON> nota", "Create your first note by clicking on the plus button below.": "Crea la teva primera nota prement sobre el botó 'més' inferior", "Created at": "<PERSON>reat el", "Created At": "<PERSON>reat el", "Created by": "Creat per", "CSV Import": "Importar CSV", "Ctrl+Enter to Send": "Ctrl+Enter per enviar", "Current Model": "Model actual", "Current Password": "Contrasenya actual", "Custom": "Personalitzat", "Custom description enabled": "Descripcions personalitzades habilitades", "Custom Parameter Name": "Nom del paràmetre personalitzat", "Custom Parameter Value": "Valor del paràmetre personalitzat", "Danger Zone": "Zona de perill", "Dark": "Fosc", "Database": "Base de dades", "Datalab Marker API": "API de Datalab Marker", "Datalab Marker API Key required.": "API de Datalab Marker requereix clau.", "DD/MM/YYYY": "DD/MM/YYYY", "December": "Desembre", "Default": "Per defecte", "Default (Open AI)": "<PERSON> defecte (Open AI)", "Default (SentenceTransformers)": "Per defecte (SentenceTransformers)", "Default action buttons will be used.": "S'utilitzaran els botons d'acció per defecte", "Default description enabled": "Descripcions per defecte habilitades", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "El mode predeterminat funciona amb una gamma més àmplia de models cridant a les eines una vegada abans de l'execució. El mode natiu aprofita les capacitats de crida d'eines integrades del model, però requereix que el model admeti aquesta funció de manera inherent.", "Default Model": "Model per defecte", "Default model updated": "Model per defecte actualitzat", "Default Models": "Models per defecte", "Default permissions": "<PERSON><PERSON><PERSON> per defecte", "Default permissions updated successfully": "Permisos per defecte actualitzats correctament", "Default Prompt Suggestions": "Suggeriments d'indicació per defecte", "Default to 389 or 636 if TLS is enabled": "Per defecte 389 o 636 si TLS està habilitat", "Default to ALL": "Per defecte TOTS", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "Per defecte, Segmented Retrieval per a l'extracció de contingut rellevant, es recomana en la majoria dels casos.", "Default User Role": "Rol d'usuari per defecte", "Delete": "Eliminar", "Delete a model": "Eliminar un model", "Delete All Chats": "Eliminar tots els xats", "Delete All Models": "Eliminar tots els models", "Delete chat": "Eliminar xat", "Delete Chat": "Eliminar xat", "Delete chat?": "Eliminar el xat?", "Delete folder?": "Eliminar la carpeta?", "Delete function?": "Eliminar funció?", "Delete Message": "Eliminar el missatge", "Delete message?": "Eliminar el missatge?", "Delete note?": "Eliminar la nota?", "Delete prompt?": "Eliminar indicació?", "delete this link": "Eliminar aquest enllaç", "Delete tool?": "Eliminar eina?", "Delete User": "Eliminar usuari", "Deleted {{deleteModelTag}}": "S'ha eliminat {{deleteModelTag}}", "Deleted {{name}}": "S'ha eliminat {{name}}", "Deleted User": "<PERSON><PERSON><PERSON> eliminat", "Deployment names are required for Azure OpenAI": "Els noms de desplegament són requerits per Azure OpenAI", "Describe Pictures in Documents": "Descriu les omatges en els documents", "Describe your knowledge base and objectives": "Descriu la teva base de coneixement i objectius", "Description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Detect Artifacts Automatically": "Detectar automàticament els artefactes", "Dictate": "Dictar", "Didn't fully follow instructions": "No s'han seguit les instruccions completament", "Direct": "Directe", "Direct Connections": "Connexions directes", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "Les connexions directes permeten als usuaris connectar-se als seus propis endpoints d'API compatibles amb OpenAI.", "Direct Tool Servers": "Servidors d'eines directes", "Disable Code Interpreter": "Deshabilitar l'interpret de codi", "Disable Image Extraction": "Deshabilitar l'extracció d'imatges", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "Desactiva l'extracció d'imatges del PDF. Si Utilitza LLM està habilitat, les imatges es descriuran automàticament. Per defecte és Fals.", "Disabled": "Deshabilitat", "Discover a function": "Descobrir una funció", "Discover a model": "Descobrir un model", "Discover a prompt": "Descobrir una indicació", "Discover a tool": "Descobrir una eina", "Discover how to use Open WebUI and seek support from the community.": "Descobreix com utilitzar Open WebUI i demana suport a la comunitat.", "Discover wonders": "<PERSON><PERSON><PERSON><PERSON>", "Discover, download, and explore custom functions": "<PERSON><PERSON><PERSON><PERSON>, descar<PERSON>gar i explorar funcions personalitzades", "Discover, download, and explore custom prompts": "<PERSON><PERSON><PERSON><PERSON>, descarregar i explorar indicacions personalitzades", "Discover, download, and explore custom tools": "<PERSON><PERSON><PERSON><PERSON>, descar<PERSON><PERSON> i explorar eines personalitzades", "Discover, download, and explore model presets": "<PERSON><PERSON><PERSON><PERSON>, descarregar i explorar models preconfigurats", "Display": "Mostrar", "Display Emoji in Call": "Mostrar emojis a la trucada", "Display Multi-model Responses in Tabs": "Mostrar respostes multi-model a les pestanyes", "Display the username instead of You in the Chat": "Mostrar el nom d'usuari en lloc de 'Tu' al xat", "Displays citations in the response": "Mostra les referències a la resposta", "Dive into knowledge": "Aprofundir en el coneixement", "Do not install functions from sources you do not fully trust.": "No instal·lis funcions de fonts en què no confiïs plenament.", "Do not install tools from sources you do not fully trust.": "No instal·lis eines de fonts en què no confiïs plenament.", "Docling": "<PERSON><PERSON>", "Docling Server URL required.": "La URL del servidor Docling és necessària", "Document": "Document", "Document Intelligence": "Document Intelligence", "Document Intelligence endpoint and key required.": "Fa falta un punt de connexió i una clau per a Document Intelligence.", "Documentation": "Documentació", "Documents": "Documents", "does not make any external connections, and your data stays securely on your locally hosted server.": "no realitza connexions externes, i les teves dades romanen segures al teu servidor allotjat localment.", "Domain Filter List": "Llista de filtre de dominis", "Don't have an account?": "No tens un compte?", "don't install random functions from sources you don't trust.": "no instal·lis funcions aleatòries de fonts en què no confiïs.", "don't install random tools from sources you don't trust.": "no instal·lis eines aleatòries de fonts en què no confiïs.", "Don't like the style": "No t'agrada l'estil?", "Done": "Fet", "Download": "<PERSON><PERSON><PERSON><PERSON>", "Download as SVG": "Descarrega com a SVG", "Download canceled": "Descàrrega cancel·lada", "Download Database": "Descarregar la base de dades", "Drag and drop a file to upload or select a file to view": "Arrossega un fitxer per pujar-lo o selecciona'n un per visualitzar-lo", "Draw": "Dibuixar", "Drop any files here to upload": "Arrossega aquí qualsevol fitxer per pujar-lo", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "p. ex. '30s','10m'. Les unitats de temps vàlides són 's', 'm', 'h'.", "e.g. \"json\" or a JSON schema": "p. ex. \"json\" o un esquema JSON", "e.g. 60": "p. ex. 60", "e.g. A filter to remove profanity from text": "p. ex. Un filtre per eliminar paraules malsonants del text", "e.g. en": "p. ex. en", "e.g. My Filter": "p. ex. El meu filtre", "e.g. My Tools": "p. ex. Les meves eines", "e.g. my_filter": "p. ex. els_meus_filtres", "e.g. my_tools": "p. ex. les_meves_eines", "e.g. pdf, docx, txt": "p. ex. pdf, docx, txt", "e.g. Tools for performing various operations": "p. ex. Eines per dur a terme operacions", "e.g., 3, 4, 5 (leave blank for default)": "p. ex. 3, 4, 5 (deixa-ho en blanc per utilitzar el per defecte)", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults)": "p. ex. audio/wav,audio/mpeg,video/*  (deixa-ho en blanc per utilitzar el per defecte)", "e.g., en-US,ja-JP (leave blank for auto-detect)": "p. ex. en-US, ja-JP, ca-ES (deixa-ho en blanc per detecció automàtica)", "e.g., westus (leave blank for eastus)": "p. ex. westus (deixa-ho en blanc per a eastus)", "Edit": "<PERSON><PERSON>", "Edit Arena Model": "Editar model de l'Arena", "Edit Channel": "Editar el canal", "Edit Connection": "Editar la connexió", "Edit Default Permissions": "Editar el permisos per defecte", "Edit Folder": "<PERSON><PERSON> la <PERSON>a", "Edit Memory": "Editar la memòria", "Edit User": "<PERSON>ar l'usuari", "Edit User Group": "Editar el grup d'usuaris", "Edited": "Editat", "Editing": "Editant", "Eject": "Expulsar", "ElevenLabs": "ElevenLabs", "Email": "<PERSON><PERSON>u elect<PERSON>ò<PERSON>", "Embark on adventures": "Embarcar en aventures", "Embedding": "Incrustació", "Embedding Batch Size": "Mida del lot d'incrustació", "Embedding Model": "Model d'incrust<PERSON><PERSON>", "Embedding Model Engine": "Motor de model d'incrustació", "Embedding model set to \"{{embedding_model}}\"": "Model d'incrustació configurat a \"{{embedding_model}}\"", "Enable API Key": "Activar la Clau API", "Enable autocomplete generation for chat messages": "Activar la generació automàtica per als missatges del xat", "Enable Code Execution": "Permetre l'execució de codi", "Enable Code Interpreter": "Activar l'intèrpret de codi", "Enable Community Sharing": "Activar l'ús compartit amb la comunitat", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Activar el bloqueig de memòria (mlock) per evitar que les dades del model s'intercanviïn fora de la memòria RAM. Aquesta opció bloqueja el conjunt de pàgines de treball del model a la memòria RAM, assegurant-se que no s'intercanviaran al disc. Això pot ajudar a mantenir el rendiment evitant errors de pàgina i garantint un accés ràpid a les dades.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Activar l'assignació de memòria (mmap) per carregar les dades del model. Aquesta opció permet que el sistema utilitzi l'emmagatzematge en disc com a extensió de la memòria RAM tractant els fitxers de disc com si estiguessin a la memòria RAM. Això pot millorar el rendiment del model permetent un accés més ràpid a les dades. Tanmateix, és possible que no funcioni correctament amb tots els sistemes i pot consumir una quantitat important d'espai en disc.", "Enable Message Rating": "Permetre la qualificació de missatges", "Enable Mirostat sampling for controlling perplexity.": "Permetre el mostreig de Mirostat per controlar la perplexitat", "Enable New Sign Ups": "Permetre nous registres", "Enabled": "Habilitat", "Endpoint URL": "URL de connexió", "Enforce Temporary Chat": "Forçar els xats temporals", "Enhance": "Millorar", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Assegura't que els teus fitxers CSV inclouen 4 columnes en aquest ordre: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Contrasenya, Rol.", "Enter {{role}} message here": "Introdueix aquí el missatge de {{role}}", "Enter a detail about yourself for your LLMs to recall": "Introdueix un detall sobre tu què els teus models de llenguatge puguin recordar", "Enter a title for the pending user info overlay. Leave empty for default.": "Introdueix un títol per a la finestra de dades d'usuari pendent. Deixa buit per a valor per defecte.", "Enter a watermark for the response. Leave empty for none.": "Introdueix una marca d'aigua per a la resposta. Deixa-ho buit per a cap.", "Enter api auth string (e.g. username:password)": "Entra la cadena d'autenticació api (p. ex. nom d'usuari:contrasenya)", "Enter Application DN": "Introdueix el DN d'aplicació", "Enter Application DN Password": "Introdueix la contrasenya del DN d'aplicació", "Enter Bing Search V7 Endpoint": "Introdueix el punt de connexió de Bing Search V7", "Enter Bing Search V7 Subscription Key": "Introdueix la clau de subscripció de Bing Search V7", "Enter Bocha Search API Key": "Introdueix la clau API de Bocha Search", "Enter Brave Search API Key": "Introdueix la clau API de Brave Search", "Enter certificate path": "Introdueix el camí del certificat", "Enter CFG Scale (e.g. 7.0)": "Entra l'escala CFG (p. ex. 7.0)", "Enter Chunk Overlap": "Introdueix la mida de solapament de blocs", "Enter Chunk Size": "Introdueix la mida del bloc", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "Introdueix parelles de \"token:valor de biaix\" separats per comes (exemple: 5432:100, 413:-100)", "Enter Config in JSON format": "Introdueix la configuració en format JSON", "Enter content for the pending user info overlay. Leave empty for default.": "Introdueix el contingut per a la finestra de dades d'usuari pendent. Deixa-ho buit per a valor per defecte.", "Enter Datalab Marker API Base URL": "Introdueix l'URL de base de l'API Datalab Marker", "Enter Datalab Marker API Key": "Introdueix la clau API de Datalab Marker", "Enter description": "Introdueix la descripció", "Enter Docling OCR Engine": "Introdueix el motor OCR de Docling", "Enter Docling OCR Language(s)": "Introdueix els idiomes per a l'OCR de Docling", "Enter Docling Server URL": "Introdueix la URL del servidor Docling", "Enter Document Intelligence Endpoint": "Introdueix el punt de connexió de Document Intelligence", "Enter Document Intelligence Key": "Introdueix la clau de Document Intelligence", "Enter domains separated by commas (e.g., example.com,site.org)": "Introdueix els dominis separats per comes (p. ex. example.com,site.org)", "Enter Exa API Key": "Introdueix la clau API de d'EXA", "Enter External Document Loader API Key": "Introdueix la clau API de Document Loader", "Enter External Document Loader URL": "Introdueix la URL de Document Loader", "Enter External Web Loader API Key": "Introdueix la clau API d'External Web Loader", "Enter External Web Loader URL": "Introdueix la URL d'External Web Loader", "Enter External Web Search API Key": "Introdueix la clau API d'External Web Search", "Enter External Web Search URL": "Introdueix la URL d'External Web Search", "Enter Firecrawl API Base URL": "Introdueix la URL base de Firecrawl API", "Enter Firecrawl API Key": "Introdueix la clau API de Firecrawl", "Enter folder name": "Introdueix el nom de la carpeta", "Enter Github Raw URL": "Introdueix l'URL en brut de Github", "Enter Google PSE API Key": "Introdueix la clau API de Google PSE", "Enter Google PSE Engine Id": "Introdueix l'identificador del motor PSE de Google", "Enter Image Size (e.g. 512x512)": "Introdueix la mida de la imatge (p. ex. 512x512)", "Enter Jina API Key": "Introdueix la clau API de Jin<PERSON>", "Enter JSON config (e.g., {\"disable_links\": true})": "Introdueix la configuració JSON (per exemple, {\"disable_links\": true})", "Enter Jupyter Password": "Introdueix la contrasenya de Jupyter", "Enter Jupyter Token": "Introdueix el token de Jupyter", "Enter Jupyter URL": "Introdueix la URL de Jupyter", "Enter Kagi Search API Key": "Introdueix la clau API de Kagi Search", "Enter Key Behavior": "Introdueix el comportament de clau", "Enter language codes": "Introdueix els codis de llenguatge", "Enter Mistral API Key": "Entra la clau API de Mistral", "Enter Model ID": "Introdueix l'identificador del model", "Enter model tag (e.g. {{modelTag}})": "Introdueix l'etiqueta del model (p. ex. {{modelTag}})", "Enter Mojeek Search API Key": "Introdueix la clau API de Mojeek Search", "Enter name": "Introdueix el nom", "Enter New Password": "Introdueix un nova contrasenya", "Enter Number of Steps (e.g. 50)": "Introdueix el nombre de passos (p. ex. 50)", "Enter Perplexity API Key": "Introdueix la clau API de Perplexity", "Enter Playwright Timeout": "Introdueix el timeout de Playwright", "Enter Playwright WebSocket URL": "Introdueix la URL de Playwright WebSocket", "Enter proxy URL (e.g. **************************:port)": "Entra l'URL (p. ex. **************************:port)", "Enter reasoning effort": "Introdueix l'esforç de raonament", "Enter Sampler (e.g. Euler a)": "Introdueix el mostrejador (p. ex. Euler a)", "Enter Scheduler (e.g. Karras)": "Entra el programador (p. ex. <PERSON>)", "Enter Score": "Introdueix la puntuació", "Enter SearchApi API Key": "Introdueix la clau API SearchApi", "Enter SearchApi Engine": "Introdueix el motor SearchApi", "Enter Searxng Query URL": "Introdueix l'URL de consulta de Searxng", "Enter Seed": "Introdueix la llavor", "Enter SerpApi API Key": "Introdueix la clau API SerpApi", "Enter SerpApi Engine": "Introdueix el motor API SerpApi", "Enter Serper API Key": "Introdueix la clau API <PERSON>", "Enter Serply API Key": "Introdueix la clau API Serply", "Enter Serpstack API Key": "Introdueix la clau API <PERSON>", "Enter server host": "Introdueix el servidor", "Enter server label": "Introdueix l'etiqueta del servidor", "Enter server port": "Introdueix el port del servidor", "Enter Sougou Search API sID": "Introdueix el sID de l'API de Sougou Search", "Enter Sougou Search API SK": "Introdueix l'SK de l'API de Sougou Search", "Enter stop sequence": "Introdueix la seqüència de parada", "Enter system prompt": "Introdueix la indicació de sistema", "Enter system prompt here": "Entra la indicació de sistema aquí", "Enter Tavily API Key": "Introdueix la clau API <PERSON>", "Enter Tavily Extract Depth": "Introdueix la profunditat d'extracció de Tavily", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "Entra la URL pública de WebUI. Aquesta URL s'utilitzarà per generar els enllaços en les notificacions.", "Enter the URL of the function to import": "Introdueix la URL de la funció a importar", "Enter the URL to import": "Introdueix la URL a importar", "Enter Tika Server URL": "Introdueix l'URL del servidor Tika", "Enter timeout in seconds": "Entra el temps màxim en segons", "Enter to Send": "Enter per enviar", "Enter Top K": "Introdueix Top K", "Enter Top K Reranker": "Introdueix el Top K Reranker", "Enter URL (e.g. http://127.0.0.1:7860/)": "Introdueix l'URL (p. ex. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Introdueix l'URL (p. ex. http://localhost:11434)", "Enter Yacy Password": "Introdueix la contrassenya de Yacy", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "Introdueix la URL de Yacy (p. ex. http://yacy.example.com:8090)", "Enter Yacy Username": "Introdueix l'usuari de Yacy", "Enter your current password": "Introdueix la teva contrasenya actual", "Enter Your Email": "Introdueix el teu correu electrònic", "Enter Your Full Name": "Introdueix el teu nom complet", "Enter your message": "Introdueix el teu missatge", "Enter your name": "Entra el teu nom", "Enter Your Name": "Entra el teu nom", "Enter your new password": "Introdueix la teva nova contrasenya", "Enter Your Password": "Introdueix la teva contrasenya", "Enter Your Role": "Introdueix el teu rol", "Enter Your Username": "Introdueix el teu nom d'usuari", "Enter your webhook URL": "Entra la URL del webhook", "Error": "Error", "ERROR": "ERROR", "Error accessing Google Drive: {{error}}": "Error en accedir a Google Drive: {{error}}", "Error accessing media devices.": "Error en accedir als dispositius multimèdia", "Error starting recording.": "Error en començar a enregistrar", "Error unloading model: {{error}}": "Error en descarregar el model: {{error}}", "Error uploading file: {{error}}": "Error en pujar l'arxiu: {{error}}", "Evaluations": "Avaluacions", "Everyone": "Tothom", "Exa API Key": "Clau API d'EXA", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Exemple: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Exemple: TOTS", "Example: mail": "Exemple: mail", "Example: ou=users,dc=foo,dc=example": "Exemple: ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "Exemple: sAMAccountName o uid o userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "S'ha superat el nombre de places a la vostra llicència. Poseu-vos en contacte amb el servei d'assistència per augmentar el nombre de places.", "Exclude": "Ex<PERSON>loure", "Execute code for analysis": "Executar el codi per analitzar-lo", "Executing **{{NAME}}**...": "Executant **{{NAME}}**...", "Expand": "Expandir", "Experimental": "Experimental", "Explain": "Explicar", "Explore the cosmos": "Explorar el cosmos", "Export": "Exportar", "Export All Archived Chats": "Exportar tots els xats arxivats", "Export All Chats (All Users)": "Exportar tots els xats (Tots els usuaris)", "Export chat (.json)": "Exportar el xat (.json)", "Export Chats": "Exportar els xats", "Export Config to JSON File": "Exportar la configuració a un arxiu JSON", "Export Functions": "Exportar funcions", "Export Models": "Exportar els models", "Export Presets": "Exportar les configuracions", "Export Prompt Suggestions": "Exportar els suggeriments d'indicació", "Export Prompts": "Exportar les indicacions", "Export to CSV": "Exportar a CSV", "Export Tools": "Exportar les eines", "Export Users": "Exportar els usuaris", "External": "Extern", "External Document Loader URL required.": "Fa falta la URL per a Document Loader", "External Task Model": "Model de tasques extern", "External Web Loader API Key": "Clau API d'External Web Loader", "External Web Loader URL": "URL d'External Web Loader", "External Web Search API Key": "Clau API d'External Web Search", "External Web Search URL": "URL d'External Web Search", "Fade Effect for Streaming Text": "Efecte de fos a negre per al text en streaming", "Failed to add file.": "No s'ha pogut afegir l'arxiu.", "Failed to connect to {{URL}} OpenAPI tool server": "No s'ha pogut connecta al servidor d'eines OpenAPI {{URL}}", "Failed to copy link": "No s'ha pogut copiar l'enllaç", "Failed to create API Key.": "No s'ha pogut crear la clau API.", "Failed to delete note": "No s'ha pogut eliminar la nota", "Failed to extract content from the file: {{error}}": "No s'ha pogut extreure el contingut del fitxer: {{error}}", "Failed to extract content from the file.": "No s'ha pogut extreure el contingut del fitxer", "Failed to fetch models": "No s'han pogut obtenir els models", "Failed to generate title": "No s'ha pogut generar el títol", "Failed to load chat preview": "No s'ha pogut carregar la previsualització del xat", "Failed to load file content.": "No s'ha pogut carregar el contingut del fitxer", "Failed to read clipboard contents": "No s'ha pogut llegir el contingut del porta-retalls", "Failed to save connections": "No s'han pogut desar les connexions", "Failed to save models configuration": "No s'ha pogut desar la configuració dels models", "Failed to update settings": "No s'han pogut actualitzar les preferències", "Failed to upload file.": "No s'ha pogut pujar l'arxiu.", "Features": "Característiques", "Features Permissions": "Permisos de les característiques", "February": "<PERSON><PERSON>", "Feedback Details": "Detalls del retorn", "Feedback History": "<PERSON><PERSON><PERSON><PERSON>", "Feedbacks": "<PERSON><PERSON><PERSON>", "Feel free to add specific details": "Sent-te lliure d'afegir detalls específics", "File": "<PERSON><PERSON><PERSON><PERSON>", "File added successfully.": "<PERSON>'ar<PERSON><PERSON> s'ha afegit correctament.", "File content updated successfully.": "El contingut de l'arxiu s'ha actualitzat correctament.", "File Mode": "Mode d'arxiu", "File not found.": "No s'ha trobat l'arxiu.", "File removed successfully.": "Ar<PERSON>u eliminat correctament.", "File size should not exceed {{maxSize}} MB.": "La mida del fitxer no ha de superar els {{maxSize}} MB.", "File Upload": "<PERSON><PERSON><PERSON> a<PERSON><PERSON>u", "File uploaded successfully": "arxiu pujat satisfactòriament", "Files": "<PERSON><PERSON><PERSON><PERSON>", "Filter": "Filtre", "Filter is now globally disabled": "El filtre ha estat desactivat globalment", "Filter is now globally enabled": "El filtre ha estat activat globalment", "Filters": "Filtres", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "S'ha detectat la suplantació d'identitat de l'empremta digital: no es poden utilitzar les inicials com a avatar. S'estableix la imatge de perfil predeterminada.", "Firecrawl API Base URL": "URL de l'API de base de Firecrawl", "Firecrawl API Key": "Clau API de Firecrawl", "Floating Quick Actions": "Accions ràpides flotants", "Focus chat input": "Estableix el focus a l'entrada del xat", "Folder deleted successfully": "Carpeta eliminada correctament", "Folder Name": "Nom de la carpeta", "Folder name cannot be empty.": "El nom de la carpeta no pot ser buit.", "Folder name updated successfully": "Nom de la carpeta actualitzat correctament", "Folder updated successfully": "Carpeta actualitazda correctament", "Follow up": "<PERSON><PERSON><PERSON>", "Follow Up Generation": "Generació de seguiment", "Follow Up Generation Prompt": "Indicació per a la generació de seguiment", "Follow-Up Auto-Generation": "Generació automàtica de seguiment", "Followed instructions perfectly": "S'han seguit les instruccions perfectament", "Force OCR": "Forçar OCR", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "Forçar OCR a totes les pàgines del PDF. Això pot portar a resultats pitjors si tens un bon text als teus PDF. Per defecte és Fals", "Forge new paths": "Crea nous camins", "Form": "Formulari", "Format Lines": "Formatar les línies", "Format the lines in the output. Defaults to False. If set to True, the lines will be formatted to detect inline math and styles.": "Formata les línies a la sortida. <PERSON> defecte, és Fals. Si es defineix com a Cert, les línies es formataran per detectar matemàtiques i estils en línia.", "Format your variables using brackets like this:": "Formata les teves variables utilitzant claudàtors així:", "Forwards system user session credentials to authenticate": "Envia les credencials de l'usuari del sistema per autenticar", "Full Context Mode": "Mode de context complert", "Function": "<PERSON><PERSON><PERSON>", "Function Calling": "Crida a funcions", "Function created successfully": "La funció s'ha creat correctament", "Function deleted successfully": "La funció s'ha eliminat correctament", "Function Description": "Descripció de la funció", "Function ID": "ID de la funció", "Function imported successfully": "La funció s'ha importat correctament", "Function is now globally disabled": "La funció ha estat desactivada globalment", "Function is now globally enabled": "La funció ha estat activada globalment", "Function Name": "Nom de la funció", "Function updated successfully": "La funció s'ha actualitzat correctament", "Functions": "Funcions", "Functions allow arbitrary code execution.": "Les funcions permeten l'execució de codi arbitrari.", "Functions imported successfully": "Les funcions s'han importat correctament", "Gemini": "Gemini", "Gemini API Config": "Configuració de Gemini API", "Gemini API Key is required.": "La clau API de Gemini és necessària", "General": "General", "Generate": "Generar", "Generate an image": "Generar una imatge", "Generate Image": "Generar imatge", "Generate prompt pair": "Generar parella d'indicació", "Generating search query": "Generant consulta", "Generating...": "Generant...", "Get information on {{name}} in the UI": "<PERSON><PERSON><PERSON><PERSON> informaci<PERSON> de {{name}} a l'interficie d'usuari", "Get started": "<PERSON><PERSON><PERSON><PERSON>", "Get started with {{WEBUI_NAME}}": "Comença amb {{WEBUI_NAME}}", "Global": "Global", "Good Response": "<PERSON>a resposta", "Google Drive": "Google Drive", "Google PSE API Key": "Clau API PSE de Google", "Google PSE Engine Id": "Identificador del motor PSE de Google", "Group created successfully": "El grup s'ha creat correctament", "Group deleted successfully": "El grup s'ha eliminat correctament", "Group Description": "Descripció del grup", "Group Name": "Nom del grup", "Group updated successfully": "Grup actualitzat correctament", "Groups": "Grups", "H1": "H1", "H2": "H2", "H3": "H3", "Haptic Feedback": "<PERSON><PERSON><PERSON> h<PERSON>", "Hello, {{name}}": "<PERSON><PERSON>, {{name}}", "Help": "<PERSON><PERSON><PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "Ajuda'ns a crear la millor taula de classificació de la comunitat compartint el teu historial de comentaris!", "Hex Color": "Color hexadecimal", "Hex Color - Leave empty for default color": "Color hexadecimal - Deixar buit per a color per defecte", "Hide": "<PERSON><PERSON>", "Hide from Sidebar": "Amagar de la barra lateral", "Hide Model": "Amagar el model", "High Contrast Mode": "Mode d'alt contrast", "Home": "Inici", "Host": "<PERSON><PERSON><PERSON>", "How can I help you today?": "Com et puc ajudar avui?", "How would you rate this response?": "Com avaluaries aquesta resposta?", "HTML": "HTML", "Hybrid Search": "Cerca híbrida", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Afirmo que he llegit i entenc les implicacions de la meva acció. Soc conscient dels riscos associats a l'execució de codi arbitrari i he verificat la fiabilitat de la font.", "ID": "ID", "iframe Sandbox Allow Forms": "Permetre formularis sandbox iframe", "iframe Sandbox Allow Same Origin": "Permetre same-origin sandbox iframe", "Ignite curiosity": "Despertar la curiositat", "Image": "Imatge", "Image Compression": "Compressió d'imatges", "Image Compression Height": "Alçada de la compressió d'imatges", "Image Compression Width": "Amplada de la compressió d'imatges", "Image Generation": "Generac<PERSON><PERSON>'im<PERSON>", "Image Generation (Experimental)": "<PERSON>rac<PERSON><PERSON> (Experimental)", "Image Generation Engine": "Motor de generació d'imatges", "Image Max Compression Size": "Mida màxima de la compressió d'imatges", "Image Max Compression Size height": "Mida màxima de l'alçada de la compressió d'imatges", "Image Max Compression Size width": "Mida màxima de l'amplada de la compressió d'imatges", "Image Prompt Generation": "Generació d'indicacions d'imatge", "Image Prompt Generation Prompt": "Indicació per a la generació d'indicacions d'imatge", "Image Settings": "Preferències d'imatges", "Images": "Imatges", "Import": "Importar", "Import Chats": "Importar xats", "Import Config from JSON File": "Importar la configuració des d'un arxiu JSON", "Import From Link": "Importar des d'un enllaç", "Import Functions": "Importar funcions", "Import Models": "Importar models", "Import Notes": "Importar nota", "Import Presets": "Importar configuracions", "Import Prompt Suggestions": "Importar suggeriments d'indicacions", "Import Prompts": "Importar indicacions", "Import Tools": "Importar eines", "Include": "<PERSON><PERSON><PERSON>", "Include `--api-auth` flag when running stable-diffusion-webui": "Inclou `--api-auth` quan executis stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "Inclou `--api` quan executis stable-diffusion-webui", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "Influeix amb la rapidesa amb què l'algoritme respon als comentaris del text generat. Una taxa d'aprenentatge més baixa donarà lloc a ajustos més lents, mentre que una taxa d'aprenentatge més alta farà que l'algorisme sigui més sensible.", "Info": "Informació", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "Injectar tot el contingut com a context per a un processament complet, això es recomana per a consultes complexes.", "Input": "Entrada", "Input commands": "En<PERSON> comandes", "Input Variables": "Variables d'entrada", "Insert": "Inserir", "Insert Follow-Up Prompt to Input": "Inserir un missatge de seguiment per a l'entrada", "Insert Prompt as Rich Text": "Inserir la indicació com a Text Ric", "Install from Github URL": "Instal·lar des de l'URL de Github", "Instant Auto-Send After Voice Transcription": "Enviament automàtic després de la transcripció de veu", "Integration": "Integració", "Interface": "Interfície", "Invalid file content": "Continguts del fitxer no vàlids", "Invalid file format.": "Format d'arxiu no vàlid.", "Invalid JSON file": "Arxiu JSON no vàlid", "Invalid JSON format in Additional Config": "Format JSON no vàlid a la configuració addicional", "Invalid Tag": "Etiqueta no vàlida", "is typing...": "està escrivint...", "Italic": "Cursiva", "January": "Gener", "Jina API Key": "<PERSON><PERSON> <PERSON>", "join our Discord for help.": "uneix-te al nostre Discord per obtenir ajuda.", "JSON": "JSON", "JSON Preview": "Vista prèvia del document JSON", "July": "<PERSON><PERSON>", "June": "<PERSON><PERSON>", "Jupyter Auth": "Autentica<PERSON><PERSON>", "Jupyter URL": "URL de Jupyter", "JWT Expiration": "Caducitat del JWT", "JWT Token": "Token JWT", "Kagi Search API Key": "Clau API de Kagi Search", "Keep Follow-Up Prompts in Chat": "Mantenir els missatges de seguiment en el xat", "Keep in Sidebar": "Mantenir a la barra lateral", "Key": "<PERSON><PERSON>", "Keyboard shortcuts": "Dreceres de teclat", "Knowledge": "Coneixement", "Knowledge Access": "Accés al coneixement", "Knowledge Base": "Base de coneixement", "Knowledge created successfully.": "Coneixement creat correctament.", "Knowledge deleted successfully.": "Coneixement eliminat correctament.", "Knowledge Public Sharing": "Compartir públicament el Coneixement", "Knowledge reset successfully.": "Coneixement restablert correctament.", "Knowledge updated successfully": "Coneixement actualitzat correctament.", "Kokoro.js (Browser)": "Kokoro.js (Navegador)", "Kokoro.js Dtype": "Kokoro.js Dtype", "Label": "Etiqueta", "Landing Page Mode": "Mode de la pàgina d'entrada", "Language": "Idioma", "Language Locales": "Localització d'idiomes", "Last Active": "Activitat recent", "Last Modified": "Modificació", "Last reply": "<PERSON><PERSON> resposta", "LDAP": "LDAP", "LDAP server updated": "Servidor LDAP actualitzat", "Leaderboard": "Tauler de classificació", "Learn More": "Aprendre'n més", "Learn more about OpenAPI tool servers.": "Aprèn més sobre els servidors d'eines OpenAPI", "Leave empty for no compression": "Deixar-ho buit per no comprimir", "Leave empty for unlimited": "Deixar-ho buit per il·limitat", "Leave empty to include all models from \"{{url}}\" endpoint": "Deixar-ho buit per incloure tots els models del punt de connexió \"{{url}}\"", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "Deixar-ho buit per incloure tots els models del punt de connexió \"{{url}}/api/tags\"", "Leave empty to include all models from \"{{url}}/models\" endpoint": "Deixar-ho buit per incloure tots els models del punt de connexió \"{{url}}/models\"", "Leave empty to include all models or select specific models": "Deixa-ho en blanc per incloure tots els models o selecciona models específics", "Leave empty to use the default prompt, or enter a custom prompt": "Deixa-ho en blanc per utilitzar la indicació predeterminada o introdueix una indicació personalitzada", "Leave model field empty to use the default model.": "Deixa el camp de model buit per utilitzar el model per defecte.", "lexical": "lèxic", "License": "Llicència", "Lift List": "Aixecar la llista", "Light": "<PERSON><PERSON>", "Listening...": "Escoltant...", "Llama.cpp": "Llama.cpp", "LLMs can make mistakes. Verify important information.": "Els models de llenguatge poden cometre errors. Verifica la informació important.", "Loader": "Carregador", "Loading Kokoro.js...": "Carregant Kokoro.js", "Local": "Local", "Local Task Model": "Model local de tasques", "Location access not allowed": "Accés a la ubicació no permesa", "Lost": "<PERSON><PERSON><PERSON>", "LTR": "LTR", "Made by Open WebUI Community": "Creat per la Comunitat OpenWebUI", "Make password visible in the user interface": "Fer que la contrasenya sigui visible a la interficie d'usuari", "Make sure to enclose them with": "Assegura't d'envoltar-los amb", "Make sure to export a workflow.json file as API format from ComfyUI.": "Assegura't d'exportar un fitxer workflow.json com a format API des de ComfyUI.", "Manage": "Gestionar", "Manage Direct Connections": "Gestionar les connexions directes", "Manage Models": "Gestionar els models", "Manage Ollama": "Gest<PERSON><PERSON>", "Manage Ollama API Connections": "Gestionar les connexions a l'API d'Ollama", "Manage OpenAI API Connections": "Gestionar les connexions a l'API d'OpenAI", "Manage Pipelines": "Gestionar les Pipelines", "Manage Tool Servers": "Gestionar els servidors d'eines", "March": "Març", "Markdown": "<PERSON><PERSON>", "Markdown (Header)": "Markdown (encapçalament)", "Max Speakers": "Nombre màxim d'altaveus", "Max Upload Count": "Nombre màxim de càrregues", "Max Upload Size": "Mida màxima de càrrega", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Es poden descarregar un màxim de 3 models simultàniament. Si us plau, prova-ho més tard.", "May": "<PERSON><PERSON>", "Memories accessible by LLMs will be shown here.": "Les memòries accessibles pels models de llenguatge es mostraran aquí.", "Memory": "Memò<PERSON>", "Memory added successfully": "Memòria afegida correctament", "Memory cleared successfully": "Memòria eliminada correctament", "Memory deleted successfully": "Memòria eliminada correctament", "Memory updated successfully": "Memòria actualitzada correctament", "Merge Responses": "Fusionar les respostes", "Merged Response": "Resposta combinada", "Message rating should be enabled to use this feature": "La classificació dels missatges s'hauria d'activar per utilitzar aquesta funció", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Els missatges enviats després de crear el teu enllaç no es compartiran. Els usuaris amb l'URL podran veure el xat compartit.", "Microsoft OneDrive": "Microsoft OneDrive", "Microsoft OneDrive (personal)": "Microsoft OneDrive (personal)", "Microsoft OneDrive (work/school)": "Microsoft OneDrive (feina/escola)", "Mistral OCR": "Mistral OCR", "Mistral OCR API Key required.": "És necessària la clau API de Mistral OCR", "Model": "Model", "Model '{{modelName}}' has been successfully downloaded.": "El model '{{modelName}}' s'ha descarregat correctament.", "Model '{{modelTag}}' is already in queue for downloading.": "El model '{{modelTag}}' ja està en cua per ser descarregat.", "Model {{modelId}} not found": "No s'ha trobat el model {{modelId}}", "Model {{modelName}} is not vision capable": "El model {{modelName}} no és capaç de visió", "Model {{name}} is now {{status}}": "El model {{name}} ara és {{status}}", "Model {{name}} is now hidden": "El model {{name}} està ara amagat", "Model {{name}} is now visible": "El model {{name}} està ara visible", "Model accepts file inputs": "El model accepta entrada de fitxers", "Model accepts image inputs": "El model accepta entrades d'imatge", "Model can execute code and perform calculations": "El model pot executar codi i realitzar càlculs", "Model can generate images based on text prompts": "El model pot generar imatges basades en les indicacions de text", "Model can search the web for information": "El model pot cercar informació a la web", "Model created successfully!": "Model creat correctament", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "S'ha detectat el camí del sistema de fitxers del model. És necessari un nom curt del model per actualitzar, no es pot continuar.", "Model Filtering": "Filtrat de models", "Model ID": "Identificador del model", "Model ID is required.": "L'ID del model és necessari", "Model IDs": "Identificadors del model", "Model Name": "Nom del model", "Model Name is required.": "El nom del model és necessari", "Model not selected": "Model no seleccionat", "Model Params": "Paràmetres del model", "Model Permissions": "Permisos dels models", "Model unloaded successfully": "El model s'ha descarregat correctament", "Model updated successfully": "Model actualitzat correctament", "Model(s) do not support file upload": "El model no permet la pujada d'arxius", "Modelfile Content": "Contingut del Modelfile", "Models": "Models", "Models Access": "Accés als models", "Models configuration saved successfully": "La configuració dels models s'ha desat correctament", "Models Public Sharing": "Compartició pública de models", "Mojeek Search API Key": "Clau API de Mojeek Search", "more": "més", "More": "Més", "More Concise": "<PERSON><PERSON> pre<PERSON>", "More Options": "Més opcions", "Name": "Nom", "Name your knowledge base": "Anomena la teva base de coneixement", "Native": "<PERSON><PERSON>", "New Button": "<PERSON><PERSON><PERSON> nou", "New Chat": "Nou xat", "New Folder": "Nova carpeta", "New Function": "Nova funció", "New Note": "Nova nota", "New Password": "Nova contrasenya", "New Tool": "Nova eina", "new-channel": "nou-canal", "Next message": "<PERSON><PERSON><PERSON> següent", "No chats found": "No s'han trobat xats", "No chats found for this user.": "No s'han trobat xats per a aquest usuari.", "No chats found.": "No s'ha trobat xats.", "No content": "No hi ha contingut", "No content found": "No s'ha trobat contingut", "No content found in file.": "No s'ha trobat contingut en el fitxer.", "No content to speak": "No hi ha contingut per parlar", "No distance available": "No hi ha distància disponible", "No feedbacks found": "No s'han trobat comentaris", "No file selected": "No s'ha escollit cap fitxer", "No groups with access, add a group to grant access": "No hi ha cap grup amb accés, afegeix un grup per concedir accés", "No HTML, CSS, or JavaScript content found.": "No s'ha trobat contingut HTML, CSS o JavaScript.", "No inference engine with management support found": "No s'ha trobat un motor d'inferència amb suport de gestió", "No knowledge found": "No s'ha trobat Coneixement", "No memories to clear": "No hi ha memòries per netejar", "No model IDs": "No hi ha IDs de model", "No models found": "No s'han trobat models", "No models selected": "No s'ha seleccionat cap model", "No Notes": "No hi ha notes", "No results found": "No s'han trobat resultats", "No search query generated": "No s'ha generat cap consulta", "No source available": "Sense font disponible", "No users were found.": "No s'han trobat usuaris", "No valves to update": "No hi ha cap Valve per actualitzar", "None": "Cap", "Not factually correct": "No és clarament correcte", "Not helpful": "No ajuda", "Note deleted successfully": "La nota s'ha eliminat correctament", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Nota: Si s'estableix una puntuació mínima, la cerca només retornarà documents amb una puntuació major o igual a la puntuació mínima.", "Notes": "Notes", "Notification Sound": "So de la notificació", "Notification Webhook": "Webhook de la notificació", "Notifications": "Notificacions", "November": "Novembre", "OAuth ID": "ID OAuth", "October": "Octubre", "Off": "Desactivat", "Okay, Let's Go!": "D'acord, som-hi!", "OLED Dark": "OLED Fosc", "Ollama": "Ollama", "Ollama API": "API d'Ollama", "Ollama API settings updated": "La configuració de l'API d'Ollama s'ha actualitzat", "Ollama Version": "Versió <PERSON>", "On": "Activat", "OneDrive": "OneDrive", "Only active when \"Paste Large Text as File\" setting is toggled on.": "Només actiu quan la configuració \"Enganxar text gran com a fitxer\" està activa.", "Only active when the chat input is in focus and an LLM is generating a response.": "Només actiu quan la entrada de xat està en focus i un model de llenguatge està generant una resposta.", "Only alphanumeric characters and hyphens are allowed": "Només es permeten caràcters alfanumèrics i guions", "Only alphanumeric characters and hyphens are allowed in the command string.": "Només es permeten caràcters alfanumèrics i guions en la comanda.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "Només es poden editar col·leccions, crea una nova base de coneixement per editar/afegir documents.", "Only markdown files are allowed": "Només es permeten arxius markdown", "Only select users and groups with permission can access": "Només hi poden accedir usuaris i grups seleccionats amb permís", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Ui! Sembla que l'URL no és vàlida. Si us plau, revisa-la i torna-ho a provar.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Ui! Encara hi ha fitxers pujant-se. Si us plau, espera que finalitzi la càrrega.", "Oops! There was an error in the previous response.": "Ui! Hi ha hagut un error a la resposta anterior.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Ui! Estàs utilitzant un mètode no suportat (només frontend). Si us plau, serveix la WebUI des del backend.", "Open file": "<PERSON><PERSON><PERSON><PERSON>", "Open in full screen": "Obrir en pantalla complerta", "Open modal to configure connection": "Obre el modal per configurar la connexió", "Open Modal To Manage Floating Quick Actions": "Obre el model per configurar les Accions ràpides flotants", "Open new chat": "Obre un xat nou", "Open Sidebar": "Obre la barra lateral", "Open User Profile Menu": "Obre el menú de perfil d'usuari", "Open WebUI can use tools provided by any OpenAPI server.": "Open WebUI pot utilitzar eines de servidors OpenAPI.", "Open WebUI uses faster-whisper internally.": "Open WebUI utilitza faster-whisper internament.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI utilitza incrustacions de SpeechT5 i CMU Arctic.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "La versió d'Open WebUI (v{{OPEN_WEBUI_VERSION}}) és inferior a la versió requerida (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "API d'OpenAI", "OpenAI API Config": "Configuració de l'API d'OpenAI", "OpenAI API Key is required.": "Es requereix la clau API d'OpenAI.", "OpenAI API settings updated": "Configuració de l'API d'OpenAI actualitzada", "OpenAI URL/Key required.": "URL/Clau d'OpenAI requerides.", "openapi.json URL or Path": "Camí o URL a openapi.json", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "Opcions per executar un model de llenguatge amb visió local a la descripció de la imatge. Els paràmetres fan referència a un model allotjat a HuggingFace. Aquest paràmetre és mutuament excloent amb picture_description_api.", "or": "o", "Ordered List": "Llista ordenada", "Organize your users": "<PERSON><PERSON><PERSON> els teus usuaris", "Other": "Altres", "OUTPUT": "SORTIDA", "Output format": "Format de sortida", "Output Format": "Format de sortida", "Overview": "Vista general", "page": "pàgina", "Paginate": "<PERSON><PERSON><PERSON>", "Parameters": "Paràmetres", "Password": "Contrasenya", "Passwords do not match.": "Les contrasenyes no coincideixen", "Paste Large Text as File": "Enganxa un text llarg com a fitxer", "PDF document (.pdf)": "Document PDF (.pdf)", "PDF Extract Images (OCR)": "Extreu imatges del PDF (OCR)", "pending": "pendent", "Pending": "<PERSON><PERSON>", "Pending User Overlay Content": "Contingut de la finestra d'usuari pendent", "Pending User Overlay Title": "Tí<PERSON>l de la finestra d'usuari pendent", "Permission denied when accessing media devices": "Permís denegat en accedir a dispositius multimèdia", "Permission denied when accessing microphone": "Permís denegat en accedir al micròfon", "Permission denied when accessing microphone: {{error}}": "Permís denegat en accedir al micròfon: {{error}}", "Permissions": "<PERSON><PERSON><PERSON>", "Perplexity API Key": "Clau API de Perplexity", "Perplexity Model": "Model de Perplexity", "Perplexity Search Context Usage": "Utilització del context de cerca de Perplexity", "Personalization": "Personalitz<PERSON><PERSON><PERSON>", "Picture Description API Config": "Configuració de l'API de la descripció d'imatges", "Picture Description Local Config": "Configuració local de la descripció d'imatges", "Picture Description Mode": "Mode de descripció d'imatges", "Pin": "Fixar", "Pinned": "Fixat", "Pioneer insights": "Perspectives pioneres", "Pipe": "Canonada", "Pipeline deleted successfully": "Pipeline eliminada correctament", "Pipeline downloaded successfully": "Pipeline descarregada correctament", "Pipelines": "Pipelines", "Pipelines Not Detected": "No s'ha detectat Pipelines", "Pipelines Valves": "Vàlvules de les Pipelines", "Plain text (.md)": "Text en pla (.md)", "Plain text (.txt)": "Text pla (.txt)", "Playground": "Zona de jocs", "Playwright Timeout (ms)": "Temps d'espera (ms) de Playwright", "Playwright WebSocket URL": "URL del WebSocket de Playwright", "Please carefully review the following warnings:": "Si us plau, revisa els següents avisos amb cura:", "Please do not close the settings page while loading the model.": "No tanquis la pàgina de configuració mentre carregues el model.", "Please enter a prompt": "Si us plau, entra una indicació", "Please enter a valid path": "Si us plau, entra un camí vàlid", "Please enter a valid URL": "Si us plau, entra una URL vàlida", "Please fill in all fields.": "Emplena tots els camps, si us plau.", "Please select a model first.": "Si us plau, selecciona un model primer", "Please select a model.": "Si us plau, selecciona un model.", "Please select a reason": "Si us plau, selecciona una raó", "Please wait until all files are uploaded.": "Si us plau, espera fins que s'hagin carregat tots els fitxers.", "Port": "Port", "Positive attitude": "<PERSON>itud positiva", "Prefix ID": "Identificador del prefix", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "L'identificador de prefix s'utilitza per evitar conflictes amb altres connexions afegint un prefix als ID de model; deixa'l en blanc per desactivar-lo.", "Prevent file creation": "Impedir la creació d'arxius", "Preview": "Previsualització", "Previous 30 days": "30 dies anteriors", "Previous 7 days": "7 dies anteriors", "Previous message": "Missatge anterior", "Private": "Privat", "Profile Image": "Imatge de perfil", "Prompt": "Indicació", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Indicació (p. ex. Digues-me quelcom divertit sobre l'Imperi Romà)", "Prompt Autocompletion": "Completar automàticament la indicació", "Prompt Content": "Contingut de la indicació", "Prompt created successfully": "Indicació creada correctament", "Prompt suggestions": "Suggeriments d'indicacions", "Prompt updated successfully": "Indicació actualitzada correctament", "Prompts": "Indicacions", "Prompts Access": "Accés a les indicacions", "Prompts Public Sharing": "Compartició pública de indicacions", "Public": "<PERSON><PERSON><PERSON><PERSON>", "Pull \"{{searchValue}}\" from Ollama.com": "Obtenir \"{{searchValue}}\" de Ollama.com", "Pull a model from Ollama.com": "Obtenir un model d'Ollama.com", "Query Generation Prompt": "Indicació per a generació de consulta", "Quick Actions": "Accions ràpides", "RAG Template": "Plantilla RAG", "Rating": "Valoració", "Re-rank models by topic similarity": "Reclassificar els models per similitud de temes", "Read": "<PERSON><PERSON><PERSON>", "Read Aloud": "Llegir en veu alta", "Reason": "<PERSON><PERSON>", "Reasoning Effort": "Esforç de raonament", "Record": "Enregistrar", "Record voice": "Enregistrar la veu", "Redirecting you to Open WebUI Community": "Redirigint-te a la comunitat OpenWebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "Redueix la probabilitat de generar ximpleries. Un valor més alt (p. ex. 100) donarà respostes més diverses, mentre que un valor més baix (p. ex. 10) serà més conservador.", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Fes referència a tu mateix com a \"Usuari\" (p. ex., \"L'usuari està aprenent espanyol\")", "References from": "Referències de", "Refused when it shouldn't have": "Refusat quan no hauria d'haver estat", "Regenerate": "<PERSON><PERSON><PERSON>", "Reindex": "Reindexar", "Reindex Knowledge Base Vectors": "Reindexar els vector base del Coneixement", "Release Notes": "Notes de la versió", "Releases": "Versions", "Relevance": "Rellevància", "Relevance Threshold": "Límit de rellevància", "Remember Dismissal": "Recordar la decisió de refutar", "Remove": "Eliminar", "Remove {{MODELID}} from list.": "Eliminar {{MODELID}} de la llista", "Remove file": "Eliminar arxiu", "Remove File": "Eliminar arxiu", "Remove image": "Eliminar imatge", "Remove Model": "Eliminar el model", "Remove this tag from list": "Eliminar aquesta etiqueta de la llista", "Rename": "Canviar el nom", "Reorder Models": "Reordenar els models", "Reply in Thread": "Respondre al fil", "Reranking Engine": "Motor de valoració", "Reranking Model": "Model de reavaluació", "Reset": "Restableix", "Reset All Models": "Restablir tots els models", "Reset Upload Directory": "Restableix el directori de pujades", "Reset Vector Storage/Knowledge": "Restableix el Repositori de vectors/Coneixement", "Reset view": "Netejar la vista", "Response": "Resposta", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Les notifications de resposta no es poden activar perquè els permisos del lloc web han estat rebutjats. Comprova les preferències del navegador per donar l'accés necessari.", "Response splitting": "Divisió de la resposta", "Response Watermark": "Marca d'aigua de la resposta", "Result": "Resultat", "Retrieval": "Retrieval", "Retrieval Query Generation": "Generació de consultes Retrieval", "Rich Text Input for Chat": "Entrada de text ric per al xat", "RK": "RK", "Role": "Rol", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "Albada Rosé Pine", "RTL": "RTL", "Run": "Executar", "Running": "S'està executant", "Save": "Desar", "Save & Create": "Desar i crear", "Save & Update": "Desar i actualitzar", "Save As Copy": "Desar com a còpia", "Save Tag": "Desar l'etiqueta", "Saved": "Desat", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Desar els registres de xat directament a l'emmagatzematge del teu navegador ja no està suportat. Si us plau, descarregr i elimina els registres de xat fent clic al botó de sota. No et preocupis, pots tornar a importar fàcilment els teus registres de xat al backend a través de", "Scroll On Branch Change": "Fer scroll en canviar de branca", "Search": "Cercar", "Search a model": "Cercar un model", "Search Base": "Base de cerca", "Search Chats": "Cercar xats", "Search Collection": "Cercar col·leccions", "Search Filters": "Filtres de cerca", "search for archived chats": "cercar xats arxivats", "search for folders": "cercar carpetes", "search for pinned chats": "cercar xats marcats", "search for shared chats": "cercar xats compartits", "search for tags": "cercar etiquetes", "Search Functions": "Cercar funcions", "Search In Models": "Cercar als models", "Search Knowledge": "Cercar coneixement", "Search Models": "Cercar models", "Search Notes": "Cercar notes", "Search options": "Opcions de cerca", "Search Prompts": "Cercar indicacions", "Search Result Count": "Recompte de resultats de cerca", "Search the internet": "Cercar a internet", "Search Tools": "Cercar eines", "SearchApi API Key": "Clau API de SearchApi", "SearchApi Engine": "Motor de SearchApi", "Searched {{count}} sites": "S'han cercat {{count}} pàgines", "Searching \"{{searchQuery}}\"": "Cercant \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "Cercant \"{{searchQuery}}\" al coneixement", "Searching the web...": "Cercant la web...", "Searxng Query URL": "URL de consulta de Searxng", "See readme.md for instructions": "Consulta l'arxiu readme.md per obtenir instruccions", "See what's new": "Veure què hi ha de nou", "Seed": "<PERSON><PERSON><PERSON>", "Select a base model": "Seleccionar un model base", "Select a conversation to preview": "Seleccionar una conversa a previsualitzar", "Select a engine": "Seleccionar un motor", "Select a function": "Seleccionar una funció", "Select a group": "Seleccionar un grup", "Select a model": "Seleccionar un model", "Select a pipeline": "Seleccionar una Pipeline", "Select a pipeline url": "Seleccionar l'URL d'una Pipeline", "Select a tool": "Seleccionar una eina", "Select an auth method": "Seleccionar un mètode d'autenticació", "Select an Ollama instance": "Seleccionar una instància d'Ollama", "Select Engine": "Seleccionar el motor", "Select Knowledge": "Seleccionar cone<PERSON>ement", "Select only one model to call": "Seleccionar només un model per trucar", "Selected model(s) do not support image inputs": "El(s) model(s) seleccionats no admeten l'entrada d'imatges", "semantic": "semànti<PERSON>", "Semantic distance to query": "Distància semàntica a la pregunta", "Send": "Enviar", "Send a Message": "Enviar un missatge", "Send message": "<PERSON><PERSON>r missatge", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Envia `stream_options: { include_usage: true }` a la sol·licitud.\nEls proveïdors compatibles retornaran la informació d'ús del token a la resposta quan s'estableixi.", "September": "Setembre", "SerpApi API Key": "Clau API de SerpApi", "SerpApi Engine": "Motor de SerpApi", "Serper API Key": "Clau <PERSON>", "Serply API Key": "Clau <PERSON> de Serply", "Serpstack API Key": "Clau <PERSON>pstack", "Server connection verified": "Connexió al servidor verificada", "Set as default": "Establir com a predeterminat", "Set CFG Scale": "Establir l'escala CFG", "Set Default Model": "Establir el model predeterminat", "Set embedding model": "Establir el model d'incrustació", "Set embedding model (e.g. {{model}})": "<PERSON><PERSON><PERSON><PERSON><PERSON> el model d'incrustació (p. ex. {{model}})", "Set Image Size": "Establir la mida de la image", "Set reranking model (e.g. {{model}})": "Establir el model de reavaluació (p. ex. {{model}})", "Set Sampler": "Establir el mostrejador", "Set Scheduler": "Establir el programador", "Set Steps": "Establir el nombre de passos", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "Estableix el nombre de capes que es descarregaran a la GPU. Augmentar aquest valor pot millorar significativament el rendiment dels models optimitzats per a l'acceleració de la GPU, però també pot consumir més energia i recursos de GPU.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "Establir el nombre de fils de treball utilitzats per al càlcul. Aquesta opció controla quants fils s'utilitzen per processar les sol·licituds entrants simultàniament. Augmentar aquest valor pot millorar el rendiment amb càrregues de treball de concurrència elevada, però també pot consumir més recursos de CPU.", "Set Voice": "Establir la veu", "Set whisper model": "Establir el model whisper", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "Estableix un biaix pla contra tokens que han aparegut almenys una vegada. Un valor més alt (p. ex., 1,5) penalitzarà les repeticions amb més força, mentre que un valor més baix (p. ex., 0,9) serà més indulgent. A 0, està desactivat.", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "Estableix un biaix d'escala contra tokens per penalitzar les repeticions, en funció de quantes vegades han aparegut. Un valor més alt (p. ex., 1,5) penalitzarà les repeticions amb més força, mentre que un valor més baix (p. ex., 0,9) serà més indulgent. A 0, està desactivat.", "Sets how far back for the model to look back to prevent repetition.": "Estableix fins a quin punt el model mira enrere per evitar la repetició.", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "Estableix la llavor del nombre aleatori que s'utilitzarà per a la generació. Establir-ho a un número específic farà que el model generi el mateix text per a la mateixa sol·licitud.", "Sets the size of the context window used to generate the next token.": "Estableix la mida de la finestra de context utilitzada per generar el següent token.", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "Establir les seqüències d'aturada a utilitzar. Quan es trobi aquest patró, el LLM deixarà de generar text. Es poden establir diversos patrons de parada especificant diversos paràmetres de parada separats en un fitxer model.", "Settings": "Preferències", "Settings saved successfully!": "Les preferències s'han desat correctament", "Share": "Compartir", "Share Chat": "Compartir el xat", "Share to Open WebUI Community": "Compartir amb la comunitat OpenWebUI", "Sharing Permissions": "Compartir els permisos", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "Les dreceres de teclat amb un asterisc (*) són situacionals i només actives sota condicions específiques.", "Show": "Mostrar", "Show \"What's New\" modal on login": "<PERSON><PERSON><PERSON> <PERSON><PERSON>uè hi ha de nou' a l'entrada", "Show Admin Details in Account Pending Overlay": "Mostrar els detalls de l'administrador a la superposició del compte pendent", "Show All": "<PERSON><PERSON> tot", "Show Formatting Toolbar": "Mostrar la barra de format", "Show image preview": "Mostrar la previsualització de la imatge", "Show Less": "Mostrar menys", "Show Model": "Mostrar el model", "Show shortcuts": "<PERSON>rar dreceres", "Show your support!": "Mostra el teu suport!", "Showcased creativity": "Creativitat mostrada", "Sign in": "<PERSON><PERSON><PERSON>", "Sign in to {{WEBUI_NAME}}": "Iniciar <PERSON><PERSON><PERSON> a {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "Iniciar se<PERSON><PERSON> a {{WEBUI_NAME}} amb LDAP", "Sign Out": "<PERSON><PERSON>", "Sign up": "Registrar-se", "Sign up to {{WEBUI_NAME}}": "Registrar-se a {{WEBUI_NAME}}", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to False.": "Millora significativament la precisió mitjançant un LLM per millorar les taules, els formularis, les matemàtiques en línia i la detecció de disseny. Augmentarà la latència. El valor per defecte és Fals.", "Signing in to {{WEBUI_NAME}}": "Iniciant <PERSON><PERSON><PERSON> a {{WEBUI_NAME}}", "Sink List": "Enfonsar la llista", "sk-1234": "sk-1234", "Skip Cache": "Ometre la memòria cau", "Skip the cache and re-run the inference. Defaults to False.": "Omet la memòria cai i torna a executar la inferència. Per defecte és Fals.", "Sougou Search API sID": "sID de l'API de Sougou Search", "Sougou Search API SK": "SK de l'API de Sougou Search", "Source": "Font", "Speech Playback Speed": "Velocitat de la parla", "Speech recognition error: {{error}}": "Error de reconeixement de veu: {{error}}", "Speech-to-Text": "Àudio-a-Text", "Speech-to-Text Engine": "Motor de veu a text", "Stop": "<PERSON><PERSON>", "Stop Generating": "Atura la generació", "Stop Sequence": "Atura la seqüència", "Stream Chat Response": "Fer streaming de la resposta del xat", "Stream Delta Chunk Size": "Mida del fragment Delta del flux", "Strikethrough": "<PERSON><PERSON><PERSON>", "Strip Existing OCR": "Eliminar OCR existent", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "Elimina el text OCR existent del PDF i torna a executar l'OCR. S'ignora si Força OCR està habilitat. Per defecte és Fals.", "STT Model": "Model SST", "STT Settings": "Preferències de STT", "Stylized PDF Export": "Exportació en PDF estilitzat", "Subtitle (e.g. about the Roman Empire)": "Subtítol (per exemple, sobre l'Imperi Romà)", "Success": "<PERSON><PERSON><PERSON>", "Successfully updated.": "Actualitzat correctament.", "Suggest a change": "Suggerir un canvi", "Suggested": "<PERSON><PERSON><PERSON>", "Support": "<PERSON><PERSON>", "Support this plugin:": "Dona suport a aquest complement:", "Supported MIME Types": "Tipus MIME admesos", "Sync directory": "Sincronitzar directori", "System": "Sistema", "System Instructions": "Instruccions de sistema", "System Prompt": "Indicació del Sistema", "Tags": "Etiquetes", "Tags Generation": "Generació d'etiquetes", "Tags Generation Prompt": "Indicació per a la generació d'etiquetes", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "El mostreig sense cua s'utilitza per reduir l'impacte de tokens menys probables de la sortida. Un valor més alt (p. ex., 2,0) reduirà més l'impacte, mentre que un valor d'1,0 desactiva aquesta configuració.", "Talk to model": "Parlar amb el model", "Tap to interrupt": "Prem per interrompre", "Task List": "Llista de tasques", "Task Model": "Model de tasques", "Tasks": "Tasques", "Tavily API Key": "<PERSON><PERSON> <PERSON>", "Tavily Extract Depth": "Profunditat d'extracció de Tavily", "Tell us more:": "Dona'ns més informació:", "Temperature": "Temperatura", "Temporary Chat": "Xat temporal", "Text Splitter": "Separador de text", "Text-to-Speech": "Text-a-veu", "Text-to-Speech Engine": "Motor de text a veu", "Thanks for your feedback!": "Gràcies pel teu comentari!", "The Application Account DN you bind with for search": "El DN del compte d'aplicació per realitzar la cerca", "The base to search for users": "La base per cercar usuaris", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "La mida del lot determina quantes sol·licituds de text es processen alhora. Una mida de lot més gran pot augmentar el rendiment i la velocitat del model, però també requereix més memòria.", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Els desenvolupadors d'aquest complement són voluntaris apassionats de la comunitat. Si trobeu útil aquest complement, considereu contribuir al seu desenvolupament.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "La classificació d'avaluació es basa en el sistema de qualificació Elo i s'actualitza en temps real.", "The format to return a response in. Format can be json or a JSON schema.": "El format per retornar una resposta. El format pot ser json o un esquema JSON.", "The height in pixels to compress images to. Leave empty for no compression.": "L'alçada en píxels per comprimir imatges. Deixar-ho buit per a cap compressió.", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "L'idiom de l'àudio d'entrada. Proporcionar l'idioma d'entrada en format ISO-639-1 (p. ex. en) millorarà la precisió i la latència. Deixar-ho buit per detectar automàticament el llenguatge.", "The LDAP attribute that maps to the mail that users use to sign in.": "L'atribut LDAP que s'associa al correu que els usuaris utilitzen per iniciar la sessió.", "The LDAP attribute that maps to the username that users use to sign in.": "L'atribut LDAP que mapeja el nom d'usuari amb l'usuari que vol iniciar sessió", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "La classificació està actualment en versió beta i és possible que s'ajustin els càlculs de la puntuació a mesura que es perfeccioni l'algorisme.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "La mida màxima del fitxer en MB. Si la mida del fitxer supera aquest límit, el fitxer no es carregarà.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "El nombre màxim de fitxers que es poden utilitzar alhora al xat. Si el nombre de fitxers supera aquest límit, els fitxers no es penjaran.", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "Format de sortida per al text. Pot ser 'json', 'markdown' o 'html'. Per defecte és 'markdown'.", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "El valor de puntuació hauria de ser entre 0.0 (0%) i 1.0 (100%).", "The stream delta chunk size for the model. Increasing the chunk size will make the model respond with larger pieces of text at once.": "La mida del fragment Delta de flux per al model. Si augmentes la mida del fragment, el model respondrà amb fragments de text més grans alhora.", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "La temperatura del model. Augmentar la temperatura farà que el model respongui de manera més creativa.", "The Weight of BM25 Hybrid Search. 0 more lexical, 1 more semantic. Default 0.5": "El pes de la cerca híbrida BM25. 0 més lèxics, 1 més semàntics. Per defecte 0,5", "The width in pixels to compress images to. Leave empty for no compression.": "L'amplada en píxels per comprimir imatges. Deixar-ho buit per a cap compressió.", "Theme": "<PERSON><PERSON>", "Thinking...": "Pensant...", "This action cannot be undone. Do you wish to continue?": "Aquesta acció no es pot desfer. Vols continuar?", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "Aquest canal es va crear el dia {{createdAt}}. Aquest és el començament del canal {{channelName}}.", "This chat won't appear in history and your messages will not be saved.": "Aquest xat no apareixerà a l'historial i els teus missatges no es desaran.", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Això assegura que les teves converses valuoses queden desades de manera segura a la teva base de dades. Gràcies!", "This feature is experimental and may be modified or discontinued without notice.": "Aquesta funció és experimental i es pot modificar o deixar de ser disponible sense previ avís.", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Aquesta és una funció experimental, és possible que no funcioni com s'espera i està subjecta a canvis en qualsevol moment.", "This model is not publicly available. Please select another model.": "Aquest model no està disponible públicament. Seleccioneu-ne un altre.", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "Aquesta opció controla quant temps el model romandrà carregat en memòria després de la sol·licitud (per defecte: 5m)", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "Aquesta opció controla quants tokens es conserven en actualitzar el context. Per exemple, si s'estableix en 2, es conservaran els darrers 2 tokens del context de conversa. Preservar el context pot ajudar a mantenir la continuïtat d'una conversa, però pot reduir la capacitat de respondre a nous temes.", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "Aquesta opció activa o desactiva l'ús de la funció de raonament a Ollama, que permet que el model pensi abans de generar una resposta. Quan està activada, el model pot trigar una estona en processar el context de la conversa i generar una resposta més reflexiva.", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "Aquesta opció estableix el nombre màxim de tokens que el model pot generar en la seva resposta. Augmentar aquest límit permet que el model proporcioni respostes més llargues, però també pot augmentar la probabilitat que es generi contingut poc útil o irrellevant.", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Aquesta opció eliminarà tots els fitxers existents de la col·lecció i els substituirà per fitxers recentment penjats.", "This response was generated by \"{{model}}\"": "Aquesta resposta l'ha generat el model \"{{model}}\"", "This will delete": "<PERSON><PERSON><PERSON> eliminar<PERSON>", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "<PERSON><PERSON><PERSON> eliminarà <strong>{{NAME}}</strong> i <strong>tots els continguts</strong>.", "This will delete all models including custom models": "<PERSON><PERSON>ò eliminarà tots els models incloent els personalitzats", "This will delete all models including custom models and cannot be undone.": "Ai<PERSON>ò eliminarà tots els models incloent els personalitzats i no es pot desfer", "This will reset the knowledge base and sync all files. Do you wish to continue?": "<PERSON><PERSON>ò restablirà la base de coneixement i sincronitzarà tots els fitxers. Vols continuar?", "Thorough explanation": "Explicació en detall", "Thought for {{DURATION}}": "He pensat durant {{DURATION}}", "Thought for {{DURATION}} seconds": "He pensat durant {{DURATION}} segons", "Thought for less than a second": "He pensat menys d'un segon", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "La URL del servidor Tika és obligatòria.", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Consell: Actualitza les diverses variables consecutivament prement la tecla de tabulació en l'entrada del xat després de cada reemplaçament.", "Title": "Títol", "Title (e.g. Tell me a fun fact)": "<PERSON><PERSON><PERSON><PERSON> (p. ex. Digues-me quelcom divertit)", "Title Auto-Generation": "Generació automàtica de títol", "Title cannot be an empty string.": "El títol no pot ser una cadena buida.", "Title Generation": "Generació de títols", "Title Generation Prompt": "Indicació de generació de títol", "TLS": "TLS", "To access the available model names for downloading,": "Per accedir als noms dels models disponibles per descarregar,", "To access the GGUF models available for downloading,": "Per accedir als models GGUF disponibles per descarregar,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Per accedir a la WebUI, poseu-vos en contacte amb l'administrador. Els administradors poden gestionar els estats dels usuaris des del tauler d'administració.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "Per adjuntar la base de coneixement aquí, afegiu-la primer a l'espai de treball \"Coneixement\".", "To learn more about available endpoints, visit our documentation.": "Per obtenir més informació sobre els punts d'accés disponibles, visiteu la nostra documentació.", "To learn more about powerful prompt variables, click here": "Per obtenir més informació sobre les variables de prompt potents, fes clic aquí", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "Per protegir la privadesa, només es comparteixen puntuacions, identificadors de models, etiquetes i metadades dels comentaris; els registres de xat romanen privats i no s'inclouen.", "To select actions here, add them to the \"Functions\" workspace first.": "Per seleccionar accions aquí, afegeix-les primer a l'espai de treball \"Funcions\".", "To select filters here, add them to the \"Functions\" workspace first.": "Per seleccionar filtres aquí, afegeix-los primer a l'espai de treball \"Funcions\".", "To select toolkits here, add them to the \"Tools\" workspace first.": "Per seleccionar kits d'eines aquí, afegeix-los primer a l'espai de treball \"Eines\".", "Toast notifications for new updates": "Notificacions Toast de noves actualitzacions", "Today": "<PERSON><PERSON><PERSON>", "Toggle search": "Alternar cerca", "Toggle settings": "Alterna preferències", "Toggle sidebar": "Alterna la barra lateral", "Toggle whether current connection is active.": "Alterna si la connexió actual està activa.", "Token": "Token", "Too verbose": "<PERSON>a explicit", "Tool created successfully": "<PERSON>a creada correctament", "Tool deleted successfully": "Eina eliminada correctament", "Tool Description": "Descripció de l'eina", "Tool ID": "ID de l'eina", "Tool imported successfully": "Eina importada correctament", "Tool Name": "Nom de l'eina", "Tool Servers": "Servidors d'eines", "Tool updated successfully": "<PERSON><PERSON> correctament", "Tools": "<PERSON><PERSON>", "Tools Access": "Accés a les eines", "Tools are a function calling system with arbitrary code execution": "Les eines són un sistema de crida a funcions amb execució de codi arbitrari", "Tools Function Calling Prompt": "Indicació per a la crida de funcions", "Tools have a function calling system that allows arbitrary code execution.": "Les eines disposen d'un sistema de crida a funcions que permet execució de codi arbitrari.", "Tools Public Sharing": "Compartició pública d'eines", "Top K": "Top K", "Top K Reranker": "Top K Reranker", "Transformers": "Transformadors", "Trouble accessing Ollama?": "Problemes en accedir a Ollama?", "Trust Proxy Environment": "<PERSON><PERSON><PERSON> en l'entorn proxy", "Try Again": "<PERSON><PERSON> a intentar-ho", "TTS Model": "Model TTS", "TTS Settings": "Preferències de TTS", "TTS Voice": "Veu TTS", "Type": "<PERSON><PERSON><PERSON>", "Type Hugging Face Resolve (Download) URL": "Escriu l'URL de Resolució (Descàrrega) de Hugging Face", "Uh-oh! There was an issue with the response.": "Vaja! Hi ha hagut una incidència amb la resposta.", "UI": "UI", "Unarchive All": "Desar<PERSON><PERSON> tot", "Unarchive All Archived Chats": "Desarxivar tots els xats arxivats", "Unarchive Chat": "Desarxivar xat", "Underline": "<PERSON><PERSON><PERSON><PERSON>", "Unloads {{FROM_NOW}}": "<PERSON><PERSON> <PERSON> {{FROM_NOW}}", "Unlock mysteries": "Desbloqueja els misteris", "Unpin": "Alliberar", "Unravel secrets": "Descobreix els secrets", "Unsupported file type.": "Tipus no suportat", "Untagged": "Sense etiquetes", "Untitled": "Sense títol", "Update": "<PERSON><PERSON><PERSON><PERSON>", "Update and Copy Link": "Actualitzar i copiar l'enllaç", "Update for the latest features and improvements.": "Actualitza per a les darreres característiques i millores.", "Update password": "Actualitzar la contrasenya", "Updated": "Actualitzat", "Updated at": "Actualitzat el", "Updated At": "Actualitzat el", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "Actualitzar a un pla amb llicència per obtenir capacitats millorades, com ara la temàtica personalitzada i la marca, i assistència dedicada.", "Upload": "<PERSON><PERSON><PERSON>", "Upload a GGUF model": "Pujar un model GGUF", "Upload Audio": "<PERSON><PERSON><PERSON>", "Upload directory": "<PERSON><PERSON>jar directori", "Upload files": "<PERSON><PERSON><PERSON> fitxe<PERSON>", "Upload Files": "<PERSON><PERSON><PERSON> fitxe<PERSON>", "Upload Pipeline": "<PERSON><PERSON>jar una Pipeline", "Upload Progress": "Progrés de c<PERSON>rrega", "URL": "URL", "URL Mode": "Mode URL", "Usage": "Ús", "Use '#' in the prompt input to load and include your knowledge.": "Utilitza '#' a l'entrada de la indicació per carregar i incloure els teus coneixements.", "Use Gravatar": "<PERSON><PERSON><PERSON><PERSON>", "Use groups to group your users and assign permissions.": "Utilitza grups per agrupar els usuaris i assignar permisos.", "Use Initials": "Utilitzar inicials", "Use LLM": "Utilizar model de llenguatge", "Use no proxy to fetch page contents.": "No utilitzis un proxy per obtenir contingut de la pàgina.", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "Utilitza el proxy designat per les variables d'entorn http_proxy i https_proxy per obtenir el contingut de la pàgina.", "user": "usuari", "User": "<PERSON><PERSON><PERSON>", "User Groups": "", "User location successfully retrieved.": "Ubicació de l'usuari obtinguda correctament", "User menu": "<PERSON><PERSON> d'usuari", "User Webhooks": "Webhooks d'usuari", "Username": "Nom d'usuari", "Users": "<PERSON><PERSON><PERSON>", "Using Entire Document": "Utilitzant tot el document", "Using Focused Retrieval": "Utilitzant RAG", "Using the default arena model with all models. Click the plus button to add custom models.": "S'utilitza el model d'Arena predeterminat amb tots els models. Clica el botó més per afegir models personalitzats.", "Valid time units:": "Unitats de temps vàlides:", "Valves": "Valves", "Valves updated": "<PERSON><PERSON>", "Valves updated successfully": "<PERSON><PERSON> actual<PERSON> correctament", "variable": "variable", "Verify Connection": "Verificar la connexió", "Verify SSL Certificate": "Verificar el certificat SSL", "Version": "<PERSON><PERSON><PERSON><PERSON>", "Version {{selectedVersion}} of {{totalVersions}}": "Versió {{selectedVersion}} de {{totalVersions}}", "View Replies": "Veure les respostes", "View Result from **{{NAME}}**": "Veure el resultat de **{{NAME}}**", "Visibility": "Visibilitat", "Vision": "Visió", "Voice": "Veu", "Voice Input": "Entrada de veu", "Voice mode": "Mode de veu", "Warning": "<PERSON><PERSON><PERSON>", "Warning:": "Avís:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Avís: <PERSON>bil<PERSON>r a<PERSON> permetrà als usuaris penjar codi arbitrari al servidor.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Avís: <PERSON> s'actualitza o es canvia el model d'incrustació, s'hauran de tornar a importar tots els documents.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "Avís: l'execució de Jupyter permet l'execució de codi arbitrari, la qual cosa comporta greus riscos de seguretat; procediu amb extrema precaució.", "Web": "Web", "Web API": "Web API", "Web Loader Engine": "Motor de càrrega Web", "Web Search": "Cerca la web", "Web Search Engine": "Motor de cerca de la web", "Web Search in Chat": "Cerca a internet al xat", "Web Search Query Generation": "Generació de consultes per a la cerca de la web", "Webhook URL": "URL del webhook", "WebUI Settings": "Preferències de WebUI", "WebUI URL": "URL de WebUI", "WebUI will make requests to \"{{url}}\"": "WebUI farà peticions a \"{{url}}\"", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI farà peticions a \"{{url}}/api/chat\"", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI farà peticions a \"{{url}}/chat/completions\"", "What are you trying to achieve?": "Què intentes aconseguir?", "What are you working on?": "En què estàs treballant?", "What's New in": "Què hi ha de nou a", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "Quan està activat, el model respondrà a cada missatge de xat en temps real, generant una resposta tan bon punt l'usuari envia un missatge. Aquest mode és útil per a aplicacions de xat en directe, però pot afectar el rendiment en maquinari més lent.", "wherever you are": "allà on estiguis", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "Si es pagina la sortida. Cada pàgina estarà separada per una regla horitzontal i un número de pàgina. Per defecte és Fals.", "Whisper (Local)": "<PERSON><PERSON><PERSON> (local)", "Why?": "Per què?", "Widescreen Mode": "Mode de pantalla ampla", "Won": "Ha guanyat", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "Funciona juntament amb top-k. Un valor més alt (p. ex., 0,95) donarà lloc a un text més divers, mentre que un valor més baix (p. ex., 0,5) generarà un text més concentrat i conservador.", "Workspace": "Espai de treball", "Workspace Permissions": "Permisos de l'espai de treball", "Write": "Escriure", "Write a prompt suggestion (e.g. Who are you?)": "Escriu una suggerència d'indicació (p. ex. Qui ets?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Escriu un resum en 50 paraules que resumeixi [tema o paraula clau].", "Write something...": "Escriu quelcom...", "Yacy Instance URL": "URL de la instància de Yacy", "Yacy Password": "Contrasenya de Yacy", "Yacy Username": "Nom d'usuari de <PERSON>", "Yesterday": "<PERSON><PERSON>", "You": "Tu", "You are currently using a trial license. Please contact support to upgrade your license.": "Actualment esteu utilitzant una llicència de prova. Poseu-vos en contacte amb el servei d'assistència per actualitzar la vostra llicència.", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "Només pots xatejar amb un màxim de {{maxCount}} fitxers alhora.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Pots personalitzar les teves interaccions amb els models de llenguatge afegint memòries mitjançant el botó 'Gestiona' que hi ha a continuació, fent-les més útils i adaptades a tu.", "You cannot upload an empty file.": "No es pot pujar un arxiu buit.", "You do not have permission to upload files.": "No tens permisos per pujar arxius.", "You have no archived conversations.": "No tens converses arxivades.", "You have shared this chat": "Has compartit aquest xat", "You're a helpful assistant.": "Ets un assistent útil.", "You're now logged in.": "Ara estàs connectat.", "Your account status is currently pending activation.": "El compte està actualment pendent d'activació", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "Tota la teva contribució anirà directament al desenvolupador del complement; Open WebUI no se'n queda cap percentatge. Tanmateix, la plataforma de finançament escollida pot tenir les seves pròpies comissions.", "Youtube": "Youtube", "Youtube Language": "Idioma de YouTube", "Youtube Proxy URL": "URL de Proxy de Youtube"}