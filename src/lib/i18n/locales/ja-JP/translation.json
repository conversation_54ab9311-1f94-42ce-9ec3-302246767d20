{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' または '-1' で無期限。", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(例: `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(例: `sh webui.sh --api`)", "(latest)": "(最新)", "(leave blank for to use commercial endpoint)": "", "[Last] dddd [at] h:mm A": "", "[Today at] h:mm A": "", "[Yesterday at] h:mm A": "", "{{ models }}": "{{ モデル }}", "{{COUNT}} Available Tools": "{{COUNT}} 利用可能ツール", "{{COUNT}} characters": "", "{{COUNT}} hidden lines": "{{COUNT}} 非表示行", "{{COUNT}} Replies": "{{COUNT}} 返信", "{{COUNT}} words": "", "{{user}}'s Chats": "{{user}} のチャット", "{{webUIName}} Backend Required": "{{webUIName}} バックエンドが必要です", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "新しいバージョンが利用可能です。", "A task model is used when performing tasks such as generating titles for chats and web search queries": "タスクモデルは、チャットやウェブ検索クエリのタイトルの生成などのタスクを実行するときに使用されます", "a user": "ユーザー", "About": "概要", "Accept autocomplete generation / Jump to prompt variable": "", "Access": "アクセス", "Access Control": "アクセス制御", "Accessible to all users": "すべてのユーザーにアクセス可能", "Account": "アカウント", "Account Activation Pending": "アカウント承認待ち", "Accurate information": "情報が正確", "Action": "", "Actions": "アクション", "Activate": "アクティブ化", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "このコマンド \"/{{COMMAND}}\" をチャットに入力してアクティブ化します", "Active Users": "アクティブユーザー", "Add": "追加", "Add a model ID": "モデルIDを追加", "Add a short description about what this model does": "このモデルの機能に関する簡単な説明を追加します", "Add a tag": "タグを追加", "Add Arena Model": "Arenaモデルを追加", "Add Connection": "接続を追加", "Add Content": "コンテンツを追加", "Add content here": "ここへコンテンツを追加", "Add Custom Parameter": "", "Add custom prompt": "カスタムプロンプトを追加", "Add Details": "", "Add Files": "ファイルを追加", "Add Group": "グループを追加", "Add Memory": "メモリを追加", "Add Model": "モデルを追加", "Add Reaction": "リアクションを追加", "Add Tag": "タグを追加", "Add Tags": "タグを追加", "Add text content": "コンテンツを追加", "Add User": "ユーザーを追加", "Add User Group": "ユーザーグループを追加", "Additional Config": "", "Additional configuration options for marker. This should be a JSON string with key-value pairs. For example, '{\"key\": \"value\"}'. Supported keys include: disable_links, keep_pageheader_in_output, keep_pagefooter_in_output, filter_blank_pages, drop_repeated_text, layout_coverage_threshold, merge_threshold, height_tolerance, gap_threshold, image_threshold, min_line_length, level_count, default_level": "", "Adjusting these settings will apply changes universally to all users.": "これらの設定を調整すると、すべてのユーザーに変更が適用されます。", "admin": "管理者", "Admin": "管理者", "Admin Panel": "管理者パネル", "Admin Settings": "管理者設定", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "管理者は全てのツールにアクセス出来ます。ユーザーはワークスペースのモデル毎に割り当てて下さい。", "Advanced Parameters": "詳細パラメーター", "Advanced Params": "高度なパラメータ", "AI": "", "All": "全て", "All Documents": "全てのドキュメント", "All models deleted successfully": "全てのモデルが正常に削除されました", "Allow Call": "コールを許可", "Allow Chat Controls": "チャットコントロールを許可", "Allow Chat Delete": "チャットの削除を許可", "Allow Chat Deletion": "チャットの削除を許可", "Allow Chat Edit": "チャットの編集を許可", "Allow Chat Export": "チャットのエクスポートを許可", "Allow Chat Params": "", "Allow Chat Share": "チャットの共有を許可", "Allow Chat System Prompt": "", "Allow Chat Valves": "", "Allow File Upload": "ファイルのアップロードを許可", "Allow Multiple Models in Chat": "チャットで複数のモデルを許可", "Allow non-local voices": "ローカル以外のボイスを許可", "Allow Speech to Text": "音声をテキストに変換を許可", "Allow Temporary Chat": "一時的なチャットを許可", "Allow Text to Speech": "テキストを音声に変換を許可", "Allow User Location": "ユーザーロケーションの許可", "Allow Voice Interruption in Call": "通話中に音声の割り込みを許可", "Allowed Endpoints": "", "Allowed File Extensions": "", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "", "Already have an account?": "すでにアカウントをお持ちですか？", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "", "Always": "常に", "Always Collapse Code Blocks": "常にコードブロックを折りたたむ", "Always Expand Details": "常に詳細を展開", "Always Play Notification Sound": "常に通知音を再生", "Amazing": "", "an assistant": "アシスタント", "Analytics": "", "Analyzed": "分析された", "Analyzing...": "分析中...", "and": "および", "and {{COUNT}} more": "および{{COUNT}}件以上", "and create a new shared link.": "し、新しい共有リンクを作成します。", "Android": "", "API": "", "API Base URL": "API ベース URL", "API Base URL for Datalab Marker service. Defaults to: https://www.datalab.to/api/v1/marker": "", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "API キー", "API Key created.": "API キーが作成されました。", "API Key Endpoint Restrictions": "API キーのエンドポイント制限", "API keys": "API キー", "API Version": "", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "4月", "Archive": "アーカイブ", "Archive All Chats": "すべてのチャットをアーカイブする", "Archived Chats": "チャット記録", "archived-chat-export": "チャット記録のエクスポート", "Are you sure you want to clear all memories? This action cannot be undone.": "すべてのメモリをクリアしますか？この操作は元に戻すことができません。", "Are you sure you want to delete this channel?": "このチャンネルを削除しますか？", "Are you sure you want to delete this message?": "このメッセージを削除しますか？", "Are you sure you want to unarchive all archived chats?": "すべてのアーカイブされたチャットをアンアーカイブしますか？", "Are you sure?": "よろしいですか？", "Arena Models": "Arenaモデル", "Artifacts": "アーティファクト", "Ask": "質問", "Ask a question": "質問して下さい。", "Assistant": "アシスタント", "Attach file from knowledge": "ナレッジからファイルを添付", "Attention to detail": "詳細に注意する", "Attribute for Mail": "メールの属性", "Attribute for Username": "ユーザー名の属性", "Audio": "オーディオ", "August": "8月", "Auth": "", "Authenticate": "", "Authentication": "認証", "Auto": "自動", "Auto-Copy Response to Clipboard": "クリップボードへの応答の自動コピー", "Auto-playback response": "応答の自動再生", "Autocomplete Generation": "オートコンプリート生成", "Autocomplete Generation Input Max Length": "オートコンプリート生成入力の最大長", "Automatic1111": "AUTOMATIC1111", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111のAuthを入力", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 ベース URL", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 ベース URL が必要です。", "Available list": "利用可能リスト", "Available Tools": "利用可能ツール", "available!": "利用可能！", "Awful": "", "Azure AI Speech": "AzureAIスピーチ", "Azure Region": "Azureリージョン", "Back": "戻る", "Bad Response": "応答が悪い", "Banners": "バナー", "Base Model (From)": "ベースモデル (From)", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "", "before": "より前", "Being lazy": "怠惰な", "Beta": "ベータ", "Bing Search V7 Endpoint": "Bing Search V7 エンドポイント", "Bing Search V7 Subscription Key": "Bing Search V7 サブスクリプションキー", "BM25 Weight": "", "Bocha Search API Key": "Bocha Search APIキー", "Bold": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "特定のトークンの強調またはペナルティを適用します。バイアス値は-100から100（包括的）にクランプされます。（デフォルト：なし）", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "Docling OCRエンジンと言語（s）の両方が提供されているか、両方が空のままになっている必要があります。", "Brave Search API Key": "Brave Search APIキー", "Bullet List": "", "Button ID": "", "Button Label": "", "Button Prompt": "", "By {{name}}": "{{name}}による", "Bypass Embedding and Retrieval": "埋め込みと検索をバイパス", "Bypass Web Loader": "", "Cache Base Model List": "", "Calendar": "カレンダー", "Call": "コール", "Call feature is not supported when using Web STT engine": "Web STTエンジンを使用している場合、コール機能はサポートされていません。", "Camera": "カメラ", "Cancel": "キャンセル", "Capabilities": "資格", "Capture": "キャプチャ", "Capture Audio": "音声のキャプチャ", "Certificate Path": "証明書パス", "Change Password": "パスワードを変更", "Channel Name": "チャンネル名", "Channels": "チャンネル", "Character": "文字", "Character limit for autocomplete generation input": "オートコンプリート生成入力の文字数の制限", "Chart new frontiers": "", "Chat": "チャット", "Chat Background Image": "チャットの背景画像", "Chat Bubble UI": "チャットバブルUI", "Chat Controls": "チャットコントロール", "Chat direction": "チャットの方向", "Chat ID": "", "Chat Overview": "チャット概要", "Chat Permissions": "チャットの許可", "Chat Tags Auto-Generation": "チャットタグの自動生成", "Chats": "チャット", "Check Again": "再確認", "Check for updates": "アップデートを確認", "Checking for updates...": "アップデートを確認しています...", "Choose a model before saving...": "保存する前にモデルを選択してください...", "Chunk Overlap": "チャンクオーバーラップ", "Chunk Size": "チャンクサイズ", "Ciphers": "", "Citation": "引用文", "Citations": "", "Clear memory": "メモリをクリア", "Clear Memory": "", "click here": "", "Click here for filter guides.": "", "Click here for help.": "ヘルプについてはここをクリックしてください。", "Click here to": "ここをクリックして", "Click here to download user import template file.": "ユーザーテンプレートをインポートするにはここをクリックしてください。", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to see available models.": "", "Click here to select": "選択するにはここをクリックしてください", "Click here to select a csv file.": "CSVファイルを選択するにはここをクリックしてください。", "Click here to select a py file.": "Pythonスクリプトファイルを選択するにはここをクリックしてください。", "Click here to upload a workflow.json file.": "workflow.jsonファイルをアップロードするにはここをクリックしてください。", "click here.": "ここをクリックしてください。", "Click on the user role button to change a user's role.": "ユーザーの役割を変更するには、ユーザー役割ボタンをクリックしてください。", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "クリップボードへの書き込み許可がありません。ブラウザ設定を確認し許可してください。", "Clone": "クローン", "Clone Chat": "チャットをクローン", "Clone of {{TITLE}}": "{{TITLE}}のクローン", "Close": "閉じる", "Close Banner": "", "Close Configure Connection Modal": "", "Close modal": "", "Close settings modal": "", "Close Sidebar": "", "Code Block": "", "Code execution": "コードの実行", "Code Execution": "コードの実行", "Code Execution Engine": "コードの実行エンジン", "Code Execution Timeout": "コードの実行タイムアウト", "Code formatted successfully": "コードフォーマットに成功しました", "Code Interpreter": "コードインタプリタ", "Code Interpreter Engine": "コードインタプリタエンジン", "Code Interpreter Prompt Template": "コードインタプリタプロンプトテンプレート", "Collapse": "折りたたむ", "Collection": "コレクション", "Color": "色", "ComfyUI": "ComfyUI", "ComfyUI API Key": "ComfyUI APIキー", "ComfyUI Base URL": "ComfyUIベースURL", "ComfyUI Base URL is required.": "ComfyUIベースURLが必要です。", "ComfyUI Workflow": "ComfyUIワークフロー", "ComfyUI Workflow Nodes": "ComfyUIワークフローノード", "Command": "コマンド", "Comment": "", "Completions": "コンプリート", "Compress Images in Channels": "", "Concurrent Requests": "同時リクエスト", "Configure": "設定", "Confirm": "確認", "Confirm Password": "パスワードの確認", "Confirm your action": "あなたのアクションの確認", "Confirm your new password": "新しいパスワードの確認", "Confirm Your Password": "", "Connect to your own OpenAI compatible API endpoints.": "独自のOpenAI互換APIエンドポイントに接続します。", "Connect to your own OpenAPI compatible external tool servers.": "独自のOpenAPI互換外部ツールサーバーに接続します。", "Connection failed": "接続に失敗しました", "Connection successful": "接続に成功しました", "Connection Type": "", "Connections": "接続", "Connections saved successfully": "接続が保存されました", "Connections settings updated": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "", "Contact Admin for WebUI Access": "WEBUIへの接続について管理者に問い合わせ下さい。", "Content": "コンテンツ", "Content Extraction Engine": "", "Continue Response": "続きの応答", "Continue with {{provider}}": "{{provider}}で続ける", "Continue with Email": "メールで続ける", "Continue with LDAP": "LDAPで続ける", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "TTSリクエストのメッセージテキストの分割方法を制御します。'句読点'は文に分割し、'段落'は段落に分割し、'なし'はメッセージを単一の文字列として保持します。", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "生成されたテキストのトークンシーケンスの繰り返しを制御します。値が高いほど（例：1.5）繰り返しをより強くペナルティを課し、値が低いほど（例：1.1）より寛容になります。値が1の場合、無効になります。", "Controls": "コントロール", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "出力のコヒーレンスとdiversityのバランスを制御します。値が低いほど、より焦点が絞られ、一貫性のあるテキストになります。", "Copied": "コピーしました。", "Copied link to clipboard": "", "Copied shared chat URL to clipboard!": "共有チャットURLをクリップボードにコピーしました!", "Copied to clipboard": "クリップボードにコピーしました。", "Copy": "コピー", "Copy Formatted Text": "フォーマットされたテキストをコピー", "Copy last code block": "最後のコードブロックをコピー", "Copy last response": "最後の応答をコピー", "Copy link": "", "Copy Link": "リンクをコピー", "Copy to clipboard": "クリップボードにコピー", "Copying to clipboard was successful!": "クリップボードへのコピーが成功しました！", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "", "Create": "", "Create a knowledge base": "ナレッジベースを作成する", "Create a model": "モデルを作成する", "Create Account": "アカウントを作成", "Create Admin Account": "管理者アカウントを作成", "Create Channel": "チャンネルを作成", "Create Folder": "", "Create Group": "グループを作成", "Create Knowledge": "ナレッジベース作成", "Create new key": "新しいキーを作成", "Create new secret key": "新しいシークレットキーを作成", "Create Note": "ノートを作成", "Create your first note by clicking on the plus button below.": "プラスボタンをクリックして最初のノートを作成します。", "Created at": "作成日時", "Created At": "作成日時", "Created by": "作成者", "CSV Import": "CSVインポート", "Ctrl+Enter to Send": "Ctrl+Enterで送信", "Current Model": "現在のモデル", "Current Password": "現在のパスワード", "Custom": "カスタム", "Custom description enabled": "", "Custom Parameter Name": "", "Custom Parameter Value": "", "Danger Zone": "危険なゾーン", "Dark": "ダーク", "Database": "データベース", "Datalab Marker API": "", "Datalab Marker API Key required.": "", "DD/MM/YYYY": "", "December": "12月", "Default": "デフォルト", "Default (Open AI)": "デフォルト(OpenAI)", "Default (SentenceTransformers)": "デフォルト (SentenceTransformers)", "Default action buttons will be used.": "", "Default description enabled": "", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "", "Default Model": "デフォルトモデル", "Default model updated": "デフォルトモデルが更新されました", "Default Models": "デフォルトモデル", "Default permissions": "デフォルトの許可", "Default permissions updated successfully": "デフォルトの許可が更新されました", "Default Prompt Suggestions": "デフォルトのプロンプトの提案", "Default to 389 or 636 if TLS is enabled": "TLSが有効な場合、389または636にデフォルトを設定します。", "Default to ALL": "デフォルトをALLに設定", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "", "Default User Role": "デフォルトのユーザー役割", "Delete": "削除", "Delete a model": "モデルを削除", "Delete All Chats": "すべてのチャットを削除", "Delete All Models": "すべてのモデルを削除", "Delete chat": "チャットを削除", "Delete Chat": "チャットを削除", "Delete chat?": "チャットを削除しますか？", "Delete folder?": "フォルダーを削除しますか？", "Delete function?": "Functionを削除しますか？", "Delete Message": "メッセージを削除", "Delete message?": "メッセージを削除しますか？", "Delete note?": "", "Delete prompt?": "プロンプトを削除しますか？", "delete this link": "このリンクを削除します", "Delete tool?": "ツールを削除しますか？", "Delete User": "ユーザーを削除", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} を削除しました", "Deleted {{name}}": "{{name}}を削除しました", "Deleted User": "ユーザーを削除", "Deployment names are required for Azure OpenAI": "", "Describe Pictures in Documents": "ドキュメントの画像を説明", "Describe your knowledge base and objectives": "ナレッジベースと目的を説明してください", "Description": "説明", "Detect Artifacts Automatically": "自動的にアーティファクトを検出", "Dictate": "", "Didn't fully follow instructions": "説明に沿って操作していませんでした", "Direct": "直接", "Direct Connections": "ダイレクトコネクション", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "ダイレクトコネクションは、ユーザーが独自のOpenAI互換APIエンドポイントに接続できるようにします。", "Direct Tool Servers": "ダイレクトツールサーバー", "Disable Code Interpreter": "", "Disable Image Extraction": "", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "", "Disabled": "無効", "Discover a function": "Functionを探す", "Discover a model": "モデルを探す", "Discover a prompt": "プロンプトを探す", "Discover a tool": "ツールを探す", "Discover how to use Open WebUI and seek support from the community.": "Open WebUIの使用方法を探し、コミュニティからサポートを求めてください。", "Discover wonders": "", "Discover, download, and explore custom functions": "カスタムFunctionを探してダウンロードする", "Discover, download, and explore custom prompts": "カスタムプロンプトを探してダウンロードする", "Discover, download, and explore custom tools": "カスタムツールを探てしダウンロードする", "Discover, download, and explore model presets": "モデルプリセットを探してダウンロードする", "Display": "", "Display Emoji in Call": "コールで絵文字を表示", "Display Multi-model Responses in Tabs": "", "Display the username instead of You in the Chat": "チャットで「あなた」の代わりにユーザー名を表示", "Displays citations in the response": "応答に引用を表示", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "信頼できないソースからFunctionをインストールしないでください。", "Do not install tools from sources you do not fully trust.": "信頼出来ないソースからツールをインストールしないでください。", "Docling": "", "Docling Server URL required.": "DoclingサーバーURLが必要です。", "Document": "ドキュメント", "Document Intelligence": "ドキュメントインテリジェンス", "Document Intelligence endpoint and key required.": "ドキュメントインテリジェンスエンドポイントとキーが必要です。", "Documentation": "ドキュメント", "Documents": "ドキュメント", "does not make any external connections, and your data stays securely on your locally hosted server.": "外部接続を行わず、データはローカルでホストされているサーバー上に安全に保持されます。", "Domain Filter List": "", "Don't have an account?": "アカウントをお持ちではありませんか？", "don't install random functions from sources you don't trust.": "信頼出来ないソースからランダムFunctionをインストールしないでください。", "don't install random tools from sources you don't trust.": "信頼出来ないソースからランダムツールをインストールしないでください。", "Don't like the style": "デザインが好きでない", "Done": "完了", "Download": "ダウンロード", "Download as SVG": "SVGとしてダウンロード", "Download canceled": "ダウンロードをキャンセルしました", "Download Database": "データベースをダウンロード", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to upload": "", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "例: '30秒'、'10分'。有効な時間単位は '秒'、'分'、'時間' です。", "e.g. \"json\" or a JSON schema": "", "e.g. 60": "", "e.g. A filter to remove profanity from text": "", "e.g. en": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. pdf, docx, txt": "", "e.g. Tools for performing various operations": "", "e.g., 3, 4, 5 (leave blank for default)": "", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "", "e.g., westus (leave blank for eastus)": "", "Edit": "編集", "Edit Arena Model": "Arenaモデルを編集", "Edit Channel": "チャンネルを編集", "Edit Connection": "接続を編集", "Edit Default Permissions": "デフォルトの許可を編集", "Edit Folder": "", "Edit Memory": "メモリを編集", "Edit User": "ユーザーを編集", "Edit User Group": "ユーザーグループを編集", "Edited": "", "Editing": "", "Eject": "", "ElevenLabs": "", "Email": "メールアドレス", "Embark on adventures": "", "Embedding": "", "Embedding Batch Size": "埋め込みモデルバッチサイズ", "Embedding Model": "埋め込みモデル", "Embedding Model Engine": "埋め込みモデルエンジン", "Embedding model set to \"{{embedding_model}}\"": "埋め込みモデルを\"{{embedding_model}}\"に設定しました", "Enable API Key": "APIキーを有効にする", "Enable autocomplete generation for chat messages": "チャットメッセージの自動補完を有効にする", "Enable Code Execution": "コードの実行を有効にする", "Enable Code Interpreter": "コードインタプリタを有効にする", "Enable Community Sharing": "コミュニティ共有を有効にする", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "メッセージ評価を有効にする", "Enable Mirostat sampling for controlling perplexity.": "", "Enable New Sign Ups": "新規登録を有効にする", "Enabled": "有効", "Endpoint URL": "エンドポイントURL", "Enforce Temporary Chat": "一時的なチャットを強制する", "Enhance": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "CSVファイルに4つの列が含まれていることを確認してください: Name, Email, Password, Role.", "Enter {{role}} message here": "{{role}} メッセージをここに入力してください", "Enter a detail about yourself for your LLMs to recall": "LLM が記憶するために、自分についての詳細を入力してください", "Enter a title for the pending user info overlay. Leave empty for default.": "保留中のユーザー情報オーバーレイのタイトルを入力してください。デフォルトのままにする場合は空のままにします。", "Enter a watermark for the response. Leave empty for none.": "", "Enter api auth string (e.g. username:password)": "API AuthStringを入力(例: Username:Password)", "Enter Application DN": "Application DNを入力", "Enter Application DN Password": "Application DNパスワードを入力", "Enter Bing Search V7 Endpoint": "Bing Search V7エンドポイントを入力", "Enter Bing Search V7 Subscription Key": "Bing Search V7サブスクリプションキーを入力", "Enter Bocha Search API Key": "Bocha Search APIキーを入力", "Enter Brave Search API Key": "Brave Search APIキーの入力", "Enter certificate path": "証明書パスを入力", "Enter CFG Scale (e.g. 7.0)": "CFGスケースを入力してください (例: 7.0)", "Enter Chunk Overlap": "チャンクオーバーラップを入力してください", "Enter Chunk Size": "チャンクサイズを入力してください", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "カンマ区切りの \"token:bias_value\" ペアを入力してください (例: 5432:100, 413:-100)", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "保留中のユーザー情報オーバーレイの内容を入力してください。デフォルトのままにする場合は空のままにします。", "Enter Datalab Marker API Base URL": "", "Enter Datalab Marker API Key": "", "Enter description": "説明を入力", "Enter Docling OCR Engine": "Docling OCRエンジンを入力", "Enter Docling OCR Language(s)": "Docling OCR言語を入力", "Enter Docling Server URL": "Docling Server URLを入力", "Enter Document Intelligence Endpoint": "Document Intelligenceエンドポイントを入力", "Enter Document Intelligence Key": "Document Intelligenceキーを入力", "Enter domains separated by commas (e.g., example.com,site.org)": "カンマ区切りのドメインを入力してください (例: example.com,site.org)", "Enter Exa API Key": "Exa APIキーを入力", "Enter External Document Loader API Key": "", "Enter External Document Loader URL": "", "Enter External Web Loader API Key": "External Web Loader APIキーを入力", "Enter External Web Loader URL": "External Web Loader URLを入力", "Enter External Web Search API Key": "External Web Search APIキーを入力", "Enter External Web Search URL": "External Web Search URLを入力", "Enter Firecrawl API Base URL": "Firecrawl API Base URLを入力", "Enter Firecrawl API Key": "Firecrawl APIキーを入力", "Enter folder name": "", "Enter Github Raw URL": "<PERSON><PERSON><PERSON> Raw URLを入力", "Enter Google PSE API Key": "Google PSE APIキーの入力", "Enter Google PSE Engine Id": "Google PSE エンジン ID を入力します。", "Enter Image Size (e.g. 512x512)": "画像サイズを入力してください (例: 512x512)", "Enter Jina API Key": "<PERSON>a APIキーを入力", "Enter JSON config (e.g., {\"disable_links\": true})": "", "Enter Jupyter Password": "Jupyterパスワードを入力", "Enter Jupyter Token": "Jupyterトークンを入力", "Enter Jupyter URL": "<PERSON><PERSON><PERSON> URLを入力", "Enter Kagi Search API Key": "<PERSON><PERSON> Search APIキーを入力", "Enter Key Behavior": "Key Behaviorを入力", "Enter language codes": "言語コードを入力してください", "Enter Mistral API Key": "Mistral APIキーを入力", "Enter Model ID": "モデルIDを入力してください。", "Enter model tag (e.g. {{modelTag}})": "モデルタグを入力してください (例: {{modelTag}})", "Enter Mojeek Search API Key": "Mojeek Search APIキーを入力", "Enter name": "", "Enter New Password": "新しいパスワードを入力", "Enter Number of Steps (e.g. 50)": "ステップ数を入力してください (例: 50)", "Enter Perplexity API Key": "Perplexity APIキーを入力", "Enter Playwright Timeout": "Playwrightタイムアウトを入力", "Enter Playwright WebSocket URL": "Playwright WebSocket URLを入力", "Enter proxy URL (e.g. **************************:port)": "プロキシURLを入力してください (例: **************************:port)", "Enter reasoning effort": "", "Enter Sampler (e.g. Euler a)": "サンプラーを入力してください(e.g. Euler a)。", "Enter Scheduler (e.g. Karras)": "スケジューラーを入力してください。(e.g. <PERSON><PERSON><PERSON>)", "Enter Score": "スコアを入力してください", "Enter SearchApi API Key": "SearchApi API Keyを入力してください。", "Enter SearchApi Engine": "SearchApi Engineを入力してください。", "Enter Searxng Query URL": "SearxngクエリURLを入力", "Enter Seed": "シードを入力", "Enter SerpApi API Key": "SerpApi APIキーを入力", "Enter SerpApi Engine": "SerpApi Engineを入力", "Enter Serper API Key": "Serper APIキーの入力", "Enter Serply API Key": "Serply API Keyを入力してください。", "Enter Serpstack API Key": "Serpstack APIキーの入力", "Enter server host": "サーバーホストを入力", "Enter server label": "サーバーラベルを入力", "Enter server port": "サーバーポートを入力", "Enter Sougou Search API sID": "Sougou Search API sIDを入力", "Enter Sougou Search API SK": "Sougou Search API SKを入力", "Enter stop sequence": "ストップシーケンスを入力してください", "Enter system prompt": "システムプロンプトを入力", "Enter system prompt here": "システムプロンプトをここに入力", "Enter Tavily API Key": "Tavily API Keyを入力してください。", "Enter Tavily Extract Depth": "Tavily Extract Depthを入力してください。", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "WebUIの公開URLを入力してください。このURLは通知でリンクを生成するために使用されます。", "Enter the URL of the function to import": "", "Enter the URL to import": "", "Enter Tika Server URL": "Tika Server URLを入力してください。", "Enter timeout in seconds": "タイムアウトを秒単位で入力してください", "Enter to Send": "送信する", "Enter Top K": "トップ K を入力してください", "Enter Top K Reranker": "トップ K Rerankerを入力してください。", "Enter URL (e.g. http://127.0.0.1:7860/)": "URL を入力してください (例: http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "URL を入力してください (例: http://localhost:11434)", "Enter Yacy Password": "Yacyパスワードを入力してください。", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "Yacy URLを入力してください (例: http://yacy.example.com:8090)", "Enter Yacy Username": "Yacyユーザー名を入力してください。", "Enter your current password": "現在のパスワードを入力してください", "Enter Your Email": "メールアドレスを入力してください", "Enter Your Full Name": "フルネームを入力してください", "Enter your message": "メッセージを入力してください", "Enter your name": "名前を入力してください", "Enter Your Name": "名前を入力してください", "Enter your new password": "新しいパスワードを入力してください", "Enter Your Password": "パスワードを入力してください", "Enter Your Role": "ロールを入力してください", "Enter Your Username": "ユーザー名を入力してください", "Enter your webhook URL": "Webhook URLを入力してください", "Error": "エラー", "ERROR": "エラー", "Error accessing Google Drive: {{error}}": "Google Driveへのアクセスに失敗しました: {{error}}", "Error accessing media devices.": "メディアデバイスへのアクセスに失敗しました。", "Error starting recording.": "録音を開始できませんでした。", "Error unloading model: {{error}}": "", "Error uploading file: {{error}}": "ファイルアップロードに失敗しました: {{error}}", "Evaluations": "評価", "Everyone": "", "Exa API Key": "Exa APIキー", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "例: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "例: ALL", "Example: mail": "例: mail", "Example: ou=users,dc=foo,dc=example": "例: ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "例: sAMAccountName or uid or userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "ライセンスのシート数を超えています。サポートにお問い合わせください。", "Exclude": "除外", "Execute code for analysis": "コードの分析に実行", "Executing **{{NAME}}**...": "**{{NAME}}**を実行中...", "Expand": "展開", "Experimental": "実験的", "Explain": "説明", "Explore the cosmos": "", "Export": "エクスポート", "Export All Archived Chats": "すべてのアーカイブチャットをエクスポート", "Export All Chats (All Users)": "すべてのチャットをエクスポート (すべてのユーザー)", "Export chat (.json)": "チャットをエクスポート(.json)", "Export Chats": "チャットをエクスポート", "Export Config to JSON File": "設定をJSONファイルでエクスポート", "Export Functions": "Functionのエクスポート", "Export Models": "モデルのエクスポート", "Export Presets": "プリセットのエクスポート", "Export Prompt Suggestions": "", "Export Prompts": "プロンプトをエクスポート", "Export to CSV": "CSVにエクスポート", "Export Tools": "ツールのエクスポート", "Export Users": "", "External": "外部", "External Document Loader URL required.": "", "External Task Model": "", "External Web Loader API Key": "External Web Loader APIキー", "External Web Loader URL": "External Web Loader URL", "External Web Search API Key": "External Web Search APIキー", "External Web Search URL": "External Web Search URL", "Fade Effect for Streaming Text": "", "Failed to add file.": "ファイルの追加に失敗しました。", "Failed to connect to {{URL}} OpenAPI tool server": "{{URL}} OpenAPIツールサーバーへの接続に失敗しました。", "Failed to copy link": "", "Failed to create API Key.": "APIキーの作成に失敗しました。", "Failed to delete note": "ノートの削除に失敗しました。", "Failed to extract content from the file: {{error}}": "", "Failed to extract content from the file.": "", "Failed to fetch models": "モデルの取得に失敗しました。", "Failed to generate title": "", "Failed to load chat preview": "", "Failed to load file content.": "ファイルの内容を読み込めませんでした。", "Failed to read clipboard contents": "クリップボードの内容を読み取れませんでした", "Failed to save connections": "接続の保存に失敗しました。", "Failed to save models configuration": "モデルの設定の保存に失敗しました。", "Failed to update settings": "設定アップデート失敗", "Failed to upload file.": "ファイルアップロード失敗", "Features": "", "Features Permissions": "機能の許可", "February": "2月", "Feedback Details": "", "Feedback History": "フィードバック履歴", "Feedbacks": "フィードバック", "Feel free to add specific details": "詳細を追加してください", "File": "ファイル", "File added successfully.": "ファイル追加が成功しました。", "File content updated successfully.": "ファイルコンテンツ追加が成功しました。", "File Mode": "ファイルモード", "File not found.": "ファイルが見つかりません。", "File removed successfully.": "ファイル削除が成功しました。", "File size should not exceed {{maxSize}} MB.": "ファイルサイズ最大値{{maxSize}} MB", "File Upload": "", "File uploaded successfully": "", "Files": "ファイル", "Filter": "", "Filter is now globally disabled": "グローバルフィルタが無効です。", "Filter is now globally enabled": "グローバルフィルタが有効です。", "Filters": "フィルター", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "指紋のなりすましが検出されました: イニシャルをアバターとして使用できません。デフォルトのプロファイル画像にデフォルト設定されています。", "Firecrawl API Base URL": "", "Firecrawl API Key": "Firecrawl APIキー", "Floating Quick Actions": "", "Focus chat input": "チャット入力をフォーカス", "Folder deleted successfully": "フォルダー削除が成功しました。", "Folder Name": "", "Folder name cannot be empty.": "フォルダー名を入力してください。", "Folder name updated successfully": "フォルダー名更新が成功しました。", "Folder updated successfully": "", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "完全に指示に従った", "Force OCR": "", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "新しいパスを作成", "Form": "フォーム", "Format Lines": "", "Format the lines in the output. Defaults to False. If set to True, the lines will be formatted to detect inline math and styles.": "", "Format your variables using brackets like this:": "", "Forwards system user session credentials to authenticate": "", "Full Context Mode": "", "Function": "", "Function Calling": "", "Function created successfully": "Functionの作成が成功しました。", "Function deleted successfully": "Functionの削除が成功しました。", "Function Description": "", "Function ID": "", "Function imported successfully": "", "Function is now globally disabled": "Functionはグローバルで無効です。", "Function is now globally enabled": "Functionはグローバルで有効です。", "Function Name": "", "Function updated successfully": "Functionのアップデートが成功しました。", "Functions": "", "Functions allow arbitrary code execution.": "Functionsは任意のコード実行を許可します。", "Functions imported successfully": "Functionsのインポートが成功しました", "Gemini": "", "Gemini API Config": "", "Gemini API Key is required.": "Gemini APIキーが必要です。", "General": "一般", "Generate": "生成", "Generate an image": "画像を生成", "Generate Image": "画像を生成", "Generate prompt pair": "プロンプトペアを生成", "Generating search query": "検索クエリの生成", "Generating...": "生成中...", "Get information on {{name}} in the UI": "", "Get started": "開始", "Get started with {{WEBUI_NAME}}": "{{WEBUI_NAME}}を開始", "Global": "グローバル", "Good Response": "良い応答", "Google Drive": "", "Google PSE API Key": "Google PSE APIキー", "Google PSE Engine Id": "Google PSE エンジン ID", "Group created successfully": "グループの作成が成功しました。", "Group deleted successfully": "グループの削除が成功しました。", "Group Description": "グループの説明", "Group Name": "グループ名", "Group updated successfully": "グループの更新が成功しました。", "Groups": "グループ", "H1": "", "H2": "", "H3": "", "Haptic Feedback": "触覚フィードバック", "Hello, {{name}}": "こんにちは、{{name}} さん", "Help": "ヘルプ", "Help us create the best community leaderboard by sharing your feedback history!": "フィードバック履歴を共有して、最も優れたコミュニティリーダーボードを作成しましょう！", "Hex Color": "16進数の色", "Hex Color - Leave empty for default color": "デフォルトの色を使用する場合は空のままにしてください", "Hide": "非表示", "Hide from Sidebar": "", "Hide Model": "モデルを非表示", "High Contrast Mode": "", "Home": "ホーム", "Host": "ホスト", "How can I help you today?": "今日はどのようにお手伝いしましょうか？", "How would you rate this response?": "この応答をどのように評価しますか？", "HTML": "", "Hybrid Search": "ブリッジ検索", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "私は、私の行動の結果とその影響を理解しており、任意のコードを実行するリスクを認識しています。ソースの信頼性を確認しました。", "ID": "", "iframe Sandbox Allow Forms": "iframeサンドボックスにフォームを許可", "iframe Sandbox Allow Same Origin": "iframeサンドボックスに同じオリジンを許可", "Ignite curiosity": "", "Image": "画像", "Image Compression": "画像圧縮", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "画像生成", "Image Generation (Experimental)": "画像生成 (実験的)", "Image Generation Engine": "画像生成エンジン", "Image Max Compression Size": "画像最大圧縮サイズ", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "画像プロンプト生成", "Image Prompt Generation Prompt": "画像プロンプト生成プロンプト", "Image Settings": "画像設定", "Images": "画像", "Import": "", "Import Chats": "チャットをインポート", "Import Config from JSON File": "設定をJSONファイルからインポート", "Import From Link": "", "Import Functions": "Functionのインポート", "Import Models": "モデルのインポート", "Import Notes": "ノートをインポート", "Import Presets": "プリセットをインポート", "Import Prompt Suggestions": "", "Import Prompts": "プロンプトをインポート", "Import Tools": "ツールのインポート", "Include": "含める", "Include `--api-auth` flag when running stable-diffusion-webui": "stable-diffusion-webuiを実行する際に`--api-auth`フラグを含める", "Include `--api` flag when running stable-diffusion-webui": "stable-diffusion-webuiを実行する際に`--api`フラグを含める", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "生成されたテキストからのフィードバックに対するアルゴリズムの応答速度を影響します。低い学習率はより遅い調整をもたらし、高い学習率はアルゴリズムをより反応的にします。", "Info": "情報", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "複雑なクエリには、包括的な処理のためにコンテンツ全体をコンテキストとして注入することをお勧めします。", "Input": "", "Input commands": "入力コマンド", "Input Variables": "", "Insert": "", "Insert Follow-Up Prompt to Input": "", "Insert Prompt as Rich Text": "", "Install from Github URL": "Github URLからインストール", "Instant Auto-Send After Voice Transcription": "音声文字変換後に即時自動送信", "Integration": "統合", "Interface": "インターフェース", "Invalid file content": "無効なファイル内容", "Invalid file format.": "無効なファイル形式", "Invalid JSON file": "", "Invalid JSON format in Additional Config": "", "Invalid Tag": "無効なタグ", "is typing...": "入力中...", "Italic": "", "January": "1月", "Jina API Key": "<PERSON>a <PERSON>", "join our Discord for help.": "ヘルプについては、Discord に参加してください。", "JSON": "JSON", "JSON Preview": "JSON プレビュー", "July": "7月", "June": "6月", "Jupyter Auth": "", "Jupyter URL": "", "JWT Expiration": "JWT 有効期限", "JWT Token": "JWT トークン", "Kagi Search API Key": "Kagi Search APIキー", "Keep Follow-Up Prompts in Chat": "", "Keep in Sidebar": "", "Key": "キー", "Keyboard shortcuts": "キーボードショートカット", "Knowledge": "ナレッジベース", "Knowledge Access": "ナレッジアクセス", "Knowledge Base": "", "Knowledge created successfully.": "ナレッジベースの作成に成功しました", "Knowledge deleted successfully.": "ナレッジベースの削除に成功しました", "Knowledge Public Sharing": "ナレッジベースの公開共有", "Knowledge reset successfully.": "ナレッジベースのリセットに成功しました", "Knowledge updated successfully": "ナレッジベースのアップデートに成功しました", "Kokoro.js (Browser)": "Kokoro.js (ブラウザ)", "Kokoro.js Dtype": "Kokoro.js Dtype", "Label": "ラベル", "Landing Page Mode": "ランディングページモード", "Language": "言語", "Language Locales": "言語ロケール", "Last Active": "最終アクティブ", "Last Modified": "最終変更", "Last reply": "最終応答", "LDAP": "LDAP", "LDAP server updated": "LDAPサーバーの更新に成功しました", "Leaderboard": "リーダーボード", "Learn More": "", "Learn more about OpenAPI tool servers.": "OpenAPIツールサーバーについては、こちらをご覧ください。", "Leave empty for no compression": "", "Leave empty for unlimited": "空欄なら無制限", "Leave empty to include all models from \"{{url}}\" endpoint": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "空欄なら「{{url}}/api/tags」エンドポイントからすべてのモデルを含める", "Leave empty to include all models from \"{{url}}/models\" endpoint": "空欄なら「{{url}}/models」エンドポイントからすべてのモデルを含める", "Leave empty to include all models or select specific models": "すべてのモデルを含めるか、特定のモデルを選択", "Leave empty to use the default prompt, or enter a custom prompt": "カスタムプロンプトを入力。空欄ならデフォルトプロンプト", "Leave model field empty to use the default model.": "モデルフィールドを空欄にしてデフォルトモデルを使用", "lexical": "", "License": "ライセンス", "Lift List": "", "Light": "ライト", "Listening...": "聞いています...", "Llama.cpp": "", "LLMs can make mistakes. Verify important information.": "LLM は間違いを犯す可能性があります。重要な情報を検証してください。", "Loader": "ローダー", "Loading Kokoro.js...": "Kokoro.js を読み込んでいます...", "Local": "ローカル", "Local Task Model": "", "Location access not allowed": "位置情報のアクセスが許可されていません", "Lost": "", "LTR": "LTR", "Made by Open WebUI Community": "OpenWebUI コミュニティによって作成", "Make password visible in the user interface": "", "Make sure to enclose them with": "必ず次で囲んでください", "Make sure to export a workflow.json file as API format from ComfyUI.": "", "Manage": "管理", "Manage Direct Connections": "直接接続の管理", "Manage Models": "モデルの管理", "Manage Ollama": "Ollamaの管理", "Manage Ollama API Connections": "Ollama API接続の管理", "Manage OpenAI API Connections": "OpenAI API接続の管理", "Manage Pipelines": "パイプラインの管理", "Manage Tool Servers": "ツールサーバーの管理", "March": "3月", "Markdown": "", "Markdown (Header)": "", "Max Speakers": "", "Max Upload Count": "最大アップロード数", "Max Upload Size": "最大アップロードサイズ", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "同時にダウンロードできるモデルは最大 3 つです。後でもう一度お試しください。", "May": "5月", "Memories accessible by LLMs will be shown here.": "LLM がアクセスできるメモリはここに表示されます。", "Memory": "メモリ", "Memory added successfully": "メモリに追加されました。", "Memory cleared successfully": "メモリをクリアしました。", "Memory deleted successfully": "メモリを削除しました。", "Memory updated successfully": "メモリアップデート成功", "Merge Responses": "応答を統合", "Merged Response": "統合された応答", "Message rating should be enabled to use this feature": "この機能を使用するには、メッセージ評価を有効にする必要があります。", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "リンクを作成した後、送信したメッセージは共有されません。URL を持つユーザーは共有チャットを閲覧できます。", "Microsoft OneDrive": "Microsoft OneDrive", "Microsoft OneDrive (personal)": "Microsoft OneDrive (個人用)", "Microsoft OneDrive (work/school)": "Microsoft OneDrive (職場/学校)", "Mistral OCR": "", "Mistral OCR API Key required.": "", "Model": "モデル", "Model '{{modelName}}' has been successfully downloaded.": "モデル '{{modelName}}' が正常にダウンロードされました。", "Model '{{modelTag}}' is already in queue for downloading.": "モデル '{{modelTag}}' はすでにダウンロード待ち行列に入っています。", "Model {{modelId}} not found": "モデル {{modelId}} が見つかりません", "Model {{modelName}} is not vision capable": "モデル {{modelName}} は視覚に対応していません", "Model {{name}} is now {{status}}": "モデル {{name}} は {{status}} になりました。", "Model {{name}} is now hidden": "モデル {{name}} は非表示になりました。", "Model {{name}} is now visible": "モデル {{name}} は表示されました。", "Model accepts file inputs": "", "Model accepts image inputs": "モデルは画像入力を受け入れます", "Model can execute code and perform calculations": "", "Model can generate images based on text prompts": "", "Model can search the web for information": "", "Model created successfully!": "モデルが正常に作成されました！", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "モデルファイルシステムパスが検出されました。モデルの短縮名が必要です。更新できません。", "Model Filtering": "モデルフィルタリング", "Model ID": "モデルID", "Model ID is required.": "", "Model IDs": "モデルID", "Model Name": "モデル名", "Model Name is required.": "", "Model not selected": "モデルが選択されていません", "Model Params": "モデルパラメータ", "Model Permissions": "モデルの許可", "Model unloaded successfully": "", "Model updated successfully": "モデルが正常に更新されました", "Model(s) do not support file upload": "", "Modelfile Content": "モデルファイルの内容", "Models": "モデル", "Models Access": "モデルアクセス", "Models configuration saved successfully": "モデル設定が正常に保存されました", "Models Public Sharing": "モデルの公開共有", "Mojeek Search API Key": "Mojeek Search APIキー", "more": "もっと見る", "More": "もっと見る", "More Concise": "", "More Options": "", "Name": "名前", "Name your knowledge base": "ナレッジベースの名前を付ける", "Native": "ネイティブ", "New Button": "", "New Chat": "新しいチャット", "New Folder": "新しいフォルダ", "New Function": "", "New Note": "新しいノート", "New Password": "新しいパスワード", "New Tool": "", "new-channel": "新しいチャンネル", "Next message": "", "No chats found": "", "No chats found for this user.": "", "No chats found.": "", "No content": "内容がありません", "No content found": "内容が見つかりません", "No content found in file.": "ファイル内に内容が見つかりません。", "No content to speak": "話す内容がありません", "No distance available": "距離が利用できません", "No feedbacks found": "フィードバックが見つかりません", "No file selected": "ファイルが選択されていません", "No groups with access, add a group to grant access": "アクセス権限があるグループがありません。グループを追加してアクセス権限を付与してください。", "No HTML, CSS, or JavaScript content found.": "HTML、CSS、または<PERSON>avaScriptの内容が見つかりません。", "No inference engine with management support found": "管理サポートのある推論エンジンが見つかりません。", "No knowledge found": "ナレッジベースが見つかりません", "No memories to clear": "クリアするメモリがありません", "No model IDs": "モデルIDがありません", "No models found": "モデルが見つかりません", "No models selected": "モデルが選択されていません", "No Notes": "ノートがありません", "No results found": "結果が見つかりません", "No search query generated": "検索クエリは生成されません", "No source available": "使用可能なソースがありません", "No users were found.": "ユーザーが見つかりません。", "No valves to update": "更新するバルブがありません", "None": "何一つ", "Not factually correct": "実事上正しくない", "Not helpful": "役に立たない", "Note deleted successfully": "ノートが正常に削除されました", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "注意：最小スコアを設定した場合、検索は最小スコア以上のスコアを持つドキュメントのみを返します。", "Notes": "ノート", "Notification Sound": "通知音", "Notification Webhook": "通知Webhook", "Notifications": "デスクトップ通知", "November": "11月", "OAuth ID": "OAuth ID", "October": "10月", "Off": "オフ", "Okay, Let's Go!": "OK、始めましょう！", "OLED Dark": "OLED ダーク", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API settings updated": "Ollama API 設定が更新されました", "Ollama Version": "Ollama バージョン", "On": "オン", "OneDrive": "", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "英数字とハイフンのみが許可されています", "Only alphanumeric characters and hyphens are allowed in the command string.": "コマンド文字列には英数字とハイフンのみが許可されています。", "Only collections can be edited, create a new knowledge base to edit/add documents.": "コレクションのみ編集できます。新しいナレッジベースを作成してドキュメントを編集/追加してください。", "Only markdown files are allowed": "マークダウンファイルのみが許可されています", "Only select users and groups with permission can access": "許可されたユーザーとグループのみがアクセスできます", "Oops! Looks like the URL is invalid. Please double-check and try again.": "おっと！ URL が無効なようです。もう一度確認してやり直してください。", "Oops! There are files still uploading. Please wait for the upload to complete.": "おっと！ アップロードが完了するまで待ってください。", "Oops! There was an error in the previous response.": "おっと！ 前の応答にエラーがありました。", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "おっと！ サポートされていない方法 (フロントエンドのみ) を使用しています。バックエンドから WebUI を提供してください。", "Open file": "ファイルを開く", "Open in full screen": "全画面表示", "Open modal to configure connection": "", "Open Modal To Manage Floating Quick Actions": "", "Open new chat": "新しいチャットを開く", "Open Sidebar": "", "Open User Profile Menu": "", "Open WebUI can use tools provided by any OpenAPI server.": "OpenWebUI は任意のOpenAPI サーバーが提供するツールを使用できます。", "Open WebUI uses faster-whisper internally.": "OpenWebUI は内部でfaster-whisperを使用します。", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "OpenWebUI は SpeechT5とCMU Arctic スピーカー埋め込みを使用します。", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "OpenWebUI のバージョン (v{{OPEN_WEBUI_VERSION}}) は要求されたバージョン (v{{REQUIRED_VERSION}}) より低いです。", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API 設定", "OpenAI API Key is required.": "OpenAI API キーが必要です。", "OpenAI API settings updated": "OpenAI API設定が更新されました", "OpenAI URL/Key required.": "OpenAI URL/Key が必要です。", "openapi.json URL or Path": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "または", "Ordered List": "", "Organize your users": "ユーザーを整理する", "Other": "その他", "OUTPUT": "出力", "Output format": "出力形式", "Output Format": "", "Overview": "概要", "page": "ページ", "Paginate": "", "Parameters": "", "Password": "パスワード", "Passwords do not match.": "", "Paste Large Text as File": "大きなテキストをファイルとして貼り付ける", "PDF document (.pdf)": "PDF ドキュメント (.pdf)", "PDF Extract Images (OCR)": "PDF 画像抽出 (OCR)", "pending": "保留中", "Pending": "", "Pending User Overlay Content": "保留中のユーザー情報オーバーレイの内容", "Pending User Overlay Title": "保留中のユーザー情報オーバーレイのタイトル", "Permission denied when accessing media devices": "メディアデバイスへのアクセス時に権限が拒否されました", "Permission denied when accessing microphone": "マイクへのアクセス時に権限が拒否されました", "Permission denied when accessing microphone: {{error}}": "マイクへのアクセス時に権限が拒否されました: {{error}}", "Permissions": "許可", "Perplexity API Key": "Perplexity API キー", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "個人化", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "ピン留め", "Pinned": "ピン留めされています", "Pioneer insights": "先駆者の洞察", "Pipe": "", "Pipeline deleted successfully": "パイプラインが正常に削除されました", "Pipeline downloaded successfully": "パイプラインが正常にダウンロードされました", "Pipelines": "パイプライン", "Pipelines Not Detected": "パイプラインは検出されませんでした", "Pipelines Valves": "パイプラインバルブ", "Plain text (.md)": "プレーンテキスト (.md)", "Plain text (.txt)": "プレーンテキスト (.txt)", "Playground": "プレイグラウンド", "Playwright Timeout (ms)": "Playwright タイムアウト (ms)", "Playwright WebSocket URL": "Playwright WebSocket URL", "Please carefully review the following warnings:": "次の警告を慎重に確認してください：", "Please do not close the settings page while loading the model.": "モデルの読み込み中に設定ページを閉じないでください。", "Please enter a prompt": "プロンプトを入力してください", "Please enter a valid path": "有効なパスを入力してください", "Please enter a valid URL": "有効なURLを入力してください", "Please fill in all fields.": "すべてのフィールドを入力してください。", "Please select a model first.": "モデルを選択してください。", "Please select a model.": "モデルを選択してください。", "Please select a reason": "理由を選択してください。", "Please wait until all files are uploaded.": "", "Port": "ポート", "Positive attitude": "前向きな態度", "Prefix ID": "Prefix ID", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "Prefix ID はモデル ID に接頭辞を追加することで、他の接続との競合を避けるために使用されます - 空の場合は無効にします", "Prevent file creation": "", "Preview": "", "Previous 30 days": "前の30日間", "Previous 7 days": "前の7日間", "Previous message": "", "Private": "プライベート", "Profile Image": "プロフィール画像", "Prompt": "プロンプト", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "プロンプト（例：ローマ帝国についての楽しい事を教えてください）", "Prompt Autocompletion": "プロンプトの自動補完", "Prompt Content": "プロンプトの内容", "Prompt created successfully": "プロンプトが正常に作成されました", "Prompt suggestions": "プロンプトの提案", "Prompt updated successfully": "プロンプトが正常に更新されました", "Prompts": "プロンプト", "Prompts Access": "プロンプトアクセス", "Prompts Public Sharing": "プロンプトの公開共有", "Public": "公開", "Pull \"{{searchValue}}\" from Ollama.com": "Ollama.com から \"{{searchValue}}\" をプル", "Pull a model from Ollama.com": "Ollama.com からモデルをプル", "Query Generation Prompt": "クエリ生成プロンプト", "Quick Actions": "", "RAG Template": "RAG テンプレート", "Rating": "評価", "Re-rank models by topic similarity": "トピックの類似性に基づいてモデルを再ランク付け", "Read": "読み込む", "Read Aloud": "読み上げ", "Reason": "", "Reasoning Effort": "推理の努力", "Record": "録音", "Record voice": "音声を録音", "Redirecting you to Open WebUI Community": "OpenWebUI コミュニティにリダイレクトしています", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "自分を「User」と呼ぶ（例：「Userはスペイン語を学んでいます」）", "References from": "参照元", "Refused when it shouldn't have": "拒否すべきでないのに拒否した", "Regenerate": "再生成", "Reindex": "再インデックス", "Reindex Knowledge Base Vectors": "ナレッジベースベクターを再インデックス", "Release Notes": "リリースノート", "Releases": "", "Relevance": "関連性", "Relevance Threshold": "関連性の閾値", "Remember Dismissal": "", "Remove": "削除", "Remove {{MODELID}} from list.": "", "Remove file": "", "Remove File": "", "Remove image": "", "Remove Model": "モデルを削除", "Remove this tag from list": "", "Rename": "名前を変更", "Reorder Models": "モデルを並べ替え", "Reply in Thread": "スレッドで返信", "Reranking Engine": "リランクエンジン", "Reranking Model": "リランクモデル", "Reset": "リセット", "Reset All Models": "すべてのモデルをリセット", "Reset Upload Directory": "アップロードディレクトリをリセット", "Reset Vector Storage/Knowledge": "ベクターストレージとナレッジベースをリセット", "Reset view": "表示をリセット", "Response": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "ウェブサイトの許可が拒否されたため、応答通知をアクティブ化できません。必要なアクセスを許可するためにブラウザの設定を訪問してください。", "Response splitting": "応答の分割", "Response Watermark": "", "Result": "結果", "Retrieval": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "チャットのリッチテキスト入力", "RK": "", "Role": "", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "実行", "Running": "実行中", "Save": "保存", "Save & Create": "保存して作成", "Save & Update": "保存して更新", "Save As Copy": "コピーとして保存", "Save Tag": "タグを保存", "Saved": "保存しました。", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "チャットログをブラウザのストレージに直接保存する機能はサポートされなくなりました。下のボタンをクリックして、チャットログをダウンロードして削除してください。ご心配なく。チャットログは、次の方法でバックエンドに簡単に再インポートできます。", "Scroll On Branch Change": "ブランチ変更時にスクロール", "Search": "検索", "Search a model": "モデルを検索", "Search Base": "ベースを検索", "Search Chats": "チャットの検索", "Search Collection": "Collectionの検索", "Search Filters": "フィルターの検索", "search for archived chats": "", "search for folders": "", "search for pinned chats": "", "search for shared chats": "", "search for tags": "タグを検索", "Search Functions": "Functionの検索", "Search In Models": "", "Search Knowledge": "ナレッジベースの検索", "Search Models": "モデル検索", "Search Notes": "", "Search options": "", "Search Prompts": "プロンプトを検索", "Search Result Count": "検索結果数", "Search the internet": "", "Search Tools": "ツールの検索", "SearchApi API Key": "SearchApiのAPIKey", "SearchApi Engine": "SearchApiエンジン", "Searched {{count}} sites": "{{count}} サイトを検索しました", "Searching \"{{searchQuery}}\"": "「{{searchQuery}}」を検索中...", "Searching Knowledge for \"{{searchQuery}}\"": "「{{searchQuery}}」のナレッジを検索中...", "Searching the web...": "ウェブを検索中...", "Searxng Query URL": "Searxng クエリ URL", "See readme.md for instructions": "手順については readme.md を参照してください", "See what's new": "新機能を見る", "Seed": "シード", "Select a base model": "基本モデルの選択", "Select a conversation to preview": "", "Select a engine": "エンジンの選択", "Select a function": "Functionの選択", "Select a group": "グループの選択", "Select a model": "モデルを選択", "Select a pipeline": "パイプラインの選択", "Select a pipeline url": "パイプラインの URL を選択する", "Select a tool": "ツールの選択", "Select an auth method": "認証方法の選択", "Select an Ollama instance": "Ollama インスタンスの選択", "Select Engine": "エンジンの選択", "Select Knowledge": "ナレッジベースの選択", "Select only one model to call": "1つのモデルを呼び出すには、1つのモデルを選択してください。", "Selected model(s) do not support image inputs": "一部のモデルは画像入力をサポートしていません", "semantic": "", "Semantic distance to query": "", "Send": "送信", "Send a Message": "メッセージを送信", "Send message": "メッセージを送信", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "リクエストに`stream_options: { include_usage: true }`を含めます。サポートされているプロバイダーは、リクエストに`stream_options: { include_usage: true }`を含めると、レスポンスにトークン使用情報を返します。", "September": "9月", "SerpApi API Key": "SerpApi APIキー", "SerpApi Engine": "SerpApiエンジン", "Serper API Key": "Serper APIキー", "Serply API Key": "Serply APIキー", "Serpstack API Key": "Serpstack APIキー", "Server connection verified": "サーバー接続が確認されました", "Set as default": "デフォルトに設定", "Set CFG Scale": "CFG Scaleを設定", "Set Default Model": "デフォルトモデルを設定", "Set embedding model": "埋め込みモデルを設定", "Set embedding model (e.g. {{model}})": "埋め込みモデルを設定します（例：{{model}}）", "Set Image Size": "画像サイズを設定", "Set reranking model (e.g. {{model}})": "モデルを設定します（例：{{model}}）", "Set Sampler": "Samplerを設定", "Set Scheduler": "Schedulerを設定", "Set Steps": "ステップを設定", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "音声を設定", "Set whisper model": "whisperモデルを設定", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets how far back for the model to look back to prevent repetition.": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "", "Sets the size of the context window used to generate the next token.": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "設定", "Settings saved successfully!": "設定が正常に保存されました！", "Share": "共有", "Share Chat": "チャットを共有", "Share to Open WebUI Community": "OpenWebUI コミュニティに共有", "Sharing Permissions": "共有許可", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "表示", "Show \"What's New\" modal on login": "ログイン時に「新しいこと」モーダルを表示", "Show Admin Details in Account Pending Overlay": "アカウント保留中の管理者詳細を表示", "Show All": "すべて表示", "Show Formatting Toolbar": "", "Show image preview": "", "Show Less": "表示を減らす", "Show Model": "モデルを表示", "Show shortcuts": "ショートカットを表示", "Show your support!": "サポートを表示", "Showcased creativity": "創造性を披露", "Sign in": "サインイン", "Sign in to {{WEBUI_NAME}}": "{{WEBUI_NAME}}にサインイン", "Sign in to {{WEBUI_NAME}} with LDAP": "{{WEBUI_NAME}}にLDAPでサインイン", "Sign Out": "サインアウト", "Sign up": "サインアップ", "Sign up to {{WEBUI_NAME}}": "{{WEBUI_NAME}}にサインアップ", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to False.": "", "Signing in to {{WEBUI_NAME}}": "{{WEBUI_NAME}}にサインイン", "Sink List": "", "sk-1234": "sk-1234", "Skip Cache": "", "Skip the cache and re-run the inference. Defaults to False.": "", "Sougou Search API sID": "Sougou Search API sID", "Sougou Search API SK": "Sougou Search API SK", "Source": "ソース", "Speech Playback Speed": "音声の再生速度", "Speech recognition error: {{error}}": "音声認識エラー: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "音声テキスト変換エンジン", "Stop": "停止", "Stop Generating": "", "Stop Sequence": "ストップシーケンス", "Stream Chat Response": "チャットレスポンスのストリーム", "Stream Delta Chunk Size": "", "Strikethrough": "", "Strip Existing OCR": "", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "", "STT Model": "STTモデル", "STT Settings": "STT設定", "Stylized PDF Export": "スタイル付きPDFエクスポート", "Subtitle (e.g. about the Roman Empire)": "字幕 (例: ローマ帝国)", "Success": "成功", "Successfully updated.": "正常に更新されました。", "Suggest a change": "", "Suggested": "提案", "Support": "サポート", "Support this plugin:": "このプラグインをサポートする:", "Supported MIME Types": "", "Sync directory": "同期ディレクトリ", "System": "システム", "System Instructions": "システムインストラクション", "System Prompt": "システムプロンプト", "Tags": "タグ", "Tags Generation": "タグ生成", "Tags Generation Prompt": "タグ生成プロンプト", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "", "Talk to model": "モデルと話す", "Tap to interrupt": "タップして中断", "Task List": "", "Task Model": "", "Tasks": "タスク", "Tavily API Key": "Tavily APIキー", "Tavily Extract Depth": "Tavily抽出深度", "Tell us more:": "もっと話してください:", "Temperature": "温度", "Temporary Chat": "一時的なチャット", "Text Splitter": "", "Text-to-Speech": "", "Text-to-Speech Engine": "テキスト音声変換エンジン", "Thanks for your feedback!": "ご意見ありがとうございます！", "The Application Account DN you bind with for search": "LDAP属性を使用してユーザーを検索するためにバインドするアプリケーションアカウントのDN。", "The base to search for users": "ユーザーを検索するためのベース。", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "バッチサイズは一度に処理されるテキストリクエストの数を決定します。バッチサイズを高くすると、モデルのパフォーマンスと速度が向上しますが、メモリの使用量も増加します。", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "このプラグインはコミュニティの熱意のあるボランティアによって開発されています。このプラグインがお役に立った場合は、開発に貢献することを検討してください。", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "評価リーダーボードはElo評価システムに基づいており、実時間で更新されています。", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "ユーザーがサインインに使用するメールのLDAP属性。", "The LDAP attribute that maps to the username that users use to sign in.": "ユーザーがサインインに使用するユーザー名のLDAP属性。", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "リーダーボードは現在ベータ版であり、アルゴリズムを改善する際に評価計算を調整する場合があります。", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "ファイルの最大サイズはMBです。ファイルサイズがこの制限を超える場合、ファイルはアップロードされません。", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "一度に使用できるファイルの最大数。ファイルの数がこの制限を超える場合、ファイルはアップロードされません。", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "スコアは0.0(0%)から1.0(100%)の間の値にしてください。", "The stream delta chunk size for the model. Increasing the chunk size will make the model respond with larger pieces of text at once.": "", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "モデルのtemperature。温度を上げるとモデルはより創造的に回答します。", "The Weight of BM25 Hybrid Search. 0 more lexical, 1 more semantic. Default 0.5": "", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "テーマ", "Thinking...": "思考中...", "This action cannot be undone. Do you wish to continue?": "このアクションは取り消し不可です。続けますか？", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "このチャンネルは{{createdAt}}に作成されました。これは{{channelName}}チャンネルの始まりです。", "This chat won't appear in history and your messages will not be saved.": "このチャットは履歴に表示されず、メッセージは保存されません。", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "これは、貴重な会話がバックエンドデータベースに安全に保存されることを保証します。ありがとうございます！", "This feature is experimental and may be modified or discontinued without notice.": "", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "実験的機能であり正常動作しない場合があります。", "This model is not publicly available. Please select another model.": "このモデルは公開されていません。別のモデルを選択してください。", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "このオプションは、コンテキストをリフレッシュする際に保持するトークンの数を制御します。例えば、2に設定すると、会話のコンテキストの最後の2つのトークンが保持されます。コンテキストを保持することで、会話の継続性を維持できますが、新しいトピックに応答する能力を低下させる可能性があります。", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "このオプションは、モデルが生成できるトークンの最大数を設定します。この制限を増加すると、モデルはより長い回答を生成できるようになりますが、不適切な内容や関連性の低い内容が生成される可能性も高まります。", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "このレスポンスは\"{{model}}\"によって生成されました。", "This will delete": "", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "これは<strong>{{NAME}}</strong>と<strong>すべての内容</strong>を削除します。", "This will delete all models including custom models": "これはカスタムモデルを含むすべてのモデルを削除します", "This will delete all models including custom models and cannot be undone.": "これはカスタムモデルを含むすべてのモデルを削除し、元に戻すことはできません。", "This will reset the knowledge base and sync all files. Do you wish to continue?": "これは知識ベースをリセットし、すべてのファイルを同期します。続けますか？", "Thorough explanation": "詳細な説明", "Thought for {{DURATION}}": "{{DURATION}}秒間の思考", "Thought for {{DURATION}} seconds": "{{DURATION}}秒間の思考", "Thought for less than a second": "", "Tika": "", "Tika Server URL required.": "Tika Server URLが必要です。", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "ヒント: 各置換後にチャット入力で Tab キーを押すことで、複数の変数スロットを連続して更新できます。", "Title": "タイトル", "Title (e.g. Tell me a fun fact)": "タイトル (例: 楽しい事を教えて)", "Title Auto-Generation": "タイトル自動生成", "Title cannot be an empty string.": "タイトルは空文字列にできません。", "Title Generation": "タイトル生成", "Title Generation Prompt": "タイトル生成プロンプト", "TLS": "", "To access the available model names for downloading,": "ダウンロード可能なモデル名にアクセスするには、", "To access the GGUF models available for downloading,": "ダウンロード可能な GGUF モデルにアクセスするには、", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "WebUIにアクセスするには、管理者にお問い合わせください。管理者は管理者パネルからユーザーのステータスを管理できます。", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "ここに知識ベースを添付するには、まず \"Knowledge\" ワークスペースに追加してください。", "To learn more about available endpoints, visit our documentation.": "利用可能なエンドポイントについては、ドキュメントを参照してください。", "To learn more about powerful prompt variables, click here": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "プライバシーを保護するため、評価、モデル ID、タグ、およびメタデータのみがフィードバックから共有されます。— チャットログはプライバシーを保護され、含まれません。", "To select actions here, add them to the \"Functions\" workspace first.": "ここでアクションを選択するには、まず \"Functions\" ワークスペースに追加してください。", "To select filters here, add them to the \"Functions\" workspace first.": "ここでフィルターを選択するには、まず \"Functions\" ワークスペースに追加してください。", "To select toolkits here, add them to the \"Tools\" workspace first.": "ここでツールキットを選択するには、まず \"Tools\" ワークスペースに追加してください。", "Toast notifications for new updates": "新しい更新のトースト通知", "Today": "今日", "Toggle search": "", "Toggle settings": "設定を切り替え", "Toggle sidebar": "サイドバーを切り替え", "Toggle whether current connection is active.": "", "Token": "トークン", "Too verbose": "冗長すぎます", "Tool created successfully": "ツールが正常に作成されました", "Tool deleted successfully": "ツールが正常に削除されました", "Tool Description": "ツールの説明", "Tool ID": "ツール ID", "Tool imported successfully": "ツールが正常にインポートされました", "Tool Name": "ツール名", "Tool Servers": "ツールサーバー", "Tool updated successfully": "ツールが正常に更新されました", "Tools": "ツール", "Tools Access": "ツールアクセス", "Tools are a function calling system with arbitrary code execution": "ツールは任意のコード実行を可能にする関数呼び出しシステムです。", "Tools Function Calling Prompt": "ツール関数呼び出しプロンプト", "Tools have a function calling system that allows arbitrary code execution.": "ツールは任意のコード実行を可能にする関数呼び出しシステムを持っています。", "Tools Public Sharing": "ツールの公開共有", "Top K": "トップ K", "Top K Reranker": "", "Transformers": "", "Trouble accessing Ollama?": "Ollama へのアクセスに問題がありますか？", "Trust Proxy Environment": "プロキシ環境を信頼する", "Try Again": "", "TTS Model": "TTSモデル", "TTS Settings": "TTS 設定", "TTS Voice": "TTSボイス", "Type": "種類", "Type Hugging Face Resolve (Download) URL": "Hugging Face Resolve (ダウンロード) URL を入力してください", "Uh-oh! There was an issue with the response.": "レスポンスに問題がありました。", "UI": "", "Unarchive All": "すべてのアーカイブされたチャットをアーカイブ解除", "Unarchive All Archived Chats": "すべてのアーカイブされたチャットをアーカイブ解除", "Unarchive Chat": "チャットをアーカイブ解除", "Underline": "", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "", "Unpin": "ピン留め解除", "Unravel secrets": "", "Unsupported file type.": "", "Untagged": "", "Untitled": "タイトルなし", "Update": "更新", "Update and Copy Link": "リンクの更新とコピー", "Update for the latest features and improvements.": "最新の機能と改善点を更新します。", "Update password": "パスワードを更新", "Updated": "更新されました", "Updated at": "更新日時", "Updated At": "更新日時", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "高度な機能を備えたライセンス付きプランにアップグレードしてください。", "Upload": "アップロード", "Upload a GGUF model": "GGUF モデルをアップロード", "Upload Audio": "", "Upload directory": "アップロードディレクトリ", "Upload files": "アップロードファイル", "Upload Files": "ファイルのアップロード", "Upload Pipeline": "アップロードパイプライン", "Upload Progress": "アップロードの進行状況", "URL": "", "URL Mode": "URL モード", "Usage": "", "Use '#' in the prompt input to load and include your knowledge.": "#を入力するとナレッジベースを参照することが出来ます。", "Use Gravatar": "Gravatar を使用する", "Use groups to group your users and assign permissions.": "グループを使用してユーザーをグループ化し、権限を割り当てます。", "Use Initials": "初期値を使用する", "Use LLM": "", "Use no proxy to fetch page contents.": "ページの内容を取得するためにプロキシを使用しません。", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "http_proxy と https_proxy 環境変数で指定されたプロキシを使用してページの内容を取得します。", "user": "ユーザー", "User": "ユーザー", "User Groups": "", "User location successfully retrieved.": "ユーザーの位置情報が正常に取得されました。", "User menu": "", "User Webhooks": "", "Username": "ユーザー名", "Users": "ユーザー", "Using Entire Document": "", "Using Focused Retrieval": "", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Valid time units:": "有効な時間単位:", "Valves": "", "Valves updated": "バルブが更新されました", "Valves updated successfully": "バルブが正常に更新されました", "variable": "変数", "Verify Connection": "接続を確認", "Verify SSL Certificate": "SSL証明書を確認", "Version": "バージョン", "Version {{selectedVersion}} of {{totalVersions}}": "", "View Replies": "リプライを表示", "View Result from **{{NAME}}**": "**{{NAME}}**の結果を表示", "Visibility": "", "Vision": "", "Voice": "ボイス", "Voice Input": "音声入力", "Voice mode": "", "Warning": "警告", "Warning:": "警告:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "警告: これを有効にすると、ユーザーがサーバー上で任意のコードをアップロードできるようになります。", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "警告: 埋め込みモデルを更新または変更した場合は、すべてのドキュメントを再インポートする必要があります。", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "", "Web": "ウェブ", "Web API": "ウェブAPI", "Web Loader Engine": "", "Web Search": "ウェブ検索", "Web Search Engine": "ウェブ検索エンジン", "Web Search in Chat": "チャットでウェブ検索", "Web Search Query Generation": "ウェブ検索クエリ生成", "Webhook URL": "Webhook URL", "WebUI Settings": "WebUI 設定", "WebUI URL": "", "WebUI will make requests to \"{{url}}\"": "WebUIは\"{{url}}\"にリクエストを行います", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUIは\"{{url}}/api/chat\"にリクエストを行います", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUIは\"{{url}}/chat/completions\"にリクエストを行います", "What are you trying to achieve?": "", "What are you working on?": "", "What's New in": "新機能", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "", "Whisper (Local)": "<PERSON><PERSON><PERSON> (ローカ<PERSON>)", "Why?": "", "Widescreen Mode": "ワイドスクリーンモード", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "", "Workspace": "ワークスペース", "Workspace Permissions": "ワークスペースの許可", "Write": "", "Write a prompt suggestion (e.g. Who are you?)": "プロンプトの提案を書いてください (例: あなたは誰ですか？)", "Write a summary in 50 words that summarizes [topic or keyword].": "[トピックまたはキーワード] を要約する 50 語の概要を書いてください。", "Write something...": "何かを書いてください...", "Yacy Instance URL": "YacyインスタンスURL", "Yacy Password": "Yacyパスワード", "Yacy Username": "Yacyユーザー名", "Yesterday": "昨日", "You": "あなた", "You are currently using a trial license. Please contact support to upgrade your license.": "現在、試用ライセンスを使用しています。サポートにお問い合わせください。", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "一度に最大{{maxCount}}のファイルしかチャットできません。", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "メモリを追加することで、LLMとの対話をカスタマイズできます。", "You cannot upload an empty file.": "空のファイルをアップロードできません。", "You do not have permission to upload files.": "ファイルをアップロードする権限がありません。", "You have no archived conversations.": "これまでにアーカイブされた会話はありません。", "You have shared this chat": "このチャットを共有しました", "You're a helpful assistant.": "あなたは有能なアシスタントです。", "You're now logged in.": "ログインしました。", "Your account status is currently pending activation.": "あなたのアカウント状態は現在登録認証待ちです。", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "", "Youtube": "YouTube", "Youtube Language": "YouTubeの言語", "Youtube Proxy URL": "YouTubeのプロキシURL"}