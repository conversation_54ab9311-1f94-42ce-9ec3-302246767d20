{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "", "(e.g. `sh webui.sh --api --api-auth username_password`)": "", "(e.g. `sh webui.sh --api`)": "", "(latest)": "", "(leave blank for to use commercial endpoint)": "", "[Last] dddd [at] h:mm A": "", "[Today at] h:mm A": "", "[Yesterday at] h:mm A": "", "{{ models }}": "", "{{COUNT}} Available Tools": "", "{{COUNT}} characters": "", "{{COUNT}} hidden lines": "", "{{COUNT}} Replies": "", "{{COUNT}} words": "", "{{user}}'s Chats": "", "{{webUIName}} Backend Required": "", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "", "A task model is used when performing tasks such as generating titles for chats and web search queries": "", "a user": "", "About": "", "Accept autocomplete generation / Jump to prompt variable": "", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "", "Account Activation Pending": "", "Accurate information": "", "Action": "", "Actions": "", "Activate": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "", "Add": "", "Add a model ID": "", "Add a short description about what this model does": "", "Add a tag": "", "Add Arena Model": "", "Add Connection": "", "Add Content": "", "Add content here": "", "Add Custom Parameter": "", "Add custom prompt": "", "Add Details": "", "Add Files": "", "Add Group": "", "Add Memory": "", "Add Model": "", "Add Reaction": "", "Add Tag": "", "Add Tags": "", "Add text content": "", "Add User": "", "Add User Group": "", "Additional Config": "", "Additional configuration options for marker. This should be a JSON string with key-value pairs. For example, '{\"key\": \"value\"}'. Supported keys include: disable_links, keep_pageheader_in_output, keep_pagefooter_in_output, filter_blank_pages, drop_repeated_text, layout_coverage_threshold, merge_threshold, height_tolerance, gap_threshold, image_threshold, min_line_length, level_count, default_level": "", "Adjusting these settings will apply changes universally to all users.": "", "admin": "", "Admin": "", "Admin Panel": "", "Admin Settings": "", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "", "Advanced Parameters": "", "Advanced Params": "", "AI": "", "All": "", "All Documents": "", "All models deleted successfully": "", "Allow Call": "", "Allow Chat Controls": "", "Allow Chat Delete": "", "Allow Chat Deletion": "", "Allow Chat Edit": "", "Allow Chat Export": "", "Allow Chat Params": "", "Allow Chat Share": "", "Allow Chat System Prompt": "", "Allow Chat Valves": "", "Allow File Upload": "", "Allow Multiple Models in Chat": "", "Allow non-local voices": "", "Allow Speech to Text": "", "Allow Temporary Chat": "", "Allow Text to Speech": "", "Allow User Location": "", "Allow Voice Interruption in Call": "", "Allowed Endpoints": "", "Allowed File Extensions": "", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "", "Already have an account?": "", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "", "Always": "", "Always Collapse Code Blocks": "", "Always Expand Details": "", "Always Play Notification Sound": "", "Amazing": "", "an assistant": "", "Analytics": "", "Analyzed": "Analysed", "Analyzing...": "Analysing", "and": "", "and {{COUNT}} more": "", "and create a new shared link.": "", "Android": "", "API": "", "API Base URL": "", "API Base URL for Datalab Marker service. Defaults to: https://www.datalab.to/api/v1/marker": "", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "", "API Key created.": "", "API Key Endpoint Restrictions": "", "API keys": "", "API Version": "", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "", "Archive": "", "Archive All Chats": "", "Archived Chats": "", "archived-chat-export": "", "Are you sure you want to clear all memories? This action cannot be undone.": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "", "Arena Models": "", "Artifacts": "", "Ask": "", "Ask a question": "", "Assistant": "", "Attach file from knowledge": "", "Attention to detail": "", "Attribute for Mail": "", "Attribute for Username": "", "Audio": "", "August": "", "Auth": "", "Authenticate": "", "Authentication": "", "Auto": "", "Auto-Copy Response to Clipboard": "", "Auto-playback response": "", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "", "AUTOMATIC1111 Api Auth String": "", "AUTOMATIC1111 Base URL": "", "AUTOMATIC1111 Base URL is required.": "", "Available list": "", "Available Tools": "", "available!": "", "Awful": "", "Azure AI Speech": "", "Azure Region": "", "Back": "", "Bad Response": "", "Banners": "", "Base Model (From)": "", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "", "before": "", "Being lazy": "", "Beta": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "BM25 Weight": "", "Bocha Search API Key": "", "Bold": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "Boosting or penalising specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "", "Brave Search API Key": "", "Bullet List": "", "Button ID": "", "Button Label": "", "Button Prompt": "", "By {{name}}": "", "Bypass Embedding and Retrieval": "", "Bypass Web Loader": "", "Cache Base Model List": "", "Calendar": "", "Call": "", "Call feature is not supported when using Web STT engine": "", "Camera": "", "Cancel": "", "Capabilities": "", "Capture": "", "Capture Audio": "", "Certificate Path": "", "Change Password": "", "Channel Name": "", "Channels": "", "Character": "", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "", "Chat Background Image": "", "Chat Bubble UI": "", "Chat Controls": "", "Chat direction": "", "Chat ID": "", "Chat Overview": "", "Chat Permissions": "", "Chat Tags Auto-Generation": "", "Chats": "", "Check Again": "", "Check for updates": "", "Checking for updates...": "", "Choose a model before saving...": "", "Chunk Overlap": "", "Chunk Size": "", "Ciphers": "", "Citation": "", "Citations": "", "Clear memory": "", "Clear Memory": "", "click here": "", "Click here for filter guides.": "", "Click here for help.": "", "Click here to": "", "Click here to download user import template file.": "", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to see available models.": "", "Click here to select": "", "Click here to select a csv file.": "", "Click here to select a py file.": "", "Click here to upload a workflow.json file.": "", "click here.": "", "Click on the user role button to change a user's role.": "", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "", "Clone": "", "Clone Chat": "", "Clone of {{TITLE}}": "", "Close": "", "Close Banner": "", "Close Configure Connection Modal": "", "Close modal": "", "Close settings modal": "", "Close Sidebar": "", "Code Block": "", "Code execution": "", "Code Execution": "", "Code Execution Engine": "", "Code Execution Timeout": "", "Code formatted successfully": "", "Code Interpreter": "", "Code Interpreter Engine": "", "Code Interpreter Prompt Template": "", "Collapse": "", "Collection": "", "Color": "Colour", "ComfyUI": "", "ComfyUI API Key": "", "ComfyUI Base URL": "", "ComfyUI Base URL is required.": "", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "", "Command": "", "Comment": "", "Completions": "", "Compress Images in Channels": "", "Concurrent Requests": "", "Configure": "", "Confirm": "", "Confirm Password": "", "Confirm your action": "", "Confirm your new password": "", "Confirm Your Password": "", "Connect to your own OpenAI compatible API endpoints.": "", "Connect to your own OpenAPI compatible external tool servers.": "", "Connection failed": "", "Connection successful": "", "Connection Type": "", "Connections": "", "Connections saved successfully": "", "Connections settings updated": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "", "Contact Admin for WebUI Access": "", "Content": "", "Content Extraction Engine": "", "Continue Response": "", "Continue with {{provider}}": "", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalise repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.", "Controls": "", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "", "Copied": "", "Copied link to clipboard": "", "Copied shared chat URL to clipboard!": "", "Copied to clipboard": "", "Copy": "", "Copy Formatted Text": "", "Copy last code block": "", "Copy last response": "", "Copy link": "", "Copy Link": "", "Copy to clipboard": "", "Copying to clipboard was successful!": "", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "", "Create": "", "Create a knowledge base": "", "Create a model": "", "Create Account": "", "Create Admin Account": "", "Create Channel": "", "Create Folder": "", "Create Group": "", "Create Knowledge": "", "Create new key": "", "Create new secret key": "", "Create Note": "", "Create your first note by clicking on the plus button below.": "", "Created at": "", "Created At": "", "Created by": "", "CSV Import": "", "Ctrl+Enter to Send": "", "Current Model": "", "Current Password": "", "Custom": "", "Custom description enabled": "", "Custom Parameter Name": "", "Custom Parameter Value": "", "Danger Zone": "", "Dark": "", "Database": "", "Datalab Marker API": "", "Datalab Marker API Key required.": "", "DD/MM/YYYY": "", "December": "", "Default": "", "Default (Open AI)": "", "Default (SentenceTransformers)": "", "Default action buttons will be used.": "", "Default description enabled": "", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "", "Default Model": "", "Default model updated": "", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "", "Default User Role": "", "Delete": "", "Delete a model": "", "Delete All Chats": "", "Delete All Models": "", "Delete chat": "", "Delete Chat": "", "Delete chat?": "", "Delete folder?": "", "Delete function?": "", "Delete Message": "", "Delete message?": "", "Delete note?": "", "Delete prompt?": "", "delete this link": "", "Delete tool?": "", "Delete User": "", "Deleted {{deleteModelTag}}": "", "Deleted {{name}}": "", "Deleted User": "", "Deployment names are required for Azure OpenAI": "", "Describe Pictures in Documents": "", "Describe your knowledge base and objectives": "", "Description": "", "Detect Artifacts Automatically": "", "Dictate": "", "Didn't fully follow instructions": "", "Direct": "", "Direct Connections": "", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "", "Direct Tool Servers": "", "Disable Code Interpreter": "", "Disable Image Extraction": "", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "", "Disabled": "", "Discover a function": "", "Discover a model": "", "Discover a prompt": "", "Discover a tool": "", "Discover how to use Open WebUI and seek support from the community.": "", "Discover wonders": "", "Discover, download, and explore custom functions": "", "Discover, download, and explore custom prompts": "", "Discover, download, and explore custom tools": "", "Discover, download, and explore model presets": "", "Display": "", "Display Emoji in Call": "", "Display Multi-model Responses in Tabs": "", "Display the username instead of You in the Chat": "", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "", "Do not install tools from sources you do not fully trust.": "", "Docling": "", "Docling Server URL required.": "", "Document": "", "Document Intelligence": "", "Document Intelligence endpoint and key required.": "", "Documentation": "", "Documents": "", "does not make any external connections, and your data stays securely on your locally hosted server.": "", "Domain Filter List": "", "Don't have an account?": "", "don't install random functions from sources you don't trust.": "", "don't install random tools from sources you don't trust.": "", "Don't like the style": "", "Done": "", "Download": "", "Download as SVG": "", "Download canceled": "", "Download Database": "", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to upload": "", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "", "e.g. \"json\" or a JSON schema": "", "e.g. 60": "", "e.g. A filter to remove profanity from text": "", "e.g. en": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. pdf, docx, txt": "", "e.g. Tools for performing various operations": "", "e.g., 3, 4, 5 (leave blank for default)": "", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "", "e.g., westus (leave blank for eastus)": "", "Edit": "", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Folder": "", "Edit Memory": "", "Edit User": "", "Edit User Group": "", "Edited": "", "Editing": "", "Eject": "", "ElevenLabs": "", "Email": "", "Embark on adventures": "", "Embedding": "", "Embedding Batch Size": "", "Embedding Model": "", "Embedding Model Engine": "", "Embedding model set to \"{{embedding_model}}\"": "", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Code Execution": "", "Enable Code Interpreter": "", "Enable Community Sharing": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "", "Enable Mirostat sampling for controlling perplexity.": "", "Enable New Sign Ups": "", "Enabled": "", "Endpoint URL": "", "Enforce Temporary Chat": "", "Enhance": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "", "Enter {{role}} message here": "", "Enter a detail about yourself for your LLMs to recall": "", "Enter a title for the pending user info overlay. Leave empty for default.": "", "Enter a watermark for the response. Leave empty for none.": "", "Enter api auth string (e.g. username:password)": "", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Bocha Search API Key": "", "Enter Brave Search API Key": "", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "", "Enter Chunk Overlap": "", "Enter Chunk Size": "", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "", "Enter Datalab Marker API Base URL": "", "Enter Datalab Marker API Key": "", "Enter description": "", "Enter Docling OCR Engine": "", "Enter Docling OCR Language(s)": "", "Enter Docling Server URL": "", "Enter Document Intelligence Endpoint": "", "Enter Document Intelligence Key": "", "Enter domains separated by commas (e.g., example.com,site.org)": "", "Enter Exa API Key": "", "Enter External Document Loader API Key": "", "Enter External Document Loader URL": "", "Enter External Web Loader API Key": "", "Enter External Web Loader URL": "", "Enter External Web Search API Key": "", "Enter External Web Search URL": "", "Enter Firecrawl API Base URL": "", "Enter Firecrawl API Key": "", "Enter folder name": "", "Enter Github Raw URL": "", "Enter Google PSE API Key": "", "Enter Google PSE Engine Id": "", "Enter Image Size (e.g. 512x512)": "", "Enter Jina API Key": "", "Enter JSON config (e.g., {\"disable_links\": true})": "", "Enter Jupyter Password": "", "Enter Jupyter Token": "", "Enter Jupyter URL": "", "Enter Kagi Search API Key": "", "Enter Key Behavior": "Enter Key Behaviour", "Enter language codes": "", "Enter Mistral API Key": "", "Enter Model ID": "", "Enter model tag (e.g. {{modelTag}})": "", "Enter Mojeek Search API Key": "", "Enter name": "", "Enter New Password": "", "Enter Number of Steps (e.g. 50)": "", "Enter Perplexity API Key": "", "Enter Playwright Timeout": "", "Enter Playwright WebSocket URL": "", "Enter proxy URL (e.g. **************************:port)": "", "Enter reasoning effort": "", "Enter Sampler (e.g. Euler a)": "", "Enter Scheduler (e.g. Karras)": "", "Enter Score": "", "Enter SearchApi API Key": "", "Enter SearchApi Engine": "", "Enter Searxng Query URL": "", "Enter Seed": "", "Enter SerpApi API Key": "", "Enter SerpApi Engine": "", "Enter Serper API Key": "", "Enter Serply API Key": "", "Enter Serpstack API Key": "", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter Sougou Search API sID": "", "Enter Sougou Search API SK": "", "Enter stop sequence": "", "Enter system prompt": "", "Enter system prompt here": "", "Enter Tavily API Key": "", "Enter Tavily Extract Depth": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter the URL of the function to import": "", "Enter the URL to import": "", "Enter Tika Server URL": "", "Enter timeout in seconds": "", "Enter to Send": "", "Enter Top K": "", "Enter Top K Reranker": "", "Enter URL (e.g. http://127.0.0.1:7860/)": "", "Enter URL (e.g. http://localhost:11434)": "", "Enter Yacy Password": "", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "", "Enter Yacy Username": "", "Enter your current password": "", "Enter Your Email": "", "Enter Your Full Name": "", "Enter your message": "", "Enter your name": "", "Enter Your Name": "", "Enter your new password": "", "Enter Your Password": "", "Enter Your Role": "", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "", "ERROR": "", "Error accessing Google Drive: {{error}}": "", "Error accessing media devices.": "", "Error starting recording.": "", "Error unloading model: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "", "Everyone": "", "Exa API Key": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: mail": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "", "Exclude": "", "Execute code for analysis": "", "Executing **{{NAME}}**...": "", "Expand": "", "Experimental": "", "Explain": "", "Explore the cosmos": "", "Export": "", "Export All Archived Chats": "", "Export All Chats (All Users)": "", "Export chat (.json)": "", "Export Chats": "", "Export Config to JSON File": "", "Export Functions": "", "Export Models": "", "Export Presets": "", "Export Prompt Suggestions": "", "Export Prompts": "", "Export to CSV": "", "Export Tools": "", "Export Users": "", "External": "", "External Document Loader URL required.": "", "External Task Model": "", "External Web Loader API Key": "", "External Web Loader URL": "", "External Web Search API Key": "", "External Web Search URL": "", "Fade Effect for Streaming Text": "", "Failed to add file.": "", "Failed to connect to {{URL}} OpenAPI tool server": "", "Failed to copy link": "", "Failed to create API Key.": "", "Failed to delete note": "", "Failed to extract content from the file: {{error}}": "", "Failed to extract content from the file.": "", "Failed to fetch models": "", "Failed to generate title": "", "Failed to load chat preview": "", "Failed to load file content.": "", "Failed to read clipboard contents": "", "Failed to save connections": "", "Failed to save models configuration": "", "Failed to update settings": "", "Failed to upload file.": "", "Features": "", "Features Permissions": "", "February": "", "Feedback Details": "", "Feedback History": "", "Feedbacks": "", "Feel free to add specific details": "", "File": "", "File added successfully.": "", "File content updated successfully.": "", "File Mode": "", "File not found.": "", "File removed successfully.": "", "File size should not exceed {{maxSize}} MB.": "", "File Upload": "", "File uploaded successfully": "", "Files": "", "Filter": "", "Filter is now globally disabled": "", "Filter is now globally enabled": "", "Filters": "", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "", "Firecrawl API Base URL": "", "Firecrawl API Key": "", "Floating Quick Actions": "", "Focus chat input": "", "Folder deleted successfully": "", "Folder Name": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Folder updated successfully": "", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "", "Force OCR": "", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "", "Form": "", "Format Lines": "", "Format the lines in the output. Defaults to False. If set to True, the lines will be formatted to detect inline math and styles.": "", "Format your variables using brackets like this:": "", "Forwards system user session credentials to authenticate": "", "Full Context Mode": "", "Function": "", "Function Calling": "", "Function created successfully": "", "Function deleted successfully": "", "Function Description": "", "Function ID": "", "Function imported successfully": "", "Function is now globally disabled": "", "Function is now globally enabled": "", "Function Name": "", "Function updated successfully": "", "Functions": "", "Functions allow arbitrary code execution.": "", "Functions imported successfully": "", "Gemini": "", "Gemini API Config": "", "Gemini API Key is required.": "", "General": "", "Generate": "", "Generate an image": "", "Generate Image": "", "Generate prompt pair": "", "Generating search query": "", "Generating...": "", "Get information on {{name}} in the UI": "", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "", "Good Response": "", "Google Drive": "", "Google PSE API Key": "", "Google PSE Engine Id": "", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "H1": "", "H2": "", "H3": "", "Haptic Feedback": "", "Hello, {{name}}": "", "Help": "", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "Hex Colour", "Hex Color - Leave empty for default color": "Hex Colour - Leave empty for default colour", "Hide": "", "Hide from Sidebar": "", "Hide Model": "", "High Contrast Mode": "", "Home": "", "Host": "", "How can I help you today?": "", "How would you rate this response?": "", "HTML": "", "Hybrid Search": "", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "", "ID": "", "iframe Sandbox Allow Forms": "", "iframe Sandbox Allow Same Origin": "", "Ignite curiosity": "", "Image": "", "Image Compression": "", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "", "Image Generation (Experimental)": "", "Image Generation Engine": "", "Image Max Compression Size": "", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "", "Image Prompt Generation Prompt": "", "Image Settings": "", "Images": "", "Import": "", "Import Chats": "", "Import Config from JSON File": "", "Import From Link": "", "Import Functions": "", "Import Models": "", "Import Notes": "", "Import Presets": "", "Import Prompt Suggestions": "", "Import Prompts": "", "Import Tools": "", "Include": "", "Include `--api-auth` flag when running stable-diffusion-webui": "", "Include `--api` flag when running stable-diffusion-webui": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "", "Info": "", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "", "Input": "", "Input commands": "", "Input Variables": "", "Insert": "", "Insert Follow-Up Prompt to Input": "", "Insert Prompt as Rich Text": "", "Install from Github URL": "", "Instant Auto-Send After Voice Transcription": "", "Integration": "", "Interface": "", "Invalid file content": "", "Invalid file format.": "", "Invalid JSON file": "", "Invalid JSON format in Additional Config": "", "Invalid Tag": "", "is typing...": "", "Italic": "", "January": "", "Jina API Key": "", "join our Discord for help.": "", "JSON": "", "JSON Preview": "", "July": "", "June": "", "Jupyter Auth": "", "Jupyter URL": "", "JWT Expiration": "", "JWT Token": "", "Kagi Search API Key": "", "Keep Follow-Up Prompts in Chat": "", "Keep in Sidebar": "", "Key": "", "Keyboard shortcuts": "", "Knowledge": "", "Knowledge Access": "", "Knowledge Base": "", "Knowledge created successfully.": "", "Knowledge deleted successfully.": "", "Knowledge Public Sharing": "", "Knowledge reset successfully.": "", "Knowledge updated successfully": "", "Kokoro.js (Browser)": "", "Kokoro.js Dtype": "", "Label": "", "Landing Page Mode": "", "Language": "", "Language Locales": "", "Last Active": "", "Last Modified": "", "Last reply": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "", "Learn More": "", "Learn more about OpenAPI tool servers.": "", "Leave empty for no compression": "", "Leave empty for unlimited": "", "Leave empty to include all models from \"{{url}}\" endpoint": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{url}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "", "Leave model field empty to use the default model.": "", "lexical": "", "License": "", "Lift List": "", "Light": "", "Listening...": "", "Llama.cpp": "", "LLMs can make mistakes. Verify important information.": "", "Loader": "", "Loading Kokoro.js...": "", "Local": "", "Local Task Model": "", "Location access not allowed": "", "Lost": "", "LTR": "", "Made by Open WebUI Community": "", "Make password visible in the user interface": "", "Make sure to enclose them with": "", "Make sure to export a workflow.json file as API format from ComfyUI.": "", "Manage": "", "Manage Direct Connections": "", "Manage Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "", "Manage Tool Servers": "", "March": "", "Markdown": "", "Markdown (Header)": "", "Max Speakers": "", "Max Upload Count": "", "Max Upload Size": "", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "", "May": "", "Memories accessible by LLMs will be shown here.": "", "Memory": "", "Memory added successfully": "", "Memory cleared successfully": "", "Memory deleted successfully": "", "Memory updated successfully": "", "Merge Responses": "", "Merged Response": "", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "", "Microsoft OneDrive": "", "Microsoft OneDrive (personal)": "", "Microsoft OneDrive (work/school)": "", "Mistral OCR": "", "Mistral OCR API Key required.": "", "Model": "", "Model '{{modelName}}' has been successfully downloaded.": "", "Model '{{modelTag}}' is already in queue for downloading.": "", "Model {{modelId}} not found": "", "Model {{modelName}} is not vision capable": "", "Model {{name}} is now {{status}}": "", "Model {{name}} is now hidden": "", "Model {{name}} is now visible": "", "Model accepts file inputs": "", "Model accepts image inputs": "", "Model can execute code and perform calculations": "", "Model can generate images based on text prompts": "", "Model can search the web for information": "", "Model created successfully!": "", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "", "Model Filtering": "", "Model ID": "", "Model ID is required.": "", "Model IDs": "", "Model Name": "", "Model Name is required.": "", "Model not selected": "", "Model Params": "", "Model Permissions": "", "Model unloaded successfully": "", "Model updated successfully": "", "Model(s) do not support file upload": "", "Modelfile Content": "", "Models": "", "Models Access": "", "Models configuration saved successfully": "", "Models Public Sharing": "", "Mojeek Search API Key": "", "more": "", "More": "", "More Concise": "", "More Options": "", "Name": "", "Name your knowledge base": "", "Native": "", "New Button": "", "New Chat": "", "New Folder": "", "New Function": "", "New Note": "", "New Password": "", "New Tool": "", "new-channel": "", "Next message": "", "No chats found": "", "No chats found for this user.": "", "No chats found.": "", "No content": "", "No content found": "", "No content found in file.": "", "No content to speak": "", "No distance available": "", "No feedbacks found": "", "No file selected": "", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No inference engine with management support found": "", "No knowledge found": "", "No memories to clear": "", "No model IDs": "", "No models found": "", "No models selected": "", "No Notes": "", "No results found": "", "No search query generated": "", "No source available": "", "No users were found.": "", "No valves to update": "", "None": "", "Not factually correct": "", "Not helpful": "", "Note deleted successfully": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "", "Notes": "", "Notification Sound": "", "Notification Webhook": "", "Notifications": "", "November": "", "OAuth ID": "", "October": "", "Off": "", "Okay, Let's Go!": "", "OLED Dark": "", "Ollama": "", "Ollama API": "", "Ollama API settings updated": "", "Ollama Version": "", "On": "", "OneDrive": "", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only markdown files are allowed": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "", "Open file": "", "Open in full screen": "", "Open modal to configure connection": "", "Open Modal To Manage Floating Quick Actions": "", "Open new chat": "", "Open Sidebar": "", "Open User Profile Menu": "", "Open WebUI can use tools provided by any OpenAPI server.": "", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "", "OpenAI": "", "OpenAI API": "", "OpenAI API Config": "", "OpenAI API Key is required.": "", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "", "openapi.json URL or Path": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "", "Ordered List": "", "Organize your users": "Organise your users", "Other": "", "OUTPUT": "", "Output format": "", "Output Format": "", "Overview": "", "page": "", "Paginate": "", "Parameters": "", "Password": "", "Passwords do not match.": "", "Paste Large Text as File": "", "PDF document (.pdf)": "", "PDF Extract Images (OCR)": "", "pending": "", "Pending": "", "Pending User Overlay Content": "", "Pending User Overlay Title": "", "Permission denied when accessing media devices": "", "Permission denied when accessing microphone": "", "Permission denied when accessing microphone: {{error}}": "", "Permissions": "", "Perplexity API Key": "", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "Personalisation", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "", "Pinned": "", "Pioneer insights": "", "Pipe": "", "Pipeline deleted successfully": "", "Pipeline downloaded successfully": "", "Pipelines": "", "Pipelines Not Detected": "", "Pipelines Valves": "", "Plain text (.md)": "", "Plain text (.txt)": "", "Playground": "", "Playwright Timeout (ms)": "", "Playwright WebSocket URL": "", "Please carefully review the following warnings:": "", "Please do not close the settings page while loading the model.": "", "Please enter a prompt": "", "Please enter a valid path": "", "Please enter a valid URL": "", "Please fill in all fields.": "", "Please select a model first.": "", "Please select a model.": "", "Please select a reason": "", "Please wait until all files are uploaded.": "", "Port": "", "Positive attitude": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Prevent file creation": "", "Preview": "", "Previous 30 days": "", "Previous 7 days": "", "Previous message": "", "Private": "", "Profile Image": "", "Prompt": "", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "", "Prompt Autocompletion": "", "Prompt Content": "", "Prompt created successfully": "", "Prompt suggestions": "", "Prompt updated successfully": "", "Prompts": "", "Prompts Access": "", "Prompts Public Sharing": "", "Public": "", "Pull \"{{searchValue}}\" from Ollama.com": "", "Pull a model from Ollama.com": "", "Query Generation Prompt": "", "Quick Actions": "", "RAG Template": "", "Rating": "", "Re-rank models by topic similarity": "", "Read": "", "Read Aloud": "", "Reason": "", "Reasoning Effort": "", "Record": "", "Record voice": "", "Redirecting you to Open WebUI Community": "", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "", "References from": "", "Refused when it shouldn't have": "", "Regenerate": "", "Reindex": "", "Reindex Knowledge Base Vectors": "", "Release Notes": "", "Releases": "", "Relevance": "", "Relevance Threshold": "", "Remember Dismissal": "", "Remove": "", "Remove {{MODELID}} from list.": "", "Remove file": "", "Remove File": "", "Remove image": "", "Remove Model": "", "Remove this tag from list": "", "Rename": "", "Reorder Models": "", "Reply in Thread": "", "Reranking Engine": "", "Reranking Model": "", "Reset": "", "Reset All Models": "", "Reset Upload Directory": "", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "", "Response splitting": "", "Response Watermark": "", "Result": "", "Retrieval": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "", "Rosé Pine": "", "Rosé Pine Dawn": "", "RTL": "", "Run": "", "Running": "", "Save": "", "Save & Create": "", "Save & Update": "", "Save As Copy": "", "Save Tag": "", "Saved": "", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "", "Scroll On Branch Change": "", "Search": "", "Search a model": "", "Search Base": "", "Search Chats": "", "Search Collection": "", "Search Filters": "", "search for archived chats": "", "search for folders": "", "search for pinned chats": "", "search for shared chats": "", "search for tags": "", "Search Functions": "", "Search In Models": "", "Search Knowledge": "", "Search Models": "", "Search Notes": "", "Search options": "", "Search Prompts": "", "Search Result Count": "", "Search the internet": "", "Search Tools": "", "SearchApi API Key": "", "SearchApi Engine": "", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "", "Searching Knowledge for \"{{searchQuery}}\"": "", "Searching the web...": "", "Searxng Query URL": "", "See readme.md for instructions": "", "See what's new": "", "Seed": "", "Select a base model": "", "Select a conversation to preview": "", "Select a engine": "", "Select a function": "", "Select a group": "", "Select a model": "", "Select a pipeline": "", "Select a pipeline url": "", "Select a tool": "", "Select an auth method": "", "Select an Ollama instance": "", "Select Engine": "", "Select Knowledge": "", "Select only one model to call": "", "Selected model(s) do not support image inputs": "", "semantic": "", "Semantic distance to query": "", "Send": "", "Send a Message": "", "Send message": "", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "", "SerpApi API Key": "", "SerpApi Engine": "", "Serper API Key": "", "Serply API Key": "", "Serpstack API Key": "", "Server connection verified": "", "Set as default": "", "Set CFG Scale": "", "Set Default Model": "", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "", "Set Image Size": "", "Set reranking model (e.g. {{model}})": "", "Set Sampler": "", "Set Scheduler": "", "Set Steps": "", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimised for GPU acceleration but may also consume more power and GPU resources.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "", "Set whisper model": "", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalise repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "Sets a scaling bias against tokens to penalise repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalise repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.", "Sets how far back for the model to look back to prevent repetition.": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "", "Sets the size of the context window used to generate the next token.": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "", "Settings saved successfully!": "", "Share": "", "Share Chat": "", "Share to Open WebUI Community": "", "Sharing Permissions": "", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "", "Show All": "", "Show Formatting Toolbar": "", "Show image preview": "", "Show Less": "", "Show Model": "", "Show shortcuts": "", "Show your support!": "", "Showcased creativity": "", "Sign in": "", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "", "Sign up": "", "Sign up to {{WEBUI_NAME}}": "", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to False.": "", "Signing in to {{WEBUI_NAME}}": "", "Sink List": "", "sk-1234": "", "Skip Cache": "", "Skip the cache and re-run the inference. Defaults to False.": "", "Sougou Search API sID": "", "Sougou Search API SK": "", "Source": "", "Speech Playback Speed": "", "Speech recognition error: {{error}}": "", "Speech-to-Text": "", "Speech-to-Text Engine": "", "Stop": "", "Stop Generating": "", "Stop Sequence": "", "Stream Chat Response": "", "Stream Delta Chunk Size": "", "Strikethrough": "", "Strip Existing OCR": "", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "", "STT Model": "", "STT Settings": "", "Stylized PDF Export": "Stylised PDF Export", "Subtitle (e.g. about the Roman Empire)": "", "Success": "", "Successfully updated.": "", "Suggest a change": "", "Suggested": "", "Support": "", "Support this plugin:": "", "Supported MIME Types": "", "Sync directory": "", "System": "", "System Instructions": "", "System Prompt": "", "Tags": "", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "", "Talk to model": "", "Tap to interrupt": "", "Task List": "", "Task Model": "", "Tasks": "", "Tavily API Key": "", "Tavily Extract Depth": "", "Tell us more:": "", "Temperature": "", "Temporary Chat": "", "Text Splitter": "", "Text-to-Speech": "", "Text-to-Speech Engine": "", "Thanks for your feedback!": "", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "", "The stream delta chunk size for the model. Increasing the chunk size will make the model respond with larger pieces of text at once.": "", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "", "The Weight of BM25 Hybrid Search. 0 more lexical, 1 more semantic. Default 0.5": "", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "", "Thinking...": "", "This action cannot be undone. Do you wish to continue?": "", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "", "This chat won't appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "", "This feature is experimental and may be modified or discontinued without notice.": "", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "", "This model is not publicly available. Please select another model.": "", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "", "Thorough explanation": "", "Thought for {{DURATION}}": "", "Thought for {{DURATION}} seconds": "", "Thought for less than a second": "", "Tika": "", "Tika Server URL required.": "", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "", "Title": "", "Title (e.g. Tell me a fun fact)": "", "Title Auto-Generation": "", "Title cannot be an empty string.": "", "Title Generation": "", "Title Generation Prompt": "", "TLS": "", "To access the available model names for downloading,": "", "To access the GGUF models available for downloading,": "", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To learn more about powerful prompt variables, click here": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "", "To select filters here, add them to the \"Functions\" workspace first.": "", "To select toolkits here, add them to the \"Tools\" workspace first.": "", "Toast notifications for new updates": "", "Today": "", "Toggle search": "", "Toggle settings": "", "Toggle sidebar": "", "Toggle whether current connection is active.": "", "Token": "", "Too verbose": "", "Tool created successfully": "", "Tool deleted successfully": "", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "", "Tool Name": "", "Tool Servers": "", "Tool updated successfully": "", "Tools": "", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "", "Tools Function Calling Prompt": "", "Tools have a function calling system that allows arbitrary code execution.": "", "Tools Public Sharing": "", "Top K": "", "Top K Reranker": "", "Transformers": "", "Trouble accessing Ollama?": "", "Trust Proxy Environment": "", "Try Again": "", "TTS Model": "", "TTS Settings": "", "TTS Voice": "", "Type": "", "Type Hugging Face Resolve (Download) URL": "", "Uh-oh! There was an issue with the response.": "", "UI": "", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Underline": "", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "", "Unpin": "", "Unravel secrets": "", "Unsupported file type.": "", "Untagged": "", "Untitled": "", "Update": "", "Update and Copy Link": "", "Update for the latest features and improvements.": "", "Update password": "", "Updated": "", "Updated at": "", "Updated At": "", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "", "Upload": "", "Upload a GGUF model": "", "Upload Audio": "", "Upload directory": "", "Upload files": "", "Upload Files": "", "Upload Pipeline": "", "Upload Progress": "", "URL": "", "URL Mode": "", "Usage": "", "Use '#' in the prompt input to load and include your knowledge.": "", "Use Gravatar": "", "Use groups to group your users and assign permissions.": "", "Use Initials": "", "Use LLM": "", "Use no proxy to fetch page contents.": "", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "", "user": "", "User": "", "User Groups": "", "User location successfully retrieved.": "", "User menu": "", "User Webhooks": "", "Username": "", "Users": "", "Using Entire Document": "", "Using Focused Retrieval": "", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Valid time units:": "", "Valves": "", "Valves updated": "", "Valves updated successfully": "", "variable": "", "Verify Connection": "", "Verify SSL Certificate": "", "Version": "", "Version {{selectedVersion}} of {{totalVersions}}": "", "View Replies": "", "View Result from **{{NAME}}**": "", "Visibility": "", "Vision": "", "Voice": "", "Voice Input": "", "Voice mode": "", "Warning": "", "Warning:": "", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "", "Web": "", "Web API": "", "Web Loader Engine": "", "Web Search": "", "Web Search Engine": "", "Web Search in Chat": "", "Web Search Query Generation": "", "Webhook URL": "", "WebUI Settings": "", "WebUI URL": "", "WebUI will make requests to \"{{url}}\"": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "What are you trying to achieve?": "", "What are you working on?": "", "What's New in": "", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "", "Whisper (Local)": "", "Why?": "", "Widescreen Mode": "", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "", "Workspace": "", "Workspace Permissions": "", "Write": "", "Write a prompt suggestion (e.g. Who are you?)": "", "Write a summary in 50 words that summarizes [topic or keyword].": "Write a summary in 50 words that summarises [topic or keyword].", "Write something...": "", "Yacy Instance URL": "", "Yacy Password": "", "Yacy Username": "", "Yesterday": "", "You": "", "You are currently using a trial license. Please contact support to upgrade your license.": "", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "You can personalise your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.", "You cannot upload an empty file.": "", "You do not have permission to upload files.": "", "You have no archived conversations.": "", "You have shared this chat": "", "You're a helpful assistant.": "", "You're now logged in.": "", "Your account status is currently pending activation.": "", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "", "Youtube": "", "Youtube Language": "", "Youtube Proxy URL": ""}