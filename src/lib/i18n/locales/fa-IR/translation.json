{"-1 for no limit, or a positive integer for a specific limit": "-1 برای بدون محدودیت، یا یک عدد مثبت برای محدودیت مشخص", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' یا '-1' برای غیر فعال کردن انقضا.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(مثال: `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(e.g. `sh webui.sh --api`)", "(latest)": "(آخ<PERSON><PERSON>ن)", "(leave blank for to use commercial endpoint)": "", "[Last] dddd [at] h:mm A": "", "[Today at] h:mm A": "", "[Yesterday at] h:mm A": "", "{{ models }}": "{{ models }}", "{{COUNT}} Available Tools": "{{COUNT}} ابزار موجود", "{{COUNT}} characters": "", "{{COUNT}} hidden lines": "{{COUNT}} خط پنهان", "{{COUNT}} Replies": "{{COUNT}} پا<PERSON>خ", "{{COUNT}} words": "", "{{user}}'s Chats": "{{user}} گفتگوهای", "{{webUIName}} Backend Required": "بکند {{webUIName}} نیاز است.", "*Prompt node ID(s) are required for image generation": "*شناسه(های) گره پرامپت برای تولید تصویر مورد نیاز است", "A new version (v{{LATEST_VERSION}}) is now available.": "نسخه جدید (v{{LATEST_VERSION}}) در دسترس است.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "یک مدل وظیفه هنگام انجام وظایف مانند تولید عناوین برای چت ها و نمایش های جستجوی وب استفاده می شود.", "a user": "ی<PERSON> کاربر", "About": "درباره", "Accept autocomplete generation / Jump to prompt variable": "پذیرش تکمیل خودکار / پرش به متغیر پرامپت", "Access": "دسترسی", "Access Control": "کنترل دسترسی", "Accessible to all users": "قابل دسترسی برای همه کاربران", "Account": "حساب کاربری", "Account Activation Pending": "فعال‌سازی حساب در حال انتظار", "Accurate information": "اطلاعات دقیق", "Action": "", "Actions": "کنش‌ها", "Activate": "فعال کردن", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "این دستور را با تایپ \"/{{COMMAND}}\" در ورودی چت فعال کنید.", "Active Users": "کاربران فعال", "Add": "اضافه کردن", "Add a model ID": "افزودن شناسه مدل", "Add a short description about what this model does": "افزودن توضیحات کوتاه در مورد انچه که این مدل انجام می دهد", "Add a tag": "افزودن یک برچسب", "Add Arena Model": "افزو<PERSON>ن مدل Arena", "Add Connection": "افزودن اتصال", "Add Content": "افزودن محتوا", "Add content here": "محتوا را اینجا اضافه کنید", "Add Custom Parameter": "", "Add custom prompt": "افزودن یک درخواست سفارشی", "Add Details": "", "Add Files": "افزودن فایل‌ها", "Add Group": "افزودن گروه", "Add Memory": "افزودن حافظه", "Add Model": "افزو<PERSON>ن مدل", "Add Reaction": "افزودن واکنش", "Add Tag": "افزودن برچسب", "Add Tags": "افزودن برچسب‌ها", "Add text content": "افزودن محتوای متنی", "Add User": "افزودن کاربر", "Add User Group": "افزودن گروه کاربری", "Additional Config": "", "Additional configuration options for marker. This should be a JSON string with key-value pairs. For example, '{\"key\": \"value\"}'. Supported keys include: disable_links, keep_pageheader_in_output, keep_pagefooter_in_output, filter_blank_pages, drop_repeated_text, layout_coverage_threshold, merge_threshold, height_tolerance, gap_threshold, image_threshold, min_line_length, level_count, default_level": "", "Adjusting these settings will apply changes universally to all users.": "با تنظیم این تنظیمات، تغییرات به طور کلی برای همه کاربران اعمال می‌شود.", "admin": "مدیر", "Admin": "مدیر", "Admin Panel": "پنل مدیریت", "Admin Settings": "تنظیمات مدیریت", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "مدیران همیشه به تمام ابزارها دسترسی دارند؛ کاربران نیاز به ابزارهای اختصاص داده شده برای هر مدل در فضای کاری دارند.", "Advanced Parameters": "پارامترهای پیشرفته", "Advanced Params": "پارام‌های پیشرفته", "AI": "", "All": "همه", "All Documents": "همهٔ سند‌ها", "All models deleted successfully": "همه مدل‌ها با موفقیت حذف شدند", "Allow Call": "اجازه تماس", "Allow Chat Controls": "اجازه کنترل‌های گفتگو", "Allow Chat Delete": "اجازه حذف گفتگو", "Allow Chat Deletion": "اجازهٔ حذف گفتگو", "Allow Chat Edit": "اجازه ویرایش گفتگو", "Allow Chat Export": "", "Allow Chat Params": "", "Allow Chat Share": "", "Allow Chat System Prompt": "", "Allow Chat Valves": "", "Allow File Upload": "اجازه بارگذاری فایل", "Allow Multiple Models in Chat": "اجازه استفاده از چند مدل در گفتگو", "Allow non-local voices": "اجازه صداهای غیر محلی", "Allow Speech to Text": "اجازه تبدیل گفتار به متن", "Allow Temporary Chat": "اجازهٔ گفتگوی موقتی", "Allow Text to Speech": "اجازه تبدیل متن به گفتار", "Allow User Location": "اجازهٔ موقعیت مکانی کاربر", "Allow Voice Interruption in Call": "اجازه قطع صدا در تماس", "Allowed Endpoints": "نقاط پایانی مجاز", "Allowed File Extensions": "", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "", "Already have an account?": "از قبل حساب کاربری دارید؟", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "جایگزینی برای top_p و هدف آن اطمینان از تعادل کیفیت و تنوع است. پارامتر p نشان‌دهنده حداقل احتمال برای در نظر گرفتن یک توکن نسبت به احتمال محتمل‌ترین توکن است. به عنوان مثال، با p=0.05 و محتمل‌ترین توکن با احتمال 0.9، لاگیت‌های با مقدار کمتر از 0.045 فیلتر می‌شوند.", "Always": "همیشه", "Always Collapse Code Blocks": "همیشه بلوک‌های کد را جمع کن", "Always Expand Details": "همیشه جزئیات را گسترش بده", "Always Play Notification Sound": "", "Amazing": "شگفت‌انگیز", "an assistant": "یک دستیار", "Analytics": "", "Analyzed": "تحلیل شده", "Analyzing...": "در حال تحلیل...", "and": "و", "and {{COUNT}} more": "و {{COUNT}} مورد دیگر", "and create a new shared link.": "و یک پیوند اشتراک‌گذاری جدید ایجاد کنید.", "Android": "اندروید", "API": "", "API Base URL": "نشانی پایهٔ API", "API Base URL for Datalab Marker service. Defaults to: https://www.datalab.to/api/v1/marker": "", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "کلید API", "API Key created.": "کلید API ساخته شد.", "API Key Endpoint Restrictions": "محدودیت‌های نقطه پایانی کلید API", "API keys": "کلیدهای API", "API Version": "", "Application DN": "DN برنامه", "Application DN Password": "رمز عبور DN برنامه", "applies to all users with the \"user\" role": "برای همه کاربران با نقش \"کاربر\" اعمال می‌شود", "April": "آوریل", "Archive": "بایگانی", "Archive All Chats": "بایگانی همه گفتگوها", "Archived Chats": "گفتگوهای بایگانی‌شده", "archived-chat-export": "خروجی-گفتگوی-بایگانی-شده", "Are you sure you want to clear all memories? This action cannot be undone.": "آیا مطمئن هستید که می‌خواهید تمام حافظه‌ها را پاک کنید؟ این عمل قابل بازگشت نیست.", "Are you sure you want to delete this channel?": "آیا مطمئن هستید که می‌خواهید این کانال را حذف کنید؟", "Are you sure you want to delete this message?": "آیا مطمئن هستید که می‌خواهید این پیام را حذف کنید؟", "Are you sure you want to unarchive all archived chats?": "آیا مطمئن هستید که می‌خواهید همه گفتگوهای بایگانی شده را از بایگانی خارج کنید؟", "Are you sure?": "مطمئنید؟", "Arena Models": "مدل‌های آرنا", "Artifacts": "مصنوعات", "Ask": "بپرس", "Ask a question": "سوالی بپرسید", "Assistant": "دستیار", "Attach file from knowledge": "پیوست فایل از دانش", "Attention to detail": "دق<PERSON><PERSON>", "Attribute for Mail": "ویژگی برای ایمیل", "Attribute for Username": "ویژگی برای نام کاربری", "Audio": "صدا", "August": "آگوست", "Auth": "احر<PERSON>ز هویت", "Authenticate": "احر<PERSON>ز هویت", "Authentication": "احر<PERSON>ز هویت", "Auto": "<PERSON>و<PERSON><PERSON>ار", "Auto-Copy Response to Clipboard": "ک<PERSON>ی خودکار پاسخ به کلیپ بورد", "Auto-playback response": "پخش خودکار پاسخ", "Autocomplete Generation": "تولید تکمیل خودکار", "Autocomplete Generation Input Max Length": "حداکثر طول ورودی تولید تکمیل خودکار", "Automatic1111": "اتوماتیک1111", "AUTOMATIC1111 Api Auth String": "رشته احراز هویت API اتوماتیک1111", "AUTOMATIC1111 Base URL": "پایه URL AUTOMATIC1111 ", "AUTOMATIC1111 Base URL is required.": "به URL پایه AUTOMATIC1111 مورد نیاز است.", "Available list": "فهرست دردسترس", "Available Tools": "ابزارهای موجود", "available!": "در دسترس!", "Awful": "وحشتناک", "Azure AI Speech": "سخنگوی هوش‌مصنوعی Azure", "Azure Region": "منطقهٔ Azure", "Back": "بازگشت", "Bad Response": "پاسخ خوب نیست", "Banners": "بنر", "Base Model (From)": "مدل پایه (از)", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "", "before": "قبل", "Being lazy": "حالت سازنده", "Beta": "بتا", "Bing Search V7 Endpoint": "نقطه پایانی جستجوی Bing V7", "Bing Search V7 Subscription Key": "کلید اشتراک جستجوی Bing V7", "BM25 Weight": "", "Bocha Search API Key": "کلید API جستجوی Bocha", "Bold": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "تقویت یا جریمه توکن‌های خاص برای پاسخ‌های محدود. مقادیر بایاس بین -100 و 100 (شا<PERSON><PERSON>) محدود خواهند شد. (پیش‌فرض: هیچ)", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "", "Brave Search API Key": "کلید API جستجوی شجاع", "Bullet List": "", "Button ID": "", "Button Label": "", "Button Prompt": "", "By {{name}}": "توسط {{name}}", "Bypass Embedding and Retrieval": "دور زدن جاسازی و بازیابی", "Bypass Web Loader": "", "Cache Base Model List": "", "Calendar": "تقویم", "Call": "تماس", "Call feature is not supported when using Web STT engine": "ویژگی تماس هنگام استفاده از موتور Web STT پشتیبانی نمی‌شود", "Camera": "دوربین", "Cancel": "لغو", "Capabilities": "قابلیت", "Capture": "ضبط", "Capture Audio": "", "Certificate Path": "مسیر گواهینامه", "Change Password": "تغییر رمز عبور", "Channel Name": "نام کانال", "Channels": "کانال‌ها", "Character": "شخصیت", "Character limit for autocomplete generation input": "محدودیت کاراکتر برای ورودی تولید تکمیل خودکار", "Chart new frontiers": "ترسیم مرزهای جدید", "Chat": "گفتگو", "Chat Background Image": "تصویر پس‌زمینهٔ گفتگو", "Chat Bubble UI": "رابط کاربری حبابی گفتگو", "Chat Controls": "کنترل‌های گفتگو", "Chat direction": "جهت‌گفتگو", "Chat ID": "", "Chat Overview": "نمای کلی گفتگو", "Chat Permissions": "مجوزهای گفتگو", "Chat Tags Auto-Generation": "تو<PERSON><PERSON>د خودکار برچسب‌های گفتگو", "Chats": "گفتگو‌ها", "Check Again": "بررسی دوباره", "Check for updates": "بررسی به‌روزرسانی", "Checking for updates...": "در حال بررسی برای به‌روزرسانی..", "Choose a model before saving...": "قبل از ذخیره یک مدل را انتخاب کنید...", "Chunk Overlap": "همپوشانی تکه", "Chunk Size": "اندازه تکه", "Ciphers": "رمزها", "Citation": "استناد", "Citations": "", "Clear memory": "پاک کردن حافظه", "Clear Memory": "پاک کردن حافظه", "click here": "اینجا کلیک کنید", "Click here for filter guides.": "برای راهنمای فیلترها اینجا کلیک کنید.", "Click here for help.": "برای کمک اینجا را کلیک کنید.", "Click here to": "برای کمک اینجا را کلیک کنید.", "Click here to download user import template file.": "برای دانلود فایل قالب واردات کاربر اینجا کلیک کنید.", "Click here to learn more about faster-whisper and see the available models.": "برای یادگیری بیشتر درباره faster-whisper و دیدن مدل‌های موجود اینجا کلیک کنید.", "Click here to see available models.": "برای دیدن مدل‌های موجود اینجا کلیک کنید.", "Click here to select": "برای انتخاب اینجا کلیک کنید", "Click here to select a csv file.": "برای انتخاب یک فایل csv اینجا را کلیک کنید.", "Click here to select a py file.": "برای انتخاب یک فایل py اینجا کلیک کنید.", "Click here to upload a workflow.json file.": "برای آپلود فایل workflow.json اینجا کلیک کنید.", "click here.": "اینجا کلیک کنید.", "Click on the user role button to change a user's role.": "برای تغییر نقش کاربر، روی دکمه نقش کاربر کلیک کنید.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "دسترسی نوشتن در کلیپ‌بورد رد شد. لطفاً تنظیمات مرورگر خود را برای اعطای دسترسی لازم بررسی کنید.", "Clone": "کلون", "Clone Chat": "کلون گفتگو", "Clone of {{TITLE}}": "کلون {{TITLE}}", "Close": "بسته", "Close Banner": "", "Close Configure Connection Modal": "", "Close modal": "", "Close settings modal": "", "Close Sidebar": "", "Code Block": "", "Code execution": "اجرای کد", "Code Execution": "اجرای کد", "Code Execution Engine": "موتور اجرای کد", "Code Execution Timeout": "مه<PERSON>ت اجرای کد", "Code formatted successfully": "کد با موفقیت قالب‌بندی شد", "Code Interpreter": "م<PERSON><PERSON><PERSON> کد", "Code Interpreter Engine": "موتور مفسر کد", "Code Interpreter Prompt Template": "قالب پرامپت مفسر کد", "Collapse": "جمع کردن", "Collection": "مجموعه", "Color": "رنگ", "ComfyUI": "کومیوآی", "ComfyUI API Key": "کلید API کومیوآی", "ComfyUI Base URL": "URL پایه کومیوآی", "ComfyUI Base URL is required.": "URL پایه کومیوآی الزامی است.", "ComfyUI Workflow": "گردش کار کومیوآی", "ComfyUI Workflow Nodes": "گره‌های گردش کار کومیوآی", "Command": "دستور", "Comment": "", "Completions": "تکمیل‌ها", "Compress Images in Channels": "", "Concurrent Requests": "درخواست های همزمان", "Configure": "پیکربندی", "Confirm": "تا<PERSON><PERSON>د", "Confirm Password": "تا<PERSON>ید رمز عبور", "Confirm your action": "عملیا<PERSON> خود را تایید کنید", "Confirm your new password": "رمز عبور جدید خود را تایید کنید", "Confirm Your Password": "", "Connect to your own OpenAI compatible API endpoints.": "به نقاط پایانی API سازگار با OpenAI خود متصل شوید.", "Connect to your own OpenAPI compatible external tool servers.": "به سرورهای ابزار خارجی سازگار با OpenAPI خود متصل شوید.", "Connection failed": "اتصال ناموفق بود", "Connection successful": "اتصال موفقیت‌آ<PERSON><PERSON>ز بود", "Connection Type": "", "Connections": "ارتباطات", "Connections saved successfully": "ارتباطات با موفقیت ذخیره شدند", "Connections settings updated": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "تلاش برای استدلال در مدل‌های استدلالی را محدود می‌کند. فقط برای مدل‌های استدلالی از ارائه‌دهندگان خاصی که از تلاش استدلالی پشتیبانی می‌کنند قابل اجراست.", "Contact Admin for WebUI Access": "برای دسترسی به WebUI با مدیر تماس بگیرید", "Content": "محتوا", "Content Extraction Engine": "موتور استخراج محتوا", "Continue Response": "ادامه پاسخ", "Continue with {{provider}}": "با {{provider}} ادامه دهید", "Continue with Email": "با ایمیل ادامه دهید", "Continue with LDAP": "با LDAP ادامه دهید", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "کنترل نحوه تقسیم متن پیام برای درخواست‌های TTS. 'علامت‌گذاری' به جملات تقسیم می‌کند، 'پاراگراف‌ها' به پاراگراف‌ها تقسیم می‌کند و 'هیچ‌کدام' پیام را به عنوان یک رشته واحد نگه می‌دارد.", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "کنترل تکرار توالی‌های توکن در متن تولید شده. مقدار بالاتر (مثلاً 1.5) تکرارها را شدیدتر جریمه می‌کند، در حالی که مقدار پایین‌تر (مثلاً 1.1) ملایم‌تر خواهد بود. در 1، غیرفعال است.", "Controls": "کنترل‌ها", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "تعادل بین انسجام و تنوع خروجی را کنترل می‌کند. مقدار پایین‌تر منجر به متن متمرکزتر و منسجم‌تر می‌شود.", "Copied": "کپی شد", "Copied link to clipboard": "", "Copied shared chat URL to clipboard!": "URL چت به کلیپ بورد کپی شد!", "Copied to clipboard": "به بریده‌دان کپی‌شد", "Copy": "کپی", "Copy Formatted Text": "", "Copy last code block": "کپی آخرین بلوک کد", "Copy last response": "کپی آخرین پاسخ", "Copy link": "", "Copy Link": "کپی لینک", "Copy to clipboard": "کپی به کلیپ‌بورد", "Copying to clipboard was successful!": "کپی کردن در کلیپ بورد با موفقیت انجام شد!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "CORS باید توسط ارائه‌دهنده به درستی پیکربندی شود تا درخواست‌ها از Open WebUI مجاز باشند.", "Create": "ایجاد", "Create a knowledge base": "ایجاد یک پایگاه دانش", "Create a model": "ایج<PERSON> یک مدل", "Create Account": "ساخت حساب کاربری", "Create Admin Account": "ایج<PERSON> حساب مدیر", "Create Channel": "ایج<PERSON> کانال", "Create Folder": "", "Create Group": "ایجاد گروه", "Create Knowledge": "ایجاد دانش", "Create new key": "ساخت کلید جدید", "Create new secret key": "ساخت کلید مخ<PERSON>ی جدید", "Create Note": "", "Create your first note by clicking on the plus button below.": "", "Created at": "ایجاد شده در", "Created At": "ایجاد شده در", "Created by": "ایجاد شده توسط", "CSV Import": "درون‌ریزی CSV", "Ctrl+Enter to Send": "Ctrl+Enter برای ارسال", "Current Model": "مدل فعلی", "Current Password": "ر<PERSON>ز عبور فعلی", "Custom": "دلخواه", "Custom description enabled": "", "Custom Parameter Name": "", "Custom Parameter Value": "", "Danger Zone": "من<PERSON><PERSON><PERSON> خطر", "Dark": "تیره", "Database": "پایگاه داده", "Datalab Marker API": "", "Datalab Marker API Key required.": "", "DD/MM/YYYY": "", "December": "دسامبر", "Default": "پیش<PERSON><PERSON>ض", "Default (Open AI)": "پی<PERSON><PERSON><PERSON><PERSON> (Open AI)", "Default (SentenceTransformers)": "پیشفرض (SentenceTransformers)", "Default action buttons will be used.": "", "Default description enabled": "", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "", "Default Model": "مدل پیشفرض", "Default model updated": "مدل پیشفرض به‌روزرسانی شد", "Default Models": "مدل‌های پیش‌فرض", "Default permissions": "مجوزهای پیش‌فرض", "Default permissions updated successfully": "مجوزهای پیش‌فرض با موفقیت به‌روز شدند", "Default Prompt Suggestions": "پیشنهادات پرامپت پیش فرض", "Default to 389 or 636 if TLS is enabled": "پیش‌فرض به 389 یا 636 اگر TLS فعال باشد", "Default to ALL": "پیش‌فرض به همه", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "پیش‌فرض به بازیابی قطعه‌ای برای استخراج محتوای متمرکز و مرتبط، این برای اکثر موارد توصیه می‌شود.", "Default User Role": "نقش کاربر پیش فرض", "Delete": "<PERSON><PERSON><PERSON>", "Delete a model": "حذ<PERSON> <PERSON><PERSON> مدل", "Delete All Chats": "حذف همه گفتگوها", "Delete All Models": "حذ<PERSON> همه مدل‌ها", "Delete chat": "حذ<PERSON> گپ", "Delete Chat": "حذ<PERSON> گپ", "Delete chat?": "گفتگو حذف شود؟", "Delete folder?": "پوشه حذف شود؟", "Delete function?": "تابع حذف شود؟", "Delete Message": "حذ<PERSON> پیام", "Delete message?": "پیام حذف شود؟", "Delete note?": "", "Delete prompt?": "پرامپت حذف شود؟", "delete this link": "حذ<PERSON> این لینک", "Delete tool?": "ابزار حذف شود؟", "Delete User": "<PERSON><PERSON><PERSON> کاربر", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} پاک شد", "Deleted {{name}}": "<PERSON><PERSON><PERSON> ش<PERSON> {{name}}", "Deleted User": "کاربر حذف شده", "Deployment names are required for Azure OpenAI": "", "Describe Pictures in Documents": "", "Describe your knowledge base and objectives": "پایگاه دانش و اهداف خود را توصیف کنید", "Description": "توضیحات", "Detect Artifacts Automatically": "تشخیص خودکار مصنوعات", "Dictate": "", "Didn't fully follow instructions": "نمی تواند دستورالعمل را کامل پیگیری کند", "Direct": "مستقیم", "Direct Connections": "اتصالات مستقیم", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "اتصالات مستقیم به کاربران اجازه می‌دهد به نقاط پایانی API سازگار با OpenAI خود متصل شوند.", "Direct Tool Servers": "سرورهای ابزار مستقیم", "Disable Code Interpreter": "", "Disable Image Extraction": "", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "", "Disabled": "غیرفعال", "Discover a function": "کشف یک تابع", "Discover a model": "کش<PERSON> یک مدل", "Discover a prompt": "یک اعلان را کشف کنید", "Discover a tool": "کشف یک ابزار", "Discover how to use Open WebUI and seek support from the community.": "نحوه استفاده از Open WebUI را کشف کنید و از انجمن پشتیبانی بگیرید.", "Discover wonders": "کشف شگفتی‌ها", "Discover, download, and explore custom functions": "کشف، دانلود و کاوش توابع سفارشی", "Discover, download, and explore custom prompts": "پرامپت‌های سفارشی را کشف، دانلود و کاوش کنید", "Discover, download, and explore custom tools": "کشف، دانلود و کاوش ابزارهای سفارشی", "Discover, download, and explore model presets": "پیش تنظیمات مدل را کشف، دانلود و کاوش کنید", "Display": "نمایش", "Display Emoji in Call": "نمایش اموجی در تماس", "Display Multi-model Responses in Tabs": "", "Display the username instead of You in the Chat": "نمایش نام کاربری به جای «شما» در چت", "Displays citations in the response": "نمایش استنادها در پاسخ", "Dive into knowledge": "غوطه‌ور شدن در دانش", "Do not install functions from sources you do not fully trust.": "توابع را از منابعی که کاملاً به آنها اعتماد ندارید نصب نکنید.", "Do not install tools from sources you do not fully trust.": "ابزارها را از منابعی که کاملاً به آنها اعتماد ندارید نصب نکنید.", "Docling": "داکلینگ", "Docling Server URL required.": "آدرس سرور داکلینگ مورد نیاز است.", "Document": "سند", "Document Intelligence": "هوش اسناد", "Document Intelligence endpoint and key required.": "نقطه پایانی و کلید هوش اسناد مورد نیاز است.", "Documentation": "مستندات", "Documents": "اسناد", "does not make any external connections, and your data stays securely on your locally hosted server.": "هیچ اتصال خارجی ایجاد نمی کند و داده های شما به طور ایمن در سرور میزبان محلی شما باقی می ماند.", "Domain Filter List": "لیست فیلتر دامنه", "Don't have an account?": "حساب کاربری ندارید؟", "don't install random functions from sources you don't trust.": "توابع تصادفی را از منابعی که به آنها اعتماد ندارید نصب نکنید.", "don't install random tools from sources you don't trust.": "ابزارهای تصادفی را از منابعی که به آنها اعتماد ندارید نصب نکنید.", "Don't like the style": "نظری ندارید؟", "Done": "انجام شد", "Download": "د<PERSON><PERSON><PERSON><PERSON> کن", "Download as SVG": "دانلود به صورت SVG", "Download canceled": "دانلود لغو شد", "Download Database": "دانلود پایگاه داده", "Drag and drop a file to upload or select a file to view": "یک فایل را برای آپلود بکشید و رها کنید یا برای مشاهده یک فایل را انتخاب کنید", "Draw": "رسم کردن", "Drop any files here to upload": "", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "به طور مثال '30s','10m'. واحد‌های زمانی معتبر 's', 'm', 'h' هستند.", "e.g. \"json\" or a JSON schema": "م<PERSON><PERSON><PERSON> \"json\" یا یک طرح JSON", "e.g. 60": "م<PERSON><PERSON><PERSON> 60", "e.g. A filter to remove profanity from text": "مثلا فیلتری برای حذف ناسزا از متن", "e.g. en": "", "e.g. My Filter": "مثلا فیلتر من", "e.g. My Tools": "مثلا ابزارهای من", "e.g. my_filter": "مثلا my_filter", "e.g. my_tools": "مثلا my_tools", "e.g. pdf, docx, txt": "", "e.g. Tools for performing various operations": "مثلا ابزارهایی برای انجام عملیات مختلف", "e.g., 3, 4, 5 (leave blank for default)": "", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "مثلا en-US,ja-JP (برای تشخیص خودکار خالی بگذارید)", "e.g., westus (leave blank for eastus)": "", "Edit": "ویرایش", "Edit Arena Model": "ویرایش مدل آرنا", "Edit Channel": "ویرایش کانال", "Edit Connection": "ویرایش اتصال", "Edit Default Permissions": "ویرایش مجوزهای پیش‌فرض", "Edit Folder": "", "Edit Memory": "ویرایش حافظه", "Edit User": "ویرایش کاربر", "Edit User Group": "ویرایش گروه کاربری", "Edited": "", "Editing": "", "Eject": "", "ElevenLabs": "الون‌لبز", "Email": "ایمیل", "Embark on adventures": "شروع ماجراجویی‌ها", "Embedding": "پیدائش", "Embedding Batch Size": "اندازه دسته پیدائش", "Embedding Model": "مدل پیدائش", "Embedding Model Engine": "محرک مدل پیدائش", "Embedding model set to \"{{embedding_model}}\"": "مدل پیدائش را به \"{{embedding_model}}\" تنظیم کنید", "Enable API Key": "فعال‌سازی کلید API", "Enable autocomplete generation for chat messages": "فعال‌سازی تولید تکمیل خودکار برای پیام‌های چت", "Enable Code Execution": "فعال‌سازی اجرای کد", "Enable Code Interpreter": "فعال‌سازی مفسر کد", "Enable Community Sharing": "فعالسازی اشتراک انجمن", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "فعال‌سازی قفل حافظه (mlock) برای جلوگیری از تعویض داده‌های مدل از RAM. این گزینه مجموعه صفحات کاری مدل را در RAM قفل می‌کند و اطمینان می‌دهد که به دیسک منتقل نمی‌شوند. این می‌تواند با جلوگیری از خطاهای صفحه و تضمین دسترسی سریع به داده‌ها، عملکرد را حفظ کند.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "فعال‌سازی نگاشت حافظه (mmap) برای بارگیری داده‌های مدل. این گزینه به سیستم اجازه می‌دهد از فضای دیسک به عنوان گسترش RAM استفاده کند با در نظر گرفتن فایل‌های دیسک مانند اینکه در RAM هستند. این می‌تواند با اجازه دادن به دسترسی سریع‌تر به داده‌ها، عملکرد مدل را بهبود بخشد. با این حال، ممکن است با همه سیستم‌ها به درستی کار نکند و می‌تواند مقدار قابل توجهی از فضای دیسک را مصرف کند.", "Enable Message Rating": "فعال‌سازی امتیازدهی پیام", "Enable Mirostat sampling for controlling perplexity.": "فعال‌سازی نمونه‌برداری میروستات برای کنترل سردرگمی", "Enable New Sign Ups": "فعال کردن ثبت نام‌های جدید", "Enabled": "فعال شده", "Endpoint URL": "", "Enforce Temporary Chat": "اجبار چت موقت", "Enhance": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "اطمینان حاصل کنید که فایل CSV شما شامل چهار ستون در این ترتیب است: نام، ایمیل، رمز عبور، نقش.", "Enter {{role}} message here": "پیام {{role}} را اینجا وارد کنید", "Enter a detail about yourself for your LLMs to recall": "برای ذخیره سازی اطلاعات خود، یک توضیح کوتاه درباره خود را وارد کنید", "Enter a title for the pending user info overlay. Leave empty for default.": "", "Enter a watermark for the response. Leave empty for none.": "", "Enter api auth string (e.g. username:password)": "رشته احراز هویت api را وارد کنید (مثلا username:password)", "Enter Application DN": "DN برنامه را وارد کنید", "Enter Application DN Password": "رمز عبور DN برنامه را وارد کنید", "Enter Bing Search V7 Endpoint": "نقطه پایانی جستجوی Bing V7 را وارد کنید", "Enter Bing Search V7 Subscription Key": "کلید اشتراک جستجوی Bing V7 را وارد کنید", "Enter Bocha Search API Key": "کلید API جستجوی Bocha را وارد کنید", "Enter Brave Search API Key": "کلید API جستجوی شجاع را وارد کنید", "Enter certificate path": "مسیر گواهینامه را وارد کنید", "Enter CFG Scale (e.g. 7.0)": "مقیاس CFG را وارد کنید (مثال: 7.0)", "Enter Chunk Overlap": "مق<PERSON><PERSON><PERSON>k <PERSON>lap را وارد کنید", "Enter Chunk Size": "مق<PERSON><PERSON><PERSON> Chun<PERSON> را وارد کنید", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "جفت‌های \"توکن:مقدار_بایاس\" را با کاما جدا شده وارد کنید (مثال: 5432:100, 413:-100)", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "", "Enter Datalab Marker API Base URL": "", "Enter Datalab Marker API Key": "", "Enter description": "توضیحات را وارد کنید", "Enter Docling OCR Engine": "", "Enter Docling OCR Language(s)": "", "Enter Docling Server URL": "آدرس سرور Docling را وارد کنید", "Enter Document Intelligence Endpoint": "نقطه پایانی هوش سند را وارد کنید", "Enter Document Intelligence Key": "کلید هوش سند را وارد کنید", "Enter domains separated by commas (e.g., example.com,site.org)": "دامنه‌ها را با کاما جدا کنید (مثال: example.com,site.org)", "Enter Exa API Key": "کلید API اکسا را وارد کنید", "Enter External Document Loader API Key": "", "Enter External Document Loader URL": "", "Enter External Web Loader API Key": "", "Enter External Web Loader URL": "", "Enter External Web Search API Key": "", "Enter External Web Search URL": "", "Enter Firecrawl API Base URL": "آدرس پایه API فایرکراول را وارد کنید", "Enter Firecrawl API Key": "کلید API فایرکراول را وارد کنید", "Enter folder name": "", "Enter Github Raw URL": "آ<PERSON><PERSON><PERSON> Github Raw را وارد کنید", "Enter Google PSE API Key": "کلید API گوگل PSE را وارد کنید", "Enter Google PSE Engine Id": "شناسه موتور PSE گوگل را وارد کنید", "Enter Image Size (e.g. 512x512)": "اندازه تصویر را وارد کنید (مثال: 512x512)", "Enter Jina API Key": "کلید API جینا را وارد کنید", "Enter JSON config (e.g., {\"disable_links\": true})": "", "Enter Jupyter Password": "رمز عبور ژوپیتر را وارد کنید", "Enter Jupyter Token": "توکن ژوپیتر را وارد کنید", "Enter Jupyter URL": "آدرس ژوپیتر را وارد کنید", "Enter Kagi Search API Key": "کلید API جستجوی کاگی را وارد کنید", "Enter Key Behavior": "رف<PERSON><PERSON><PERSON> کلید را وارد کنید", "Enter language codes": "کد زبان را وارد کنید", "Enter Mistral API Key": "کلید API میسترال را وارد کنید", "Enter Model ID": "شناسه مدل را وارد کنید", "Enter model tag (e.g. {{modelTag}})": "تگ مدل را وارد کنید (مثلا {{modelTag}})", "Enter Mojeek Search API Key": "کلید API جستجوی موجیک را وارد کنید", "Enter name": "", "Enter New Password": "", "Enter Number of Steps (e.g. 50)": "تعداد گام‌ها را وارد کنید (مثال: 50)", "Enter Perplexity API Key": "کلید API پرپلکسیتی را وارد کنید", "Enter Playwright Timeout": "مهلت پلی‌رایت را وارد کنید", "Enter Playwright WebSocket URL": "آدرس وب‌سوکت پلی‌رایت را وارد کنید", "Enter proxy URL (e.g. **************************:port)": "آدرس پراکسی را وارد کنید (مثال: **************************:port)", "Enter reasoning effort": "تلاش استدلال را وارد کنید", "Enter Sampler (e.g. Euler a)": "نمونه‌گیر را وارد کنید (مثال: Euler a)", "Enter Scheduler (e.g. Karras)": "زمان‌بند را وارد کنید (مثال: Karras)", "Enter Score": "امتیاز را وارد کنید", "Enter SearchApi API Key": "کلید API جستجو را وارد کنید", "Enter SearchApi Engine": "موتور جستجو را وارد کنید", "Enter Searxng Query URL": "نشانی وب پرسوجوی Searxng را وارد کنید", "Enter Seed": "مقدار بذر را وارد کنید", "Enter SerpApi API Key": "کلید API سرپ را وارد کنید", "Enter SerpApi Engine": "موتور سرپ را وارد کنید", "Enter Serper API Key": "کلید API سرپر را وارد کنید", "Enter Serply API Key": "کلید API سرپلی را وارد کنید", "Enter Serpstack API Key": "کلید API سرپ‌استک را وارد کنید", "Enter server host": "میزبان سرور را وارد کنید", "Enter server label": "برچسب سرور را وارد کنید", "Enter server port": "پورت سرور را وارد کنید", "Enter Sougou Search API sID": "شناسه API جستجوی سوگو را وارد کنید", "Enter Sougou Search API SK": "کلید SK جستجوی سوگو را وارد کنید", "Enter stop sequence": "توالی توقف را وارد کنید", "Enter system prompt": "پرامپت سیستم را وارد کنید", "Enter system prompt here": "پرامپت سیستم را اینجا وارد کنید", "Enter Tavily API Key": "کلید API تاویلی را وارد کنید", "Enter Tavily Extract Depth": "عمق استخراج تاویلی را وارد کنید", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "آدرس عمومی رابط کاربری وب خود را وارد کنید. این آدرس برای تولید پیوندها در اعلان‌ها استفاده خواهد شد.", "Enter the URL of the function to import": "", "Enter the URL to import": "", "Enter Tika Server URL": "آدرس سرور تیکا را وارد کنید", "Enter timeout in seconds": "مهلت زمانی را به ثانیه وارد کنید", "Enter to Send": "برای ارسال اینتر را بزنید", "Enter Top K": "مقدار Top K را وارد کنید", "Enter Top K Reranker": "مقدار Top K بازچینش‌گر را وارد کنید", "Enter URL (e.g. http://127.0.0.1:7860/)": "مقدار URL را وارد کنید (مثال http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "مقدار URL را وارد کنید (مثال http://localhost:11434)", "Enter Yacy Password": "", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "", "Enter Yacy Username": "", "Enter your current password": "رمز عبور فعلی خود را وارد کنید", "Enter Your Email": "ایمیل خود را وارد کنید", "Enter Your Full Name": "نام کامل خود را وارد کنید", "Enter your message": "پیام خود را وارد کنید", "Enter your name": "نام خود را وارد کنید", "Enter Your Name": "", "Enter your new password": "رمز عبور جدید خود را وارد کنید", "Enter Your Password": "رمز عبور خود را وارد کنید", "Enter Your Role": "نقش خود را وارد کنید", "Enter Your Username": "نام کاربری خود را وارد کنید", "Enter your webhook URL": "آدرس وب‌هوک خود را وارد کنید", "Error": "خطا", "ERROR": "خطا", "Error accessing Google Drive: {{error}}": "خطا در دسترسی به گوگل درایو: {{error}}", "Error accessing media devices.": "", "Error starting recording.": "", "Error unloading model: {{error}}": "", "Error uploading file: {{error}}": "خطا در بارگذاری فایل: {{error}}", "Evaluations": "ارزیابی‌ها", "Everyone": "", "Exa API Key": "کلید API اکسا", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "مثال: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "مثال: ALL", "Example: mail": "مثال: mail", "Example: ou=users,dc=foo,dc=example": "مثال: ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "مثال: sAMAccountName یا uid یا userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "تعداد جایگاه‌های مجاز در مجوز شما تمام شده است. لطفاً برای افزایش تعداد جایگاه‌ها با پشتیبانی تماس بگیرید.", "Exclude": "مستثنی کردن", "Execute code for analysis": "اجرای کد برای تحلیل", "Executing **{{NAME}}**...": "در حال اجرای **{{NAME}}**...", "Expand": "گسترش", "Experimental": "آزمایشی", "Explain": "توضیح", "Explore the cosmos": "کاوش کیهان", "Export": "خروجی گرفتن", "Export All Archived Chats": "خروجی گرفتن تمام گفتگوهای بایگانی شده", "Export All Chats (All Users)": "خروجی گرفتن همه گفتگو‌ها (همه کاربران)", "Export chat (.json)": "خروجی گرفتن گفتگو (json)", "Export Chats": "خروجی گرفتن گفتگوها", "Export Config to JSON File": "برون‌ریزی پیکربندی به پروندهٔ JSON", "Export Functions": "برون‌ریزی توابع", "Export Models": "برون‌ریزی مدل‌ها", "Export Presets": "برون‌ریزی پیش‌تنظیم‌ها", "Export Prompt Suggestions": "", "Export Prompts": "برون‌ریزی پرامپت‌ها", "Export to CSV": "برون‌ریزی به CSV", "Export Tools": "برون‌ریزی ابزارها", "Export Users": "", "External": "<PERSON>ا<PERSON><PERSON>ی", "External Document Loader URL required.": "", "External Task Model": "", "External Web Loader API Key": "", "External Web Loader URL": "", "External Web Search API Key": "", "External Web Search URL": "", "Fade Effect for Streaming Text": "", "Failed to add file.": "خطا در افزودن پرونده", "Failed to connect to {{URL}} OpenAPI tool server": "خطا در اتصال به سرور ابزار OpenAPI {{URL}}", "Failed to copy link": "", "Failed to create API Key.": "ایجاد کلید API با خطا مواجه شد.", "Failed to delete note": "", "Failed to extract content from the file: {{error}}": "", "Failed to extract content from the file.": "", "Failed to fetch models": "خطا در دریافت مدل‌ها", "Failed to generate title": "", "Failed to load chat preview": "", "Failed to load file content.": "", "Failed to read clipboard contents": "خو<PERSON>دن محتوای کلیپ بورد ناموفق بود", "Failed to save connections": "خطا در ذخیره‌سازی اتصالات", "Failed to save models configuration": "خطا در ذخیره‌سازی پیکربندی مدل‌ها", "Failed to update settings": "خطا در به‌روزرسانی تنظیمات", "Failed to upload file.": "خطا در بارگذاری پرونده", "Features": "ویژگی‌ها", "Features Permissions": "مجوزهای ویژگی‌ها", "February": "فوریه", "Feedback Details": "", "Feedback History": "تاریخچهٔ بازخورد", "Feedbacks": "بازخوردها", "Feel free to add specific details": "اگر به دلخواه، معلومات خاصی اضافه کنید", "File": "پرونده", "File added successfully.": "پرونده با موفقیت افزوده شد.", "File content updated successfully.": "محتوای پرونده با موفقیت به‌روز شد.", "File Mode": "حالت پرونده", "File not found.": "پرونده یافت نشد.", "File removed successfully.": "پرونده با موفقیت حذف شد.", "File size should not exceed {{maxSize}} MB.": "حجم پرونده نبایستی از {{maxSize}} MB بیشتر باشد.", "File Upload": "", "File uploaded successfully": "پرونده با موفقیت بارگذاری شد", "Files": "پرونده‌ها", "Filter": "", "Filter is now globally disabled": "فیلتر به صورت سراسری غیرفعال شد", "Filter is now globally enabled": "فیلتر به صورت سراسری فعال شد", "Filters": "فیلترها", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "فانگ سرفیس شناسایی شد: نمی توان از نمایه شما به عنوان آواتار استفاده کرد. پیش فرض به عکس پروفایل پیش فرض برگشت داده شد.", "Firecrawl API Base URL": "آدرس پایه API فایرکراول", "Firecrawl API Key": "کلید API فایرکراول", "Floating Quick Actions": "", "Focus chat input": "فوکوس کردن ورودی گپ", "Folder deleted successfully": "پوشه با موفقیت حذف شد", "Folder Name": "", "Folder name cannot be empty.": "نام پوشه نمی‌تواند خالی باشد.", "Folder name updated successfully": "نام پوشه با موفقیت به‌روز شد", "Folder updated successfully": "", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "دستورالعمل ها را کاملا دنبال کرد", "Force OCR": "", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "مسیره<PERSON><PERSON> جدید بسازید", "Form": "فرم", "Format Lines": "", "Format the lines in the output. Defaults to False. If set to True, the lines will be formatted to detect inline math and styles.": "", "Format your variables using brackets like this:": "متغیرهای خود را با استفاده از براکت به این شکل قالب‌بندی کنید:", "Forwards system user session credentials to authenticate": "اعتبارنامه‌های نشست کاربر سیستم را برای احراز هویت ارسال می‌کند", "Full Context Mode": "حالت متن کامل", "Function": "تابع", "Function Calling": "فراخوانی تابع", "Function created successfully": "تابع با موفقیت ایجاد شد", "Function deleted successfully": "تابع با موفقیت حذف شد", "Function Description": "توضیحات تابع", "Function ID": "شناسه تابع", "Function imported successfully": "", "Function is now globally disabled": "تابع به صورت سراسری غیرفعال شد", "Function is now globally enabled": "تابع به صورت سراسری فعال شد", "Function Name": "نام تابع", "Function updated successfully": "تابع با موفقیت به‌روز شد", "Functions": "توابع", "Functions allow arbitrary code execution.": "توابع اجازه اجرای کد دلخواه را می‌دهند.", "Functions imported successfully": "درون‌ریزی توابع با موفقیت انجام شد", "Gemini": "ج<PERSON><PERSON><PERSON><PERSON>", "Gemini API Config": "پیکربندی API جمینی", "Gemini API Key is required.": "کلید API جمینی مورد نیاز است.", "General": "عمومی", "Generate": "", "Generate an image": "تولید یک تصویر", "Generate Image": "تولید تصویر", "Generate prompt pair": "تولید جفت پرامپت", "Generating search query": "در حال تولید پرسوجوی جستجو", "Generating...": "", "Get information on {{name}} in the UI": "", "Get started": "شروع کنید", "Get started with {{WEBUI_NAME}}": "شروع کار با {{WEBUI_NAME}}", "Global": "سراسری", "Good Response": "پا<PERSON><PERSON> خوب", "Google Drive": "گوگل درایو", "Google PSE API Key": "گوگل PSE API کلید", "Google PSE Engine Id": "شناسه موتور PSE گوگل", "Group created successfully": "گروه با موفقیت ایجاد شد", "Group deleted successfully": "گروه با موفقیت حذف شد", "Group Description": "توضیحات گروه", "Group Name": "نام گروه", "Group updated successfully": "گروه با موفقیت به‌روز شد", "Groups": "گروه‌ها", "H1": "", "H2": "", "H3": "", "Haptic Feedback": "با<PERSON><PERSON>و<PERSON>د لمسی", "Hello, {{name}}": "سلام، {{name}}", "Help": "کمک", "Help us create the best community leaderboard by sharing your feedback history!": "با به اشتراک گذاشتن تاریخچه بازخورد خود به ما در ایجاد بهترین تابلوی امتیازات جامعه کمک کنید!", "Hex Color": "رنگ هگز", "Hex Color - Leave empty for default color": "رنگ هگز - برای رنگ پیش‌فرض خالی بگذارید", "Hide": "پنهان‌سازی", "Hide from Sidebar": "", "Hide Model": "پنهان کردن مدل", "High Contrast Mode": "", "Home": "خانه", "Host": "میزبان", "How can I help you today?": "امروز چطور می توانم کمک تان کنم؟", "How would you rate this response?": "این پاسخ را چگونه ارزیابی می‌کنید؟", "HTML": "", "Hybrid Search": "جستجوی همزمان", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "من تأیید می‌کنم که پیامدهای اقدام خود را خوانده و درک کرده‌ام. از خطرات مرتبط با اجرای کد دلخواه آگاه هستم و اعتبار منبع را تأیید کرده‌ام.", "ID": "شناسه", "iframe Sandbox Allow Forms": "اجازه فرم‌ها در سندباکس iframe", "iframe Sandbox Allow Same Origin": "اجازه منشأ یکسان در سندباکس iframe", "Ignite curiosity": "کنجکاوی را برانگیزید", "Image": "تصویر", "Image Compression": "فشرده‌سازی تصویر", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "تولید تصویر", "Image Generation (Experimental)": "تولید تصویر (آزمایشی)", "Image Generation Engine": "موتور تولید تصویر", "Image Max Compression Size": "حداکثر اندازه فشرده‌سازی تصویر", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "تولید پرامپت تصویر", "Image Prompt Generation Prompt": "پرامپت تولید پرامپت تصویر", "Image Settings": "تنظیمات تصویر", "Images": "تصاویر", "Import": "", "Import Chats": "درون‌ریزی گفتگوها", "Import Config from JSON File": "درون‌ریزی از پروندهٔ JSON", "Import From Link": "", "Import Functions": "درون‌ریزی توابع", "Import Models": "درون‌ریزی مدل‌ها", "Import Notes": "", "Import Presets": "درون‌ریزی پیش‌تنظیم‌ها", "Import Prompt Suggestions": "", "Import Prompts": "درون‌ریزی پرامپت‌ها", "Import Tools": "درون‌ریزی ابزارها", "Include": "شامل", "Include `--api-auth` flag when running stable-diffusion-webui": "هنگام اجرای stable-diffusion-webui پرچم `--api-auth` را اضافه کنید", "Include `--api` flag when running stable-diffusion-webui": "فلگ `--api` را هنکام اجرای stable-diffusion-webui استفاده کنید.", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "تأثیر می‌گذارد که الگوریتم چقدر سریع به بازخورد متن تولید شده پاسخ می‌دهد. نرخ یادگیری پایین‌تر منجر به تنظیمات کندتر می‌شود، در حالی که نرخ یادگیری بالاتر الگوریتم را پاسخگوتر می‌کند.", "Info": "اطلاعات", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "کل محتوا را به عنوان زمینه برای پردازش جامع تزریق کنید، این برای پرس‌وجوهای پیچیده توصیه می‌شود.", "Input": "", "Input commands": "ورودی دستورات", "Input Variables": "", "Insert": "", "Insert Follow-Up Prompt to Input": "", "Insert Prompt as Rich Text": "", "Install from Github URL": "نصب از ادر<PERSON>", "Instant Auto-Send After Voice Transcription": "ارسال خودکار فوری پس از رونویسی صوتی", "Integration": "یکپارچه‌سازی", "Interface": "رابط", "Invalid file content": "", "Invalid file format.": "قالب فایل نامعتبر است.", "Invalid JSON file": "", "Invalid JSON format in Additional Config": "", "Invalid Tag": "تگ نامعتبر", "is typing...": "در حال تایپ...", "Italic": "", "January": "ژانویه", "Jina API Key": "کلید API جینا", "join our Discord for help.": "برای کمک به دیسکورد ما بپیوندید.", "JSON": "JSON", "JSON Preview": "پیش نمایش JSON", "July": "ژوئن", "June": "جو<PERSON><PERSON><PERSON>", "Jupyter Auth": "احراز هویت ژوپیتر", "Jupyter URL": "آدرس ژوپیتر", "JWT Expiration": "JWT انقضای", "JWT Token": "JWT توکن", "Kagi Search API Key": "کلید API جستجوی کاگی", "Keep Follow-Up Prompts in Chat": "", "Keep in Sidebar": "", "Key": "<PERSON><PERSON><PERSON><PERSON>", "Keyboard shortcuts": "میانبرهای صفحه کلید", "Knowledge": "د<PERSON>ش", "Knowledge Access": "دسترسی به دانش", "Knowledge Base": "", "Knowledge created successfully.": "دانش با موفقیت ایجاد شد.", "Knowledge deleted successfully.": "دانش با موفقیت حذف شد.", "Knowledge Public Sharing": "اشتراک‌گذاری عمومی دانش", "Knowledge reset successfully.": "دانش با موفقیت بازنشانی شد.", "Knowledge updated successfully": "دانش با موفقیت به‌روز شد", "Kokoro.js (Browser)": "Kokoro.js (مرورگر)", "Kokoro.js Dtype": "نوع داده Kokoro.js", "Label": "برچسب", "Landing Page Mode": "حالت صفحه فرود", "Language": "زبان", "Language Locales": "محلی‌<PERSON><PERSON><PERSON>ی زبان", "Last Active": "آخرین فعال", "Last Modified": "آخرین تغییر", "Last reply": "آخرین پاسخ", "LDAP": "LDAP", "LDAP server updated": "سرور LDAP به‌روز شد", "Leaderboard": "تابلوی امتیازات", "Learn More": "", "Learn more about OpenAPI tool servers.": "درباره سرورهای ابزار OpenAPI بیشتر بدانید.", "Leave empty for no compression": "", "Leave empty for unlimited": "برای نامحدود خالی بگذارید", "Leave empty to include all models from \"{{url}}\" endpoint": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "برای شامل شدن همه مدل‌ها از نقطه پایانی \"{{url}}/api/tags\" خالی بگذارید", "Leave empty to include all models from \"{{url}}/models\" endpoint": "برای شامل شدن همه مدل‌ها از نقطه پایانی \"{{url}}/models\" خالی بگذارید", "Leave empty to include all models or select specific models": "برای شامل شدن همه مدل‌ها خالی بگذارید یا مدل‌های خاص را انتخاب کنید", "Leave empty to use the default prompt, or enter a custom prompt": "برای استفاده از پرامپت پیش‌فرض خالی بگذارید، یا یک پرامپت سفارشی وارد کنید", "Leave model field empty to use the default model.": "برای استفاده از مدل پیش‌فرض، فیلد مدل را خالی بگذارید.", "lexical": "", "License": "مجوز", "Lift List": "", "Light": "روشن", "Listening...": "در حال گوش دادن...", "Llama.cpp": "Llama.cpp", "LLMs can make mistakes. Verify important information.": "مدل‌های زبانی بزرگ می‌توانند اشتباه کنند. اطلاعات مهم را راستی‌آزمایی کنید.", "Loader": "بارگذار", "Loading Kokoro.js...": "در حال بارگذاری Kokoro.js...", "Local": "<PERSON><PERSON><PERSON><PERSON>", "Local Task Model": "", "Location access not allowed": "دسترسی به موقعیت مکانی مجاز نیست", "Lost": "گم شده", "LTR": "LTR", "Made by Open WebUI Community": "ساخته شده توسط OpenWebUI Community", "Make password visible in the user interface": "", "Make sure to enclose them with": "مطمئن شوید که آنها را با این محصور کنید:", "Make sure to export a workflow.json file as API format from ComfyUI.": "مطمئن شوید که یک فایل workflow.json را به عنوان قالب API از ComfyUI صادر کنید.", "Manage": "مدی<PERSON><PERSON>ت", "Manage Direct Connections": "مدیریت اتصالات مستقیم", "Manage Models": "مدیریت مدل‌ها", "Manage Ollama": "مدی<PERSON><PERSON><PERSON> ollama", "Manage Ollama API Connections": "مدیریت اتصالات API ollama", "Manage OpenAI API Connections": "مدیریت اتصالات API اوپن‌ای‌آی", "Manage Pipelines": "مدی<PERSON><PERSON><PERSON> خطوط لوله", "Manage Tool Servers": "مدیریت سرورهای ابزار", "March": "ما<PERSON><PERSON>", "Markdown": "", "Markdown (Header)": "", "Max Speakers": "", "Max Upload Count": "حداکثر تعداد آپلود", "Max Upload Size": "حداکثر اندازه آپلود", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "حداکثر 3 مدل را می توان به طور همزمان دانلود کرد. لطفاً بعداً دوباره امتحان کنید.", "May": "ماهی", "Memories accessible by LLMs will be shown here.": "حافظه های دسترسی به LLMs در اینجا نمایش داده می شوند.", "Memory": "حافظه", "Memory added successfully": "حافظه با موفقیت اضافه شد", "Memory cleared successfully": "حافظه با موفقیت پاک شد", "Memory deleted successfully": "حافظه با موفقیت حذف شد", "Memory updated successfully": "حافظه با موفقیت به‌روز شد", "Merge Responses": "ادغام پاسخ‌ها", "Merged Response": "پاسخ ادغام شده", "Message rating should be enabled to use this feature": "برای استفاده از این ویژگی باید امتیازدهی پیام‌ها فعال باشد", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "پیام های شما بعد از ایجاد لینک شما به اشتراک نمی گردد. کاربران با لینک URL می توانند چت اشتراک را مشاهده کنند.", "Microsoft OneDrive": "", "Microsoft OneDrive (personal)": "", "Microsoft OneDrive (work/school)": "", "Mistral OCR": "تشخیص متن میسترال", "Mistral OCR API Key required.": "کلید API تشخیص متن میسترال مورد نیاز است.", "Model": "مدل", "Model '{{modelName}}' has been successfully downloaded.": "مدل '{{modelName}}' با موفقیت دانلود شد.", "Model '{{modelTag}}' is already in queue for downloading.": "مدل '{{modelTag}}' در حال حاضر در صف برای دانلود است.", "Model {{modelId}} not found": "مدل {{modelId}} یافت نشد", "Model {{modelName}} is not vision capable": "مدل {{modelName}} قادر به بینایی نیست", "Model {{name}} is now {{status}}": "مدل {{name}} در حال حاضر {{status}}", "Model {{name}} is now hidden": "مدل {{name}} اکنون مخفی است", "Model {{name}} is now visible": "مدل {{name}} اکنون قابل مشاهده است", "Model accepts file inputs": "", "Model accepts image inputs": "مدل ورودی تصویر را می‌پذیرد", "Model can execute code and perform calculations": "", "Model can generate images based on text prompts": "", "Model can search the web for information": "", "Model created successfully!": "مدل با موفقیت ایجاد شد!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "مسیر فایل سیستم مدل یافت شد. برای بروزرسانی نیاز است نام کوتاه مدل وجود داشته باشد.", "Model Filtering": "فیلتر کردن مدل", "Model ID": "شناسه مدل", "Model ID is required.": "", "Model IDs": "شناسه‌های مدل", "Model Name": "نام مدل", "Model Name is required.": "", "Model not selected": "مدل انتخاب نشده", "Model Params": "مدل پارامز", "Model Permissions": "مجوزهای مدل", "Model unloaded successfully": "", "Model updated successfully": "مدل با موفقیت به‌روز شد", "Model(s) do not support file upload": "", "Modelfile Content": "محتویات فایل مدل", "Models": "مدل‌ها", "Models Access": "دسترسی به مدل‌ها", "Models configuration saved successfully": "پیکربندی مدل‌ها با موفقیت ذخیره شد", "Models Public Sharing": "اشتراک‌گذاری عمومی مدل‌ها", "Mojeek Search API Key": "کلید API جستجوی موجیک", "more": "بیشتر", "More": "بیشتر", "More Concise": "", "More Options": "", "Name": "نام", "Name your knowledge base": "پایگاه دانش خود را نام‌گذاری کنید", "Native": "بومی", "New Button": "", "New Chat": "گپ جدید", "New Folder": "پوشه جدید", "New Function": "", "New Note": "", "New Password": "<PERSON><PERSON><PERSON> عبور جدید", "New Tool": "", "new-channel": "کانال-ج<PERSON><PERSON>د", "Next message": "", "No chats found": "", "No chats found for this user.": "", "No chats found.": "", "No content": "", "No content found": "محتوایی یافت نشد", "No content found in file.": "", "No content to speak": "محتوایی برای خواندن وجود ندارد", "No distance available": "فاصله‌ای در دسترس نیست", "No feedbacks found": "بازخوردی یافت نشد", "No file selected": "فایلی انتخاب نشده است", "No groups with access, add a group to grant access": "هیچ گروهی با دسترسی وجود ندارد، یک گروه برای اعطای دسترسی اضافه کنید", "No HTML, CSS, or JavaScript content found.": "محتوای HTML، CSS یا JavaScript یافت نشد.", "No inference engine with management support found": "موتور استنتاج با پشتیبانی مدیریت یافت نشد", "No knowledge found": "دانشی یافت نشد", "No memories to clear": "حافظه‌ای برای پاک کردن وجود ندارد", "No model IDs": "شناسه مدلی وجود ندارد", "No models found": "مد<PERSON>ی یافت نشد", "No models selected": "مدلی انتخاب نشده است", "No Notes": "", "No results found": "نتیجه‌ای یافت نشد", "No search query generated": "پرسوجوی جستجویی ایجاد نشده است", "No source available": "منبعی در دسترس نیست", "No users were found.": "کاربری یافت نشد.", "No valves to update": "شیری برای به‌روزرسانی وجود ندارد", "None": "ه<PERSON>چ کدام", "Not factually correct": "اشتباهی فکری نیست", "Not helpful": "م<PERSON><PERSON><PERSON> نیست", "Note deleted successfully": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "توجه: اگر حداقل نمره را تعیین کنید، جستجو تنها اسنادی را با نمره بیشتر یا برابر با حداقل نمره باز می گرداند.", "Notes": "یادداشت‌ها", "Notification Sound": "صدای اعلان", "Notification Webhook": "وب‌هوک اعلان", "Notifications": "اعلان", "November": "نوامبر", "OAuth ID": "شنا<PERSON>ه OAuth", "October": "اکتبر", "Off": "خاموش", "Okay, Let's Go!": "باشه، بزن بریم!", "OLED Dark": "OLED تیره", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API settings updated": "تنظیمات API ollama به‌روز شد", "Ollama Version": "نسخه ollama", "On": "روشن", "OneDrive": "وان‌درایو", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "فقط حروف الفبا، اعداد و خط تیره مجاز هستند", "Only alphanumeric characters and hyphens are allowed in the command string.": "فقط کاراکترهای الفبایی و خط فاصله در رشته فرمان مجاز هستند.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "فقط مجموعه‌ها قابل ویرایش هستند، برای ویرایش/افزودن اسناد یک پایگاه دانش جدید ایجاد کنید.", "Only markdown files are allowed": "", "Only select users and groups with permission can access": "فقط کاربران و گروه‌های دارای مجوز می‌توانند دسترسی داشته باشند", "Oops! Looks like the URL is invalid. Please double-check and try again.": "اوه! به نظر می رسد URL نامعتبر است. لطفاً دوباره بررسی کنید و دوباره امتحان کنید.", "Oops! There are files still uploading. Please wait for the upload to complete.": "اوه! هنوز فایل‌هایی در حال آپلود هستند. لطفاً منتظر تکمیل آپلود بمانید.", "Oops! There was an error in the previous response.": "اوه! در پاسخ قبلی خطایی رخ داد.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "اوه! شما از یک روش پشتیبانی نشده (فقط frontend) استفاده می کنید. لطفاً WebUI را از بکند اجرا کنید.", "Open file": "باز کردن فایل", "Open in full screen": "باز کردن در تمام صفحه", "Open modal to configure connection": "", "Open Modal To Manage Floating Quick Actions": "", "Open new chat": "باز کردن گپ جدید", "Open Sidebar": "", "Open User Profile Menu": "", "Open WebUI can use tools provided by any OpenAPI server.": "Open WebUI می‌تواند از ابزارهای ارائه شده توسط هر سرور OpenAPI استفاده کند.", "Open WebUI uses faster-whisper internally.": "Open WebUI به صورت داخلی از faster-whisper استفاده می‌کند.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI از SpeechT5 و جاسازی‌های گوینده CMU Arctic استفاده می‌کند.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "نسخه Open WebUI (v{{OPEN_WEBUI_VERSION}}) پایین‌تر از نسخه مورد نیاز (v{{REQUIRED_VERSION}}) است", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API تنظیمات", "OpenAI API Key is required.": "مقدار کلید OpenAI API مورد نیاز است.", "OpenAI API settings updated": "تنظیمات API اوپن‌ای‌آی به‌روز شد", "OpenAI URL/Key required.": "URL/Key OpenAI مورد نیاز است.", "openapi.json URL or Path": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "یا", "Ordered List": "", "Organize your users": "کار<PERSON><PERSON><PERSON> خود را سازماندهی کنید", "Other": "دیگر", "OUTPUT": "خروجی", "Output format": "قالب خروجی", "Output Format": "", "Overview": "نمای کلی", "page": "صفحه", "Paginate": "", "Parameters": "", "Password": "<PERSON><PERSON><PERSON> عبور", "Passwords do not match.": "", "Paste Large Text as File": "چسباندن متن بزرگ به عنوان فایل", "PDF document (.pdf)": "PDF سند (.pdf)", "PDF Extract Images (OCR)": "استخراج تصاویر از PDF (OCR)", "pending": "در انتظار", "Pending": "", "Pending User Overlay Content": "", "Pending User Overlay Title": "", "Permission denied when accessing media devices": "دسترسی به دستگاه‌های رسانه رد شد", "Permission denied when accessing microphone": "دسترسی به میکروفون رد شد", "Permission denied when accessing microphone: {{error}}": "هنگام دسترسی به میکروفون، اجازه داده نشد: {{error}}", "Permissions": "مجوزها", "Perplexity API Key": "کلید API پرپلکسیتی", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "شخصی سازی", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "پین کردن", "Pinned": "پین شده", "Pioneer insights": "بینش‌های پیشگام", "Pipe": "", "Pipeline deleted successfully": "خط لوله با موفقیت حذف شد", "Pipeline downloaded successfully": "خط لوله با موفقیت دانلود شد", "Pipelines": "<PERSON>ط لوله", "Pipelines Not Detected": "خطوط لوله شناسایی نشدند", "Pipelines Valves": "شیرا<PERSON>ات خطوط لوله", "Plain text (.md)": "", "Plain text (.txt)": "متن ساده (.txt)", "Playground": "ز<PERSON>ین بازی", "Playwright Timeout (ms)": "مهلت زمانی پلی‌رایت (میلی‌ثانیه)", "Playwright WebSocket URL": "آدرس وب‌سوکت پلی‌رایت", "Please carefully review the following warnings:": "لطفاً هشدارهای زیر را با دقت بررسی کنید:", "Please do not close the settings page while loading the model.": "لطفاً در حین بارگیری مدل، صفحه تنظیمات را نبندید.", "Please enter a prompt": "لطفاً یک پرامپت وارد کنید", "Please enter a valid path": "لطفاً یک مسیر معتبر وارد کنید", "Please enter a valid URL": "لطفاً یک URL معتبر وارد کنید", "Please fill in all fields.": "لطفاً همه فیلدها را پر کنید.", "Please select a model first.": "لطفاً ابتدا یک مدل انتخاب کنید.", "Please select a model.": "لطفاً یک مدل انتخاب کنید.", "Please select a reason": "لطفاً یک دلیل انتخاب کنید", "Please wait until all files are uploaded.": "", "Port": "پورت", "Positive attitude": "نظرات مثبت", "Prefix ID": "شناسه پیشوند", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "شناسه پیشوند برای جلوگیری از تداخل با سایر اتصالات با افزودن پیشوند به شناسه‌های مدل استفاده می‌شود - برای غیرفعال کردن خالی بگذارید", "Prevent file creation": "", "Preview": "", "Previous 30 days": "30 روز قبل", "Previous 7 days": "7 روز قبل", "Previous message": "", "Private": "خصوصی", "Profile Image": "تصویر پروفایل", "Prompt": "پرامپت", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "پیشنهاد (برای مثال: به من بگوید چیزی که برای من یک کاربرد داره درباره ایران)", "Prompt Autocompletion": "تکمیل خودکار پرامپت", "Prompt Content": "محتویات پرامپت", "Prompt created successfully": "پرامپت با موفقیت ایجاد شد", "Prompt suggestions": "پیشنهادات پرامپت", "Prompt updated successfully": "پرامپت با موفقیت به‌روز شد", "Prompts": "پرامپت‌ها", "Prompts Access": "دسترسی پرامپت‌ها", "Prompts Public Sharing": "اشتراک‌گذاری عمومی پرامپت‌ها", "Public": "عمومی", "Pull \"{{searchValue}}\" from Ollama.com": "بازگرداندن \"{{searchValue}}\" از Ollama.com", "Pull a model from Ollama.com": "دریافت یک مدل از Ollama.com", "Query Generation Prompt": "پرامپت تولید کوئری", "Quick Actions": "", "RAG Template": "RAG الگوی", "Rating": "امتیازدهی", "Re-rank models by topic similarity": "رتبه‌بندی مجدد مدل‌ها براساس شباهت موضوعی", "Read": "<PERSON>و<PERSON><PERSON>ن", "Read Aloud": "خو<PERSON>دن به صورت صوتی", "Reason": "", "Reasoning Effort": "تلاش استدلال", "Record": "", "Record voice": "<PERSON><PERSON><PERSON> صدا", "Redirecting you to Open WebUI Community": "در حال هدایت به OpenWebUI Community", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "احتمال تولید محتوای بی‌معنی را کاهش می‌دهد. مقدار بالاتر (مثلاً 100) پاسخ‌های متنوع‌تری می‌دهد، در حالی که مقدار پایین‌تر (مثلاً 10) محافظه‌کارانه‌تر خواهد بود.", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "به خود به عنوان \"کاربر\" اشاره کنید (مثلاً، \"کاربر در حال یادگیری اسپانیایی است\")", "References from": "مراجع از", "Refused when it shouldn't have": "رد شده زمانی که باید نباشد", "Regenerate": "تو<PERSON><PERSON><PERSON> مجدد", "Reindex": "فهرست‌بندی مجدد", "Reindex Knowledge Base Vectors": "فهرست‌بندی مجدد بردارهای پایگاه دانش", "Release Notes": "یادداشت‌های انتشار", "Releases": "", "Relevance": "ارتباط", "Relevance Threshold": "آستانه ارتباط", "Remember Dismissal": "", "Remove": "<PERSON><PERSON><PERSON>", "Remove {{MODELID}} from list.": "", "Remove file": "", "Remove File": "", "Remove image": "", "Remove Model": "<PERSON><PERSON><PERSON> مدل", "Remove this tag from list": "", "Rename": "تغییر نام", "Reorder Models": "ترتی<PERSON> مجدد مدل‌ها", "Reply in Thread": "پاسخ در رشته", "Reranking Engine": "", "Reranking Model": "مدل ری‌شناسی مجدد غیرفعال است", "Reset": "بازنشانی", "Reset All Models": "بازنشانی همه مدل‌ها", "Reset Upload Directory": "بازنشانی پوشه آپلود", "Reset Vector Storage/Knowledge": "بازنشانی ذخیره‌سازی برداری/دانش", "Reset view": "بازنشانی نما", "Response": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "اعلان‌های پاسخ نمی‌توانند فعال شوند زیرا مجوزهای وب‌سایت رد شده‌اند. لطفاً تنظیمات مرورگر خود را برای اعطای دسترسی لازم بررسی کنید.", "Response splitting": "تقسیم پاسخ", "Response Watermark": "", "Result": "نتیجه", "Retrieval": "بازیا<PERSON>ی", "Retrieval Query Generation": "تولید کوئری بازیابی", "Rich Text Input for Chat": "ورودی متن غنی برای چت", "RK": "RK", "Role": "نقش", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "اجرا", "Running": "در حال اجرا", "Save": "ذخیره", "Save & Create": "ذخیره و ایجاد", "Save & Update": "ذخیره و به‌روزرسانی", "Save As Copy": "ذخیره به صویت رونوشت", "Save Tag": "ذخیرهٔ برچسب", "Saved": "ذخیره شد", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "ذخیره گزارش‌های چت مستقیماً در حافظه مرورگر شما دیگر پشتیبانی نمی‌شود. لطفاً با کلیک بر روی دکمه زیر، چند لحظه برای دانلود و حذف گزارش های چت خود وقت بگذارید. نگران نباشید، شما به راحتی می توانید گزارش های چت خود را از طریق بکند دوباره وارد کنید", "Scroll On Branch Change": "", "Search": "جستجو", "Search a model": "جستجوی یک مدل", "Search Base": "پایه جستجو", "Search Chats": "جستجو گفتگوها", "Search Collection": "جستجوی مجموعه‌ها", "Search Filters": "فیلترهای جستجو", "search for archived chats": "", "search for folders": "", "search for pinned chats": "", "search for shared chats": "", "search for tags": "جستجو برای برچسب‌ها", "Search Functions": "جستجوی توابع", "Search In Models": "", "Search Knowledge": "جستجوی دانش", "Search Models": "جستجوی مدل‌ها", "Search Notes": "", "Search options": "گزینه‌های جستجو", "Search Prompts": "جستجوی پرامپت‌ها", "Search Result Count": "تعداد نتایج جستجو", "Search the internet": "جستجوی اینترنت", "Search Tools": "ابزارهای جستجو", "SearchApi API Key": "کلید API SearchApi", "SearchApi Engine": "موتور SearchApi", "Searched {{count}} sites": "جستجوی {{count}} سایت", "Searching \"{{searchQuery}}\"": "جستجوی «{{searchQuery}}»", "Searching Knowledge for \"{{searchQuery}}\"": "جستجوی دانش برای «{{searchQuery}}»", "Searching the web...": "", "Searxng Query URL": "نشانی وب جستجوی Searxng", "See readme.md for instructions": "برای مشاهده دستورالعمل‌ها به readme.md مراجعه کنید", "See what's new": "ب<PERSON><PERSON><PERSON><PERSON>د موارد جدید چه بوده", "Seed": "هسته", "Select a base model": "انتخاب یک مدل پایه", "Select a conversation to preview": "", "Select a engine": "انتخاب یک موتور", "Select a function": "انتخاب یک تابع", "Select a group": "انتخاب یک گروه", "Select a model": "انتخاب یک مدل", "Select a pipeline": "انتخاب یک خط لوله", "Select a pipeline url": "یک ادرس خط لوله را انتخاب کنید", "Select a tool": "انتخاب یک ابقزار", "Select an auth method": "یک روش احراز هویت را انتخاب کنید", "Select an Ollama instance": "یک نمونه ollama را انتخاب کنید", "Select Engine": "انتخاب موتور", "Select Knowledge": "انتخاب دانش", "Select only one model to call": "تنها یک مدل را برای صدا زدن انتخاب کنید", "Selected model(s) do not support image inputs": "مدل) های (انتخاب شده ورودیهای تصویر را پشتیبانی نمیکند", "semantic": "", "Semantic distance to query": "فاصله معنایی تا پرس و جو", "Send": "ارسال", "Send a Message": "ارسال یک پیام", "Send message": "ارسال پیام", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "ارسال `stream_options: { include_usage: true }` در درخواست.\nارائه دهندگان پشتیبانی شده در صورت تنظیم، اطلاعات استفاده از توکن را در پاسخ برمی گردانند.", "September": "سپتامبر", "SerpApi API Key": "کلید API سرپ‌ای‌پی‌آی", "SerpApi Engine": "موتور سرپ‌ای‌پی‌آی", "Serper API Key": "کلید API Serper", "Serply API Key": "کلید API سرپلی", "Serpstack API Key": "کلید API Serpstack", "Server connection verified": "اتصال سرور تأیید شد", "Set as default": "تنظیم به عنوان پیشفرض", "Set CFG Scale": "تنظیم مقیاس CFG", "Set Default Model": "تنظیم مدل پیش فرض", "Set embedding model": "تنظیم مدل جاسازی", "Set embedding model (e.g. {{model}})": "تنظیم مدل پیچشی (برای مثال {{model}})", "Set Image Size": "تنظیم اندازه تصویر", "Set reranking model (e.g. {{model}})": "تنظیم مدل ری‌راینگ (برای مثال {{model}})", "Set Sampler": "تنظیم نمونه‌گیر", "Set Scheduler": "تنظیم زمان‌بند", "Set Steps": "تنظیم گام‌ها", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "تعداد لایه‌هایی را که به GPU منتقل می‌شوند تنظیم کنید. افزایش این مقدار می‌تواند عملکرد مدل‌هایی که برای شتاب‌دهی GPU بهینه‌سازی شده‌اند را به طور قابل توجهی بهبود بخشد اما ممکن است مصرف برق و منابع GPU را نیز افزایش دهد.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "تعداد نخ‌های کارگر مورد استفاده برای محاسبات را تنظیم کنید. این گزینه کنترل می‌کند که چند نخ برای پردازش همزمان درخواست‌های ورودی استفاده می‌شود. افزایش این مقدار می‌تواند عملکرد را در بارهای کاری با همزمانی بالا بهبود بخشد اما ممکن است منابع CPU بیشتری مصرف کند.", "Set Voice": "تنظیم صدا", "Set whisper model": "تنظیم مدل ویسپر", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "یک بایاس ثابت در برابر توکن‌هایی که حداقل یک بار ظاهر شده‌اند تنظیم می‌کند. مقدار بالاتر (مثلاً 1.5) تکرارها را شدیدتر جریمه می‌کند، در حالی که مقدار پایین‌تر (مثلاً 0.9) آسان‌گیرتر خواهد بود. در 0، غیرفعال می‌شود.", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "یک بایاس مقیاس‌پذیر در برابر توکن‌ها برای جریمه کردن تکرارها، بر اساس تعداد دفعات ظاهر شدن آنها تنظیم می‌کند. مقدار بالاتر (مثلاً 1.5) تکرارها را شدیدتر جریمه می‌کند، در حالی که مقدار پایین‌تر (مثلاً 0.9) آسان‌گیرتر خواهد بود. در 0، غیرفعال می‌شود.", "Sets how far back for the model to look back to prevent repetition.": "تنظیم می‌کند که مدل چقدر به عقب نگاه کند تا از تکرار جلوگیری شود.", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "عدد تصادفی اولیه را برای تولید تنظیم می‌کند. تنظیم این به یک عدد خاص باعث می‌شود مدل برای پرامپت یکسان، متن یکسانی تولید کند.", "Sets the size of the context window used to generate the next token.": "اندازه پنجره متن مورد استفاده برای تولید توکن بعدی را تنظیم می‌کند.", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "توالی‌های توقف مورد استفاده را تنظیم می‌کند. وقتی این الگو مشاهده شود، LLM تولید متن را متوقف کرده و برمی‌گردد. الگوهای توقف متعدد می‌توانند با مشخص کردن پارامترهای توقف جداگانه متعدد در فایل مدل تنظیم شوند.", "Settings": "تنظیمات", "Settings saved successfully!": "تنظیمات با موفقیت ذخیره شد!", "Share": "اشتراک‌گذاری", "Share Chat": "اشتراک‌گذاری چت", "Share to Open WebUI Community": "اشتراک گذاری با OpenWebUI Community", "Sharing Permissions": "مجوزهای اشتراک‌گذاری", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "نمایش", "Show \"What's New\" modal on login": "نمایش مودال \"موارد جدید\" هنگام ورود", "Show Admin Details in Account Pending Overlay": "نمایش جزئیات مدیر در پوشش حساب در انتظار", "Show All": "", "Show Formatting Toolbar": "", "Show image preview": "", "Show Less": "", "Show Model": "نمایش مدل", "Show shortcuts": "نمایش میانبرها", "Show your support!": "حمایت خود را نشان دهید!", "Showcased creativity": "ایده‌آفرینی", "Sign in": "ورود", "Sign in to {{WEBUI_NAME}}": "ورود به {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "ورود به {{WEBUI_NAME}} با LDAP", "Sign Out": "خروج", "Sign up": "ثبت نام", "Sign up to {{WEBUI_NAME}}": "ثبت نام در {{WEBUI_NAME}}", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to False.": "", "Signing in to {{WEBUI_NAME}}": "در حال ورود به {{WEBUI_NAME}}", "Sink List": "", "sk-1234": "sk-1234", "Skip Cache": "", "Skip the cache and re-run the inference. Defaults to False.": "", "Sougou Search API sID": "شناسه API جستجوی سوگو", "Sougou Search API SK": "کلید SK API جستجوی سوگو", "Source": "منبع", "Speech Playback Speed": "سرعت پخش گفتار", "Speech recognition error: {{error}}": "خطای تشخیص گفتار: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "موتور گفتار به متن", "Stop": "توقف", "Stop Generating": "", "Stop Sequence": "توقف توالی", "Stream Chat Response": "پاسخ چت جریانی", "Stream Delta Chunk Size": "", "Strikethrough": "", "Strip Existing OCR": "", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "", "STT Model": "مدل تبدیل صدا به متن", "STT Settings": "تنظیمات تبدیل صدا به متن", "Stylized PDF Export": "", "Subtitle (e.g. about the Roman Empire)": "زیرنویس (برای مثال: درباره رمانی)", "Success": "مو<PERSON><PERSON><PERSON>ت", "Successfully updated.": "با موفقیت به‌روز شد", "Suggest a change": "", "Suggested": "پیشنهادی", "Support": "حما<PERSON>ت", "Support this plugin:": "حمایت از این افزونه", "Supported MIME Types": "", "Sync directory": "هم‌گام‌سازی پوشه", "System": "سیستم", "System Instructions": "دستورالعمل‌های سیستم", "System Prompt": "پرامپت سیستم", "Tags": "برچسب‌ها", "Tags Generation": "تولید برچسب‌ها", "Tags Generation Prompt": "پرامپت تولید برچسب‌ها", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "نمونه‌برداری دنباله آزاد برای کاهش تأثیر توکن‌های کم احتمال‌تر از خروجی استفاده می‌شود. مقدار بالاتر (مثلاً 2.0) تأثیر را بیشتر کاهش می‌دهد، در حالی که مقدار 1.0 این تنظیم را غیرفعال می‌کند.", "Talk to model": "گفتگو با مدل", "Tap to interrupt": "برای وقفه ضربه بزنید", "Task List": "", "Task Model": "", "Tasks": "وظایف", "Tavily API Key": "کلید API تاویلی", "Tavily Extract Depth": "عمق استخراج تاویلی", "Tell us more:": "بیشتر بگویید:", "Temperature": "دما", "Temporary Chat": "چت موقت", "Text Splitter": "تقسیم‌کننده متن", "Text-to-Speech": "", "Text-to-Speech Engine": "موتور تبدیل متن به گفتار", "Thanks for your feedback!": "با تشکر از بازخورد شما!", "The Application Account DN you bind with for search": "DN حساب برنامه که برای جستجو به آن متصل می‌شوید", "The base to search for users": "پایه برای جستجوی کاربران", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "اندازه دسته تعیین می‌کند که چند درخواست متنی همزمان پردازش می‌شوند. اندازه دسته بزرگتر می‌تواند عملکرد و سرعت مدل را افزایش دهد، اما به حافظه بیشتری نیاز دارد.", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "توسعه‌دهندگان این افزونه داوطلبان مشتاق از جامعه هستند. اگر این افزونه را مفید می‌دانید، لطفاً در توسعه آن مشارکت کنید.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "تابلوی امتیازات ارزیابی بر اساس سیستم رتبه‌بندی Elo است و در زمان واقعی به‌روز می‌شود.", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "ویژگی LDAP که به ایمیلی که کاربران برای ورود استفاده می‌کنند نگاشت می‌شود.", "The LDAP attribute that maps to the username that users use to sign in.": "ویژگی LDAP که به نام کاربری که کاربران برای ورود استفاده می‌کنند نگاشت می‌شود.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "تابلوی امتیازات در حال حاضر در نسخه بتا است و ممکن است محاسبات رتبه‌بندی را با بهبود الگوریتم تنظیم کنیم.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "حداکثر اندازه فایل به مگابایت. اگر اندازه فایل از این حد بیشتر باشد، فایل آپلود نخواهد شد.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "حداکثر تعداد فایل‌هایی که می‌توانند همزمان در چت استفاده شوند. اگر تعداد فایل‌ها از این حد بیشتر باشد، فایل‌ها آپلود نخواهند شد.", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "امتیاز باید مقداری بین 0.0 (0%) و 1.0 (100%) باشد.", "The stream delta chunk size for the model. Increasing the chunk size will make the model respond with larger pieces of text at once.": "", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "دمای مدل. افزایش دما باعث می‌شود مدل خلاقانه‌تر پاسخ دهد.", "The Weight of BM25 Hybrid Search. 0 more lexical, 1 more semantic. Default 0.5": "", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "پوسته", "Thinking...": "در حال فکر کردن...", "This action cannot be undone. Do you wish to continue?": "این عمل قابل بازگشت نیست. آیا می‌خواهید ادامه دهید؟", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "این کانال در {{createdAt}} ایجاد شد. این آغاز کانال {{channelName}} است.", "This chat won't appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "این اطمینان می‌دهد که مکالمات ارزشمند شما به طور امن در پایگاه داده پشتیبان ذخیره می‌شوند. متشکریم!", "This feature is experimental and may be modified or discontinued without notice.": "", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "این یک ویژگی آزمایشی است، ممکن است طبق انتظار کار نکند و در هر زمان ممکن است تغییر کند.", "This model is not publicly available. Please select another model.": "", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "این گزینه کنترل می‌کند که هنگام تازه‌سازی متن، چند توکن حفظ شوند. برای مثال، اگر روی 2 تنظیم شود، 2 توکن آخر متن مکالمه حفظ خواهند شد. حفظ متن می‌تواند به حفظ پیوستگی مکالمه کمک کند، اما ممکن است توانایی پاسخ به موضوعات جدید را کاهش دهد.", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "این گزینه حداکثر تعداد توکن‌هایی را که مدل می‌تواند در پاسخ خود تولید کند تنظیم می‌کند. افزایش این محدودیت به مدل اجازه می‌دهد پاسخ‌های طولانی‌تری ارائه دهد، اما ممکن است احتمال تولید محتوای بی‌فایده یا نامربوط را نیز افزایش دهد.", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "این گزینه تمام فایل‌های موجود در مجموعه را حذف کرده و با فایل‌های جدید آپلود شده جایگزین می‌کند.", "This response was generated by \"{{model}}\"": "این پاسخ توسط \"{{model}}\" تولید شده است", "This will delete": "این حذف خواهد شد", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "این <strong>{{NAME}}</strong> و <strong>تمام محتویات آن</strong> را حذف خواهد کرد.", "This will delete all models including custom models": "این همه مدل‌ها از جمله مدل‌های سفارشی را حذف خواهد کرد", "This will delete all models including custom models and cannot be undone.": "این همه مدل‌ها از جمله مدل‌های سفارشی را حذف خواهد کرد و قابل بازگشت نیست.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "این پایگاه دانش را بازنشانی کرده و همه فایل‌ها را همگام‌سازی خواهد کرد. آیا می‌خواهید ادامه دهید؟", "Thorough explanation": "توضی<PERSON> کامل", "Thought for {{DURATION}}": "فکر کردن برای {{DURATION}}", "Thought for {{DURATION}} seconds": "فکر کردن برای {{DURATION}} ثانیه", "Thought for less than a second": "", "Tika": "تیکا", "Tika Server URL required.": "آدرس سرور تیکا مورد نیاز است.", "Tiktoken": "تیک توکن", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "با فشردن کلید Tab در ورودی چت پس از هر بار تعویض، چندین متغیر را به صورت متوالی به روزرسانی کنید.", "Title": "عنوان", "Title (e.g. Tell me a fun fact)": "عنوان (برای مثال: به من بگوید چیزی که دوست دارید)", "Title Auto-Generation": "تو<PERSON><PERSON><PERSON> خودکار عنوان", "Title cannot be an empty string.": "عنوان نمی تواند یک رشته خالی باشد.", "Title Generation": "تو<PERSON>ید عنوان", "Title Generation Prompt": "پرامپت تولید عنوان", "TLS": "TLS", "To access the available model names for downloading,": "برای دسترسی به نام مدل های موجود برای دانلود،", "To access the GGUF models available for downloading,": "برای دسترسی به مدل‌های GGUF موجود برای دانلود،", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "برای دسترسی به رابط کاربری وب، لطفاً با مدیر تماس بگیرید. مدیران می‌توانند وضعیت کاربران را از پنل مدیریت مدیریت کنند.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "برای اتصال پایگاه دانش در اینجا، ابتدا آنها را به فضای کاری \"دانش\" اضافه کنید.", "To learn more about available endpoints, visit our documentation.": "برای کسب اطلاعات بیشتر در مورد نقاط پایانی موجود، به مستندات ما مراجعه کنید.", "To learn more about powerful prompt variables, click here": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "برای حفظ حریم خصوصی شما، فقط امتیازات، شناسه‌های مدل، برچسب‌ها و متادیتا از بازخورد شما به اشتراک گذاشته می‌شود - گفتگوهای شما خصوصی باقی می‌ماند و شامل نمی‌شود.", "To select actions here, add them to the \"Functions\" workspace first.": "برای انتخاب عملیات در اینجا، ابتدا آنها را به فضای کاری \"توابع\" اضافه کنید.", "To select filters here, add them to the \"Functions\" workspace first.": "برای انتخاب فیلترها در اینجا، ابتدا آنها را به فضای کاری \"توابع\" اضافه کنید.", "To select toolkits here, add them to the \"Tools\" workspace first.": "برای انتخاب ابزارها در اینجا، ابتدا آنها را به فضای کاری \"ابزارها\" اضافه کنید.", "Toast notifications for new updates": "اعلان‌های پاپ‌آپ برای به‌روزرسانی‌های جدید", "Today": "امروز", "Toggle search": "", "Toggle settings": "نمایش/عدم نمایش تنظیمات", "Toggle sidebar": "نمایش/عدم نمایش نوار کناری", "Toggle whether current connection is active.": "", "Token": "توکن", "Too verbose": "<PERSON><PERSON><PERSON><PERSON> طولانی", "Tool created successfully": "ابزار با موفقیت ایجاد شد", "Tool deleted successfully": "ابزار با موفقیت حذف شد", "Tool Description": "توضیحات ابزار", "Tool ID": "شناسه ابزار", "Tool imported successfully": "ابزار با موفقیت وارد شد", "Tool Name": "نام ابزار", "Tool Servers": "سرورهای ابزار", "Tool updated successfully": "ابزار با موفقیت به‌روزرسانی شد", "Tools": "ابزارها", "Tools Access": "دسترسی به ابزارها", "Tools are a function calling system with arbitrary code execution": "ابزارها یک سیستم فراخوانی تابع با اجرای کد دلخواه هستند", "Tools Function Calling Prompt": "پرامپت فراخوانی تابع ابزارها", "Tools have a function calling system that allows arbitrary code execution.": "ابزارها دارای سیستم فراخوانی تابع هستند که اجازه اجرای کد دلخواه را می‌دهد.", "Tools Public Sharing": "اشتراک‌گذاری عمومی ابزارها", "Top K": "Top K", "Top K Reranker": "رتب<PERSON>‌<PERSON>ن<PERSON>ی مجدد Top K", "Transformers": "ترنسفورمرها", "Trouble accessing Ollama?": "در دسترسی به ollama مشکل دارید؟", "Trust Proxy Environment": "اعتماد به محیط پراکسی", "Try Again": "", "TTS Model": "مدل TTS", "TTS Settings": "تنظیمات TTS", "TTS Voice": "صدای TTS", "Type": "نوع", "Type Hugging Face Resolve (Download) URL": "مقدار URL دانلود (Resolve) Hugging Face را وارد کنید", "Uh-oh! There was an issue with the response.": "اوه! مشکلی در پاسخ وجود داشت.", "UI": "راب<PERSON> کاربری", "Unarchive All": "خارج کردن همه از آرشیو", "Unarchive All Archived Chats": "خارج کردن همه چت‌های آرشیو شده از آرشیو", "Unarchive Chat": "<PERSON>ارج کردن چت از آرشیو", "Underline": "", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "رمزگشایی از اسرار", "Unpin": "برداشتن پین", "Unravel secrets": "کشف رازها", "Unsupported file type.": "", "Untagged": "بدون برچسب", "Untitled": "", "Update": "به‌روزرسانی", "Update and Copy Link": "به روزرسانی و کپی لینک", "Update for the latest features and improvements.": "برای آخرین ویژگی‌ها و بهبودها به‌روزرسانی کنید.", "Update password": "به روزرسانی رمزعبور", "Updated": "بارگذاری شد", "Updated at": "بارگذاری در", "Updated At": "بارگذاری در", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "برای قابلیت‌های پیشرفته، از جمله تم و برندسازی سفارشی و پشتیبانی اختصاصی، به طرح دارای مجوز ارتقا دهید.", "Upload": "بارگذاری", "Upload a GGUF model": "آپلود یک مدل GGUF", "Upload Audio": "", "Upload directory": "پوشه آپلود", "Upload files": "آپلود فایل‌ها", "Upload Files": "بارگذاری پروندهها", "Upload Pipeline": "<PERSON><PERSON> تولید آپلود", "Upload Progress": "پیشرفت آپلود", "URL": "آدرس اینترنتی", "URL Mode": "حالت URL", "Usage": "", "Use '#' in the prompt input to load and include your knowledge.": "از '#' در ورودی پرامپت برای بارگیری و شامل کردن دانش خود استفاده کنید.", "Use Gravatar": "استفاده از گراواتار", "Use groups to group your users and assign permissions.": "از گروه‌ها برای گروه‌بندی کاربران و تخصیص مجوزها استفاده کنید.", "Use Initials": "استفاده از سرواژه", "Use LLM": "", "Use no proxy to fetch page contents.": "از هیچ پراکسی برای دریافت محتوای صفحه استفاده نکنید.", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "از پراکسی تعیین شده توسط متغیرهای محیطی http_proxy و https_proxy برای دریافت محتوای صفحه استفاده کنید.", "user": "کاربر", "User": "کاربر", "User Groups": "", "User location successfully retrieved.": "موقعیت مکانی کاربر با موفقیت دریافت شد.", "User menu": "", "User Webhooks": "وب‌هوک‌های کاربر", "Username": "نام کاربری", "Users": "کاربران", "Using Entire Document": "", "Using Focused Retrieval": "", "Using the default arena model with all models. Click the plus button to add custom models.": "در حال استفاده از مدل آرنا با همهٔ مدل‌های دیگر به طور پیش‌فرض. برای افزودن مدل‌های سفارشی، روی دکمه به‌علاوه کلیک کنید.", "Valid time units:": "واحدهای زمانی معتبر:", "Valves": "شیرها", "Valves updated": "شیرها به‌روزرسانی شدند", "Valves updated successfully": "شیرها با موفقیت به‌روزرسانی شدند", "variable": "متغ<PERSON>ر", "Verify Connection": "ت<PERSON><PERSON><PERSON>د اتصال", "Verify SSL Certificate": "تأیید گواهی SSL", "Version": "نسخه", "Version {{selectedVersion}} of {{totalVersions}}": "نسخهٔ {{selectedVersion}} از {{totalVersions}}", "View Replies": "مشاهده پاسخ‌ها", "View Result from **{{NAME}}**": "مشاهده نتیجه از **{{NAME}}**", "Visibility": "قابلیت مشاهده", "Vision": "", "Voice": "صوت", "Voice Input": "ورودی صوتی", "Voice mode": "", "Warning": "هشدار", "Warning:": "هشدار", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "هشدار: فعال کردن این گزینه به کاربران اجازه می‌دهد کد دلخواه را روی سرور آپلود کنند.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "هشدار: اگر شما به روز کنید یا تغییر دهید مدل شما، باید تمام سند ها را مجددا وارد کنید.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "هشدار: اجرای ژوپیتر امکان اجرای کد دلخواه را فراهم می‌کند که خطرات امنیتی جدی به همراه دارد - با احتیاط زیاد ادامه دهید.", "Web": "وب", "Web API": "API وب", "Web Loader Engine": "موتور بارگذاری وب", "Web Search": "جستجوی وب", "Web Search Engine": "موتور جستجوی وب", "Web Search in Chat": "جستجوی وب در گفتگو", "Web Search Query Generation": "تولید کوئری جستجوی وب", "Webhook URL": "نشانی وب‌هوک", "WebUI Settings": "تنظیمات WebUI", "WebUI URL": "آدرس WebUI", "WebUI will make requests to \"{{url}}\"": "WebUI به \"{{url}}\" درخواست خواهد داد", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI به \"{{url}}/api/chat\" درخواست خواهد داد", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI به \"{{url}}/chat/completions\" درخواست خواهد داد", "What are you trying to achieve?": "به دنبال دستیابی به چه هدفی هستید؟", "What are you working on?": "روی چه چیزی کار می‌کنید؟", "What's New in": "", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "وقتی فعال باشد، مدل به هر پیام گفتگو در زمان واقعی پاسخ می‌دهد و به محض ارسال پیام توسط کاربر، پاسخی تولید می‌کند. این حالت برای برنامه‌های گفتگوی زنده مفید است، اما ممکن است در سخت‌افزارهای کندتر بر عملکرد تأثیر بگذارد.", "wherever you are": "هر جا که هستید", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "", "Whisper (Local)": "و<PERSON><PERSON><PERSON><PERSON> (مح<PERSON><PERSON>)", "Why?": "چرا؟", "Widescreen Mode": "حالت صفحهٔ عریض", "Won": "برنده شد", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "با top-k همکاری می‌کند. مقدار بالاتر (مثلاً 0.95) منجر به متن متنوع‌تر می‌شود، در حالی که مقدار پایین‌تر (مثلاً 0.5) متن متمرکزتر و محافظه‌کارانه‌تری تولید می‌کند.", "Workspace": "<PERSON><PERSON><PERSON><PERSON> کار", "Workspace Permissions": "مجوزهای محیط کار", "Write": "نوشتن", "Write a prompt suggestion (e.g. Who are you?)": "یک پیشنهاد پرامپت بنویسید (مثلاً شما کی هستید؟)", "Write a summary in 50 words that summarizes [topic or keyword].": "خلاصه ای در 50 کلمه بنویسید که [موضوع یا کلمه کلیدی] را خلاصه کند.", "Write something...": "چیزی بنویسید...", "Yacy Instance URL": "", "Yacy Password": "", "Yacy Username": "", "Yesterday": "دی<PERSON><PERSON><PERSON>", "You": "شما", "You are currently using a trial license. Please contact support to upgrade your license.": "شما در حال حاضر از نسخه آزمایشی استفاده می‌کنید. لطفاً برای ارتقای مجوز خود با پشتیبانی تماس بگیرید.", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "شما در هر زمان نهایتا می‌توانید با {{maxCount}} پرونده گفتگو کنید.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "شما می‌توانید تعاملات خود با LLM‌ها را با افزودن خاطرات از طریق دکمه 'مدیریت' در زیر شخصی‌سازی کنید تا آنها مفیدتر و متناسب‌تر با شما شوند.", "You cannot upload an empty file.": "نمی‌توانید فایل خالی آپلود کنید.", "You do not have permission to upload files.": "شما اجازه آپلود فایل ندارید.", "You have no archived conversations.": "شما هیچ گفتگوی ذخیره شده ندارید.", "You have shared this chat": "شما این گفتگو را به اشتراک گذاشته اید", "You're a helpful assistant.": "تو یک دستیار سودمند هستی.", "You're now logged in.": "شما اکنون وارد شده‌اید.", "Your account status is currently pending activation.": "وضعیت حساب شما در حال حاضر در انتظار فعال‌سازی است.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "تمام مشارکت شما مستقیماً به توسعه‌دهنده افزونه می‌رسد؛ Open WebUI هیچ درصدی دریافت نمی‌کند. با این حال، پلتفرم تأمین مالی انتخاب شده ممکن است کارمزد خود را داشته باشد.", "Youtube": "یوتیوب", "Youtube Language": "زبان یوتیوب", "Youtube Proxy URL": "آدرس پراکسی یوتیوب"}