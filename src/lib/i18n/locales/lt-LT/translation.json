{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' arba '-1' kad neiš<PERSON> iš <PERSON>.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(pvz. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(pvz. `sh webui.sh --api`)", "(latest)": "(naujausias)", "(leave blank for to use commercial endpoint)": "", "[Last] dddd [at] h:mm A": "", "[Today at] h:mm A": "", "[Yesterday at] h:mm A": "", "{{ models }}": "{{ models }}", "{{COUNT}} Available Tools": "", "{{COUNT}} characters": "", "{{COUNT}} hidden lines": "", "{{COUNT}} Replies": "", "{{COUNT}} words": "", "{{user}}'s Chats": "{{user}} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "{{webUIName}} Backend Required": "{{webUIName}} būtinas serveris", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Užduočių modelis naudojamas pokalbių pavadinimų ir paieškos užklausų generavimui.", "a user": "naudotojas", "About": "<PERSON><PERSON>", "Accept autocomplete generation / Jump to prompt variable": "", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "Paskyra", "Account Activation Pending": "<PERSON><PERSON><PERSON> p<PERSON>", "Accurate information": "Tiksli informacija", "Action": "", "Actions": "Veiks<PERSON><PERSON>", "Activate": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Add": "<PERSON><PERSON><PERSON><PERSON>", "Add a model ID": "", "Add a short description about what this model does": "<PERSON><PERSON><PERSON><PERSON> trumpą modelio aprašymą", "Add a tag": "<PERSON><PERSON><PERSON><PERSON>", "Add Arena Model": "", "Add Connection": "", "Add Content": "", "Add content here": "", "Add Custom Parameter": "", "Add custom prompt": "Pridėti užklausos šabloną", "Add Details": "", "Add Files": "<PERSON><PERSON><PERSON><PERSON>", "Add Group": "", "Add Memory": "<PERSON><PERSON><PERSON><PERSON> atminį", "Add Model": "<PERSON><PERSON><PERSON><PERSON> modelį", "Add Reaction": "", "Add Tag": "<PERSON><PERSON><PERSON><PERSON>", "Add Tags": "<PERSON><PERSON><PERSON><PERSON>", "Add text content": "", "Add User": "<PERSON><PERSON><PERSON><PERSON> na<PERSON>", "Add User Group": "", "Additional Config": "", "Additional configuration options for marker. This should be a JSON string with key-value pairs. For example, '{\"key\": \"value\"}'. Supported keys include: disable_links, keep_pageheader_in_output, keep_pagefooter_in_output, filter_blank_pages, drop_repeated_text, layout_coverage_threshold, merge_threshold, height_tolerance, gap_threshold, image_threshold, min_line_length, level_count, default_level": "", "Adjusting these settings will apply changes universally to all users.": "Šių nustatymų pakeitimas bus pritakytas visiems naudotojams.", "admin": "<PERSON><PERSON>", "Admin": "<PERSON><PERSON>", "Admin Panel": "Administratorių panelė", "Admin Settings": "Administratorių nustatymai", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Administratoriai visada turi visus įrankius. Naudotojai turi tuėti prieigą prie dokumentų per modelių nuostatas", "Advanced Parameters": "Pažengę nustatymai", "Advanced Params": "Pažengę nustatymai", "AI": "", "All": "", "All Documents": "Visi dokumentai", "All models deleted successfully": "", "Allow Call": "", "Allow Chat Controls": "", "Allow Chat Delete": "", "Allow Chat Deletion": "Leisti pokalbių ištrynimą", "Allow Chat Edit": "", "Allow Chat Export": "", "Allow Chat Params": "", "Allow Chat Share": "", "Allow Chat System Prompt": "", "Allow Chat Valves": "", "Allow File Upload": "", "Allow Multiple Models in Chat": "", "Allow non-local voices": "<PERSON><PERSON><PERSON> ne<PERSON> balsus", "Allow Speech to Text": "", "Allow Temporary Chat": "", "Allow Text to Speech": "", "Allow User Location": "Leisti naudotojo vietos mat<PERSON>", "Allow Voice Interruption in Call": "Leisti pertraukimą skambučio metu", "Allowed Endpoints": "", "Allowed File Extensions": "", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "", "Already have an account?": "Ar jau turite pask<PERSON>?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "", "Always": "", "Always Collapse Code Blocks": "", "Always Expand Details": "", "Always Play Notification Sound": "", "Amazing": "", "an assistant": "assist<PERSON><PERSON>", "Analytics": "", "Analyzed": "", "Analyzing...": "", "and": "ir", "and {{COUNT}} more": "", "and create a new shared link.": "sukurti naują dalinimosi nuorodą", "Android": "", "API": "", "API Base URL": "API basės nuoroda", "API Base URL for Datalab Marker service. Defaults to: https://www.datalab.to/api/v1/marker": "", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "API raktas", "API Key created.": "API raktas sukurtas", "API Key Endpoint Restrictions": "", "API keys": "API raktai", "API Version": "", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "<PERSON><PERSON><PERSON>", "Archive": "Archyvai", "Archive All Chats": "Archyvuoti visus pokalbius", "Archived Chats": "Archyvuoti p<PERSON>i", "archived-chat-export": "", "Are you sure you want to clear all memories? This action cannot be undone.": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "Are esate tikri?", "Arena Models": "", "Artifacts": "", "Ask": "", "Ask a question": "", "Assistant": "", "Attach file from knowledge": "", "Attention to detail": "<PERSON><PERSON><PERSON><PERSON>", "Attribute for Mail": "", "Attribute for Username": "", "Audio": "Audio įrašas", "August": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Auth": "", "Authenticate": "", "Authentication": "", "Auto": "", "Auto-Copy Response to Clipboard": "Automatiškai nukopijuoti atsakymą", "Auto-playback response": "Automat<PERSON>s <PERSON>", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111 Api Auth String", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 bazės nuoroda", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 bazės nuoroda reikalinga.", "Available list": "", "Available Tools": "", "available!": "prieinama!", "Awful": "", "Azure AI Speech": "", "Azure Region": "", "Back": "Atgal", "Bad Response": "<PERSON><PERSON><PERSON><PERSON>", "Banners": "Baneriai", "Base Model (From)": "<PERSON><PERSON><PERSON> modelis", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "", "before": "<PERSON><PERSON><PERSON>", "Being lazy": "<PERSON><PERSON><PERSON><PERSON> tingiu", "Beta": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "BM25 Weight": "", "Bocha Search API Key": "", "Bold": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "", "Brave Search API Key": "Brave Search API raktas", "Bullet List": "", "Button ID": "", "Button Label": "", "Button Prompt": "", "By {{name}}": "", "Bypass Embedding and Retrieval": "", "Bypass Web Loader": "", "Cache Base Model List": "", "Calendar": "", "Call": "Skambinti", "Call feature is not supported when using Web STT engine": "Skambučio funkcionalumas neleidžiamas naudojant Web STT variklį", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "<PERSON><PERSON><PERSON><PERSON>", "Capabilities": "Gebė<PERSON>i", "Capture": "", "Capture Audio": "", "Certificate Path": "", "Change Password": "<PERSON><PERSON><PERSON> slaptažodį", "Channel Name": "", "Channels": "", "Character": "", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "Pokalbis", "Chat Background Image": "Pokalbio galinė užsklanda", "Chat Bubble UI": "Pokalbio burbulo s<PERSON>", "Chat Controls": "Pokalbio valdymas", "Chat direction": "Pokalbio linkmė", "Chat ID": "", "Chat Overview": "", "Chat Permissions": "", "Chat Tags Auto-Generation": "", "Chats": "Pokalbiai", "Check Again": "<PERSON><PERSON><PERSON><PERSON> i<PERSON> naujo", "Check for updates": "<PERSON><PERSON><PERSON><PERSON>", "Checking for updates...": "Ieškoma atnaujinimų...", "Choose a model before saving...": "Pasirinkite modelį prieš i<PERSON>...", "Chunk Overlap": "Blokų persidengimas", "Chunk Size": "Blokų dydis", "Ciphers": "", "Citation": "Citata", "Citations": "", "Clear memory": "<PERSON><PERSON><PERSON><PERSON> atmintį", "Clear Memory": "", "click here": "", "Click here for filter guides.": "", "Click here for help.": "Paspauskite čia dėl pagalbos.", "Click here to": "Paspauskite čia, kad:", "Click here to download user import template file.": "Pasauskite čia norėdami sukurti naudotojo įkėlimo šablono rinkmeną", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to see available models.": "", "Click here to select": "Spauskite čia norėdami pasirinkti", "Click here to select a csv file.": "Spauskite čia tam, kad pasirinkti csv failą", "Click here to select a py file.": "Spauskite čia norėdami pasirinkti py failą", "Click here to upload a workflow.json file.": "", "click here.": "paspauskite čia.", "Click on the user role button to change a user's role.": "Paspauskite ant naudotojo rol<PERSON> my<PERSON> ta<PERSON>, kad pakeisti naudotojo rolę.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> naudo<PERSON>.", "Clone": "<PERSON><PERSON><PERSON><PERSON>", "Clone Chat": "", "Clone of {{TITLE}}": "", "Close": "Uždaryti", "Close Banner": "", "Close Configure Connection Modal": "", "Close modal": "", "Close settings modal": "", "Close Sidebar": "", "Code Block": "", "Code execution": "", "Code Execution": "", "Code Execution Engine": "", "Code Execution Timeout": "", "Code formatted successfully": "Kodas suformatuotas sėkmingai", "Code Interpreter": "", "Code Interpreter Engine": "", "Code Interpreter Prompt Template": "", "Collapse": "", "Collection": "Kolekcija", "Color": "", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "ComfyUI bazės nuoroda", "ComfyUI Base URL is required.": "ComfyUI bazės nuoroda privaloma", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "", "Command": "Command", "Comment": "", "Completions": "", "Compress Images in Channels": "", "Concurrent Requests": "<PERSON><PERSON><PERSON> vienu metu", "Configure": "", "Confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Confirm Password": "Patvirtinkite slaptažodį", "Confirm your action": "Patvirtinkite ve<PERSON>smą", "Confirm your new password": "", "Confirm Your Password": "", "Connect to your own OpenAI compatible API endpoints.": "", "Connect to your own OpenAPI compatible external tool servers.": "", "Connection failed": "", "Connection successful": "", "Connection Type": "", "Connections": "Ryšiai", "Connections saved successfully": "", "Connections settings updated": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "", "Contact Admin for WebUI Access": "Susisiekite su administratoriumi dėl prieigos", "Content": "Turinys", "Content Extraction Engine": "", "Continue Response": "Tęsti atsakymą", "Continue with {{provider}}": "<PERSON><PERSON><PERSON><PERSON> su {{tie<PERSON><PERSON><PERSON>}}", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "", "Controls": "<PERSON><PERSON><PERSON>", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "", "Copied": "Nukopijuota", "Copied link to clipboard": "", "Copied shared chat URL to clipboard!": "Nukopijavote pokalbio nuorodą", "Copied to clipboard": "", "Copy": "Ko<PERSON><PERSON><PERSON><PERSON>", "Copy Formatted Text": "", "Copy last code block": "Kopijuoti paskutinį kodo bloką", "Copy last response": "Kopijuoti paskutinį atsakymą", "Copy link": "", "Copy Link": "Kopijuoti nuorodą", "Copy to clipboard": "", "Copying to clipboard was successful!": "La copie dans le presse-papiers a réussi !", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "", "Create": "", "Create a knowledge base": "", "Create a model": "Sukurti modelį", "Create Account": "<PERSON><PERSON><PERSON> un compte", "Create Admin Account": "", "Create Channel": "", "Create Folder": "", "Create Group": "", "Create Knowledge": "", "Create new key": "Sukurti naują raktą", "Create new secret key": "Sukurti naują slaptą raktą", "Create Note": "", "Create your first note by clicking on the plus button below.": "", "Created at": "Sukurta", "Created At": "Sukurta", "Created by": "Sukurta", "CSV Import": "CSV importavimas", "Ctrl+Enter to Send": "", "Current Model": "<PERSON><PERSON><PERSON><PERSON> modelis", "Current Password": "<PERSON><PERSON><PERSON>", "Custom": "Personalizuota", "Custom description enabled": "", "Custom Parameter Name": "", "Custom Parameter Value": "", "Danger Zone": "", "Dark": "Tamsus", "Database": "Duomenų bazė", "Datalab Marker API": "", "Datalab Marker API Key required.": "", "DD/MM/YYYY": "", "December": "<PERSON><PERSON><PERSON><PERSON>", "Default": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Default (Open AI)": "", "Default (SentenceTransformers)": "Numatytasis (SentenceTransformers)", "Default action buttons will be used.": "", "Default description enabled": "", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "", "Default Model": "Numatytasis modelis", "Default model updated": "Numatytasis modelis atnaujintas", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "Numatytieji užklausų pasiūlymai", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "", "Default User Role": "<PERSON><PERSON><PERSON><PERSON><PERSON> naudo<PERSON> rol<PERSON>", "Delete": "<PERSON><PERSON><PERSON><PERSON>", "Delete a model": "<PERSON><PERSON><PERSON><PERSON> modėlį", "Delete All Chats": "<PERSON><PERSON><PERSON><PERSON> visus pokalbius", "Delete All Models": "", "Delete chat": "<PERSON><PERSON><PERSON><PERSON> pokalbį", "Delete Chat": "<PERSON><PERSON><PERSON><PERSON> pokalbį", "Delete chat?": "<PERSON><PERSON><PERSON><PERSON> pokalbį?", "Delete folder?": "", "Delete function?": "<PERSON><PERSON><PERSON><PERSON>", "Delete Message": "", "Delete message?": "", "Delete note?": "", "Delete prompt?": "<PERSON>š<PERSON>nti užklausą?", "delete this link": "Ištrinti nuorodą", "Delete tool?": "<PERSON><PERSON><PERSON><PERSON> įrankį?", "Delete User": "<PERSON><PERSON><PERSON><PERSON>", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} ištrinta", "Deleted {{name}}": "<PERSON><PERSON><PERSON><PERSON> {{name}}", "Deleted User": "", "Deployment names are required for Azure OpenAI": "", "Describe Pictures in Documents": "", "Describe your knowledge base and objectives": "", "Description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Detect Artifacts Automatically": "", "Dictate": "", "Didn't fully follow instructions": "Pilnai nesekė instrukcijų", "Direct": "", "Direct Connections": "", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "", "Direct Tool Servers": "", "Disable Code Interpreter": "", "Disable Image Extraction": "", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "", "Disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Discover a function": "<PERSON><PERSON><PERSON>", "Discover a model": "<PERSON><PERSON><PERSON> modelį", "Discover a prompt": "<PERSON><PERSON><PERSON>", "Discover a tool": "<PERSON><PERSON><PERSON> įrankį", "Discover how to use Open WebUI and seek support from the community.": "", "Discover wonders": "", "Discover, download, and explore custom functions": "<PERSON><PERSON><PERSON>, atsisiųsti arba rasti naujas funkcijas", "Discover, download, and explore custom prompts": "Atrasti ir parsisiųsti užklausas", "Discover, download, and explore custom tools": "<PERSON><PERSON><PERSON>, atsisiųsti arba rasti naujų įrankių", "Discover, download, and explore model presets": "Atrasti ir parsisiųsti modelių konfigūracija", "Display": "", "Display Emoji in Call": "Rodyti emoji pokalbiuose", "Display Multi-model Responses in Tabs": "", "Display the username instead of You in the Chat": "Rodyti naudotojo vardą vietoje žodžio <PERSON>", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "Neinstaliuokite funkcijų iš nepatikimų šaltinių", "Do not install tools from sources you do not fully trust.": "Neinstaliuokite įrankių iš nepatikimų šaltinių", "Docling": "", "Docling Server URL required.": "", "Document": "Dokumentas", "Document Intelligence": "", "Document Intelligence endpoint and key required.": "", "Documentation": "Dokumentacija", "Documents": "Dokumentai", "does not make any external connections, and your data stays securely on your locally hosted server.": "neturi jokių išorinių ryšių ir duomenys lieka serveryje.", "Domain Filter List": "", "Don't have an account?": "Neturite paskyros?", "don't install random functions from sources you don't trust.": "neinstaliuokite funkcijų iš nepatikimų šaltinių", "don't install random tools from sources you don't trust.": "neinstaliuokite įrankių iš nepatikimų šaltinių", "Don't like the style": "<PERSON><PERSON><PERSON><PERSON> stilius", "Done": "Atlikta", "Download": "Parsisiųsti", "Download as SVG": "", "Download canceled": "Parsisiuntimas atšauktas", "Download Database": "Parsisiųsti duomenų bazę", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to upload": "", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "pvz. '30s', '10m'. Laiko vienetai yra 's', 'm', 'h'.", "e.g. \"json\" or a JSON schema": "", "e.g. 60": "", "e.g. A filter to remove profanity from text": "", "e.g. en": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. pdf, docx, txt": "", "e.g. Tools for performing various operations": "", "e.g., 3, 4, 5 (leave blank for default)": "", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "", "e.g., westus (leave blank for eastus)": "", "Edit": "Red<PERSON><PERSON><PERSON>", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Folder": "", "Edit Memory": "Koreguoti atminį", "Edit User": "Redaguoti naudotoją", "Edit User Group": "", "Edited": "", "Editing": "", "Eject": "", "ElevenLabs": "ElevenLabs", "Email": "El. <PERSON>", "Embark on adventures": "", "Embedding": "", "Embedding Batch Size": "Embedding dydis", "Embedding Model": "Embedding modelis", "Embedding Model Engine": "Embedding modelio variklis", "Embedding model set to \"{{embedding_model}}\"": "Embedding modelis nustatytas kaip\"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Code Execution": "", "Enable Code Interpreter": "", "Enable Community Sharing": "<PERSON><PERSON><PERSON>ą<PERSON> su bendruomene", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "", "Enable Mirostat sampling for controlling perplexity.": "", "Enable New Sign Ups": "Aktyvuoti naujas registracijas", "Enabled": "<PERSON><PERSON><PERSON>", "Endpoint URL": "", "Enforce Temporary Chat": "", "Enhance": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Įsitikinkite, kad CSV failas turi 4 kolonas šiuo eiliškumu: Name, Email, Password, Role.", "Enter {{role}} message here": "Įveskite {{role}} ž<PERSON><PERSON><PERSON>ia", "Enter a detail about yourself for your LLMs to recall": "Įveskite informaciją apie save jūsų modelio atminčiai", "Enter a title for the pending user info overlay. Leave empty for default.": "", "Enter a watermark for the response. Leave empty for none.": "", "Enter api auth string (e.g. username:password)": "Įveskite API autentifikacijos kodą (pvz. username:password)", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Bocha Search API Key": "", "Enter Brave Search API Key": "Įveskite Bravo Search API raktą", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "", "Enter Chunk Overlap": "Įveskite blokų persidengimą", "Enter Chunk Size": "Įveskite blokų dydį", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "", "Enter Datalab Marker API Base URL": "", "Enter Datalab Marker API Key": "", "Enter description": "", "Enter Docling OCR Engine": "", "Enter Docling OCR Language(s)": "", "Enter Docling Server URL": "", "Enter Document Intelligence Endpoint": "", "Enter Document Intelligence Key": "", "Enter domains separated by commas (e.g., example.com,site.org)": "", "Enter Exa API Key": "", "Enter External Document Loader API Key": "", "Enter External Document Loader URL": "", "Enter External Web Loader API Key": "", "Enter External Web Loader URL": "", "Enter External Web Search API Key": "", "Enter External Web Search URL": "", "Enter Firecrawl API Base URL": "", "Enter Firecrawl API Key": "", "Enter folder name": "", "Enter Github Raw URL": "Įveskite GitHub Raw nuorodą", "Enter Google PSE API Key": "Įveskite Google PSE API raktą", "Enter Google PSE Engine Id": "Įveskite Google PSE variklio ID", "Enter Image Size (e.g. 512x512)": "Įveskite paveiksliuko dydį (pvz. 512x512)", "Enter Jina API Key": "", "Enter JSON config (e.g., {\"disable_links\": true})": "", "Enter Jupyter Password": "", "Enter Jupyter Token": "", "Enter Jupyter URL": "", "Enter Kagi Search API Key": "", "Enter Key Behavior": "", "Enter language codes": "Įveskite kalbos kodus", "Enter Mistral API Key": "", "Enter Model ID": "", "Enter model tag (e.g. {{modelTag}})": "Įveskite modelio žymą (pvz. {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name": "", "Enter New Password": "", "Enter Number of Steps (e.g. 50)": "Įveskite žingsnių kiekį (pvz. 50)", "Enter Perplexity API Key": "", "Enter Playwright Timeout": "", "Enter Playwright WebSocket URL": "", "Enter proxy URL (e.g. **************************:port)": "", "Enter reasoning effort": "", "Enter Sampler (e.g. Euler a)": "", "Enter Scheduler (e.g. Karras)": "", "Enter Score": "Įveskite rezultatą", "Enter SearchApi API Key": "", "Enter SearchApi Engine": "", "Enter Searxng Query URL": "Įveskite Searxng Query nuorodą", "Enter Seed": "", "Enter SerpApi API Key": "", "Enter SerpApi Engine": "", "Enter Serper API Key": "Įveskite Serper API raktą", "Enter Serply API Key": "Įveskite Serply API raktą", "Enter Serpstack API Key": "Įveskite Serpstack API raktą", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter Sougou Search API sID": "", "Enter Sougou Search API SK": "", "Enter stop sequence": "Įveskite paba<PERSON>s sek<PERSON>ą", "Enter system prompt": "Įveskite sistemos <PERSON>", "Enter system prompt here": "", "Enter Tavily API Key": "Įveskite Tavily API raktą", "Enter Tavily Extract Depth": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter the URL of the function to import": "", "Enter the URL to import": "", "Enter Tika Server URL": "Įveskite Tika serverio nuorodą", "Enter timeout in seconds": "", "Enter to Send": "", "Enter Top K": "Įveskite Top K", "Enter Top K Reranker": "", "Enter URL (e.g. http://127.0.0.1:7860/)": "Įveskite nuorodą (pvz. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Įveskite nuorododą (pvz. http://localhost:11434", "Enter Yacy Password": "", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "", "Enter Yacy Username": "", "Enter your current password": "", "Enter Your Email": "Įveskite el. pa<PERSON><PERSON> ad<PERSON>", "Enter Your Full Name": "Įveskite vardą bei pavardę", "Enter your message": "Įveskite žinutę", "Enter your name": "", "Enter Your Name": "", "Enter your new password": "", "Enter Your Password": "Įveskite slaptažodį", "Enter Your Role": "Įveskite savo rolę", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "<PERSON><PERSON><PERSON>", "ERROR": "", "Error accessing Google Drive: {{error}}": "", "Error accessing media devices.": "", "Error starting recording.": "", "Error unloading model: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "", "Everyone": "", "Exa API Key": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: mail": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "", "Exclude": "", "Execute code for analysis": "", "Executing **{{NAME}}**...": "", "Expand": "", "Experimental": "Eksperimentinis", "Explain": "", "Explore the cosmos": "", "Export": "Eksportuoti", "Export All Archived Chats": "", "Export All Chats (All Users)": "Eksportuoti visų naudotojų visus pokalbius", "Export chat (.json)": "Eksportuoti pokalbį (.json)", "Export Chats": "Eksportuoti pokalbius", "Export Config to JSON File": "", "Export Functions": "Eksportuoti funkcijas", "Export Models": "Eksportuoti modelius", "Export Presets": "", "Export Prompt Suggestions": "", "Export Prompts": "Eksportuoti užklausas", "Export to CSV": "", "Export Tools": "Eksportuoti įrankius", "Export Users": "", "External": "", "External Document Loader URL required.": "", "External Task Model": "", "External Web Loader API Key": "", "External Web Loader URL": "", "External Web Search API Key": "", "External Web Search URL": "", "Fade Effect for Streaming Text": "", "Failed to add file.": "", "Failed to connect to {{URL}} OpenAPI tool server": "", "Failed to copy link": "", "Failed to create API Key.": "Nepavyko sukurti API rakto", "Failed to delete note": "", "Failed to extract content from the file: {{error}}": "", "Failed to extract content from the file.": "", "Failed to fetch models": "", "Failed to generate title": "", "Failed to load chat preview": "", "Failed to load file content.": "", "Failed to read clipboard contents": "Nepavyko per<PERSON>ityti kopijuoklės", "Failed to save connections": "", "Failed to save models configuration": "", "Failed to update settings": "Nepavyko atnaujinti nustatymų", "Failed to upload file.": "", "Features": "", "Features Permissions": "", "February": "<PERSON><PERSON><PERSON>", "Feedback Details": "", "Feedback History": "", "Feedbacks": "", "Feel free to add specific details": "Galite pridėti specifinių detalių", "File": "Rinkmena", "File added successfully.": "", "File content updated successfully.": "", "File Mode": "Rinkmenų rėžimas", "File not found.": "<PERSON><PERSON><PERSON> ne<PERSON>.", "File removed successfully.": "", "File size should not exceed {{maxSize}} MB.": "", "File Upload": "", "File uploaded successfully": "", "Files": "Rinkmen<PERSON>", "Filter": "", "Filter is now globally disabled": "Filtrai nėra leidžiami globaliai", "Filter is now globally enabled": "Filtrai globaliai leidžiami", "Filters": "Filtrai", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Nepavyko nsutatyti profilio n<PERSON>", "Firecrawl API Base URL": "", "Firecrawl API Key": "", "Floating Quick Actions": "", "Focus chat input": "Fokusuoti žinut<PERSON> įvestį", "Folder deleted successfully": "", "Folder Name": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Folder updated successfully": "", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "Tobulai sekė instruk<PERSON>jas", "Force OCR": "", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "", "Form": "Forma", "Format Lines": "", "Format the lines in the output. Defaults to False. If set to True, the lines will be formatted to detect inline math and styles.": "", "Format your variables using brackets like this:": "", "Forwards system user session credentials to authenticate": "", "Full Context Mode": "", "Function": "", "Function Calling": "", "Function created successfully": "Funkcija sukurta s<PERSON>", "Function deleted successfully": "Funkcija ištrinta sėkmingai", "Function Description": "", "Function ID": "", "Function imported successfully": "", "Function is now globally disabled": "Funkcijos š<PERSON> metu <PERSON>", "Function is now globally enabled": "<PERSON><PERSON><PERSON>", "Function Name": "", "Function updated successfully": "Funkcija atnaujinta s<PERSON>", "Functions": "Funkcijos", "Functions allow arbitrary code execution.": "Funkcijos leidžia nekontroliuojamo kodo vykdymą", "Functions imported successfully": "Funkcijos importuo<PERSON>", "Gemini": "", "Gemini API Config": "", "Gemini API Key is required.": "", "General": "<PERSON><PERSON>", "Generate": "", "Generate an image": "", "Generate Image": "Generuoti paveikslėlį", "Generate prompt pair": "", "Generating search query": "Generuoti paieškos užklausą", "Generating...": "", "Get information on {{name}} in the UI": "", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "Globalu", "Good Response": "<PERSON><PERSON><PERSON>", "Google Drive": "", "Google PSE API Key": "Google PSE API raktas", "Google PSE Engine Id": "Google PSE variklio ID", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "H1": "", "H2": "", "H3": "", "Haptic Feedback": "", "Hello, {{name}}": "<PERSON><PERSON><PERSON>, {{name}}", "Help": "Pagalba", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "Paslėpti", "Hide from Sidebar": "", "Hide Model": "", "High Contrast Mode": "", "Home": "", "Host": "", "How can I help you today?": "<PERSON><PERSON>u Ju<PERSON> ?", "How would you rate this response?": "", "HTML": "", "Hybrid Search": "Hibridinė paieška", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Suprantu veiksmų ir kodo vykdymo rizikas.", "ID": "", "iframe Sandbox Allow Forms": "", "iframe Sandbox Allow Same Origin": "", "Ignite curiosity": "", "Image": "", "Image Compression": "", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "", "Image Generation (Experimental)": "Vaizdų generavimas (eksperimentinis)", "Image Generation Engine": "Vaizdų generavimo variklis", "Image Max Compression Size": "", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "", "Image Prompt Generation Prompt": "", "Image Settings": "Vaizdų nustatymai", "Images": "<PERSON><PERSON><PERSON><PERSON>", "Import": "", "Import Chats": "Import<PERSON><PERSON> poka<PERSON>", "Import Config from JSON File": "", "Import From Link": "", "Import Functions": "I<PERSON>rt<PERSON><PERSON>", "Import Models": "Import<PERSON><PERSON> modelius", "Import Notes": "", "Import Presets": "", "Import Prompt Suggestions": "", "Import Prompts": "Importuoti užklausas", "Import Tools": "I<PERSON>rtuo<PERSON> įrankius", "Include": "", "Include `--api-auth` flag when running stable-diffusion-webui": "Įtraukti `--api-auth` flag when running stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "<PERSON><PERSON><PERSON><PERSON> `--api<PERSON> kai v<PERSON> stable-diffusion-webui", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "", "Info": "Informacija", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "", "Input": "", "Input commands": "Įvesties komandos", "Input Variables": "", "Insert": "", "Insert Follow-Up Prompt to Input": "", "Insert Prompt as Rich Text": "", "Install from Github URL": "Instaliuoti Github nuorodą", "Instant Auto-Send After Voice Transcription": "Siųsti iškart po balso transkripcijos", "Integration": "", "Interface": "Sąsaja", "Invalid file content": "", "Invalid file format.": "", "Invalid JSON file": "", "Invalid JSON format in Additional Config": "", "Invalid Tag": "Neteisinga žyma", "is typing...": "", "Italic": "", "January": "Sausis", "Jina API Key": "", "join our Discord for help.": "prisijunkite prie mūsų Discord.", "JSON": "JSON", "JSON Preview": "JSON peržiūra", "July": "liepa", "June": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Jupyter Auth": "", "Jupyter URL": "", "JWT Expiration": "JWT išėjimas iš <PERSON>", "JWT Token": "JWT žetonas", "Kagi Search API Key": "", "Keep Follow-Up Prompts in Chat": "", "Keep in Sidebar": "", "Key": "", "Keyboard shortcuts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "Knowledge": "<PERSON><PERSON><PERSON>", "Knowledge Access": "", "Knowledge Base": "", "Knowledge created successfully.": "", "Knowledge deleted successfully.": "", "Knowledge Public Sharing": "", "Knowledge reset successfully.": "", "Knowledge updated successfully": "", "Kokoro.js (Browser)": "", "Kokoro.js Dtype": "", "Label": "", "Landing Page Mode": "", "Language": "Kalba", "Language Locales": "", "Last Active": "Paskutinį kartą aktyvus", "Last Modified": "<PERSON><PERSON><PERSON><PERSON>", "Last reply": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "", "Learn More": "", "Learn more about OpenAPI tool servers.": "", "Leave empty for no compression": "", "Leave empty for unlimited": "", "Leave empty to include all models from \"{{url}}\" endpoint": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{url}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "", "Leave model field empty to use the default model.": "", "lexical": "", "License": "", "Lift List": "", "Light": "<PERSON><PERSON><PERSON>", "Listening...": "Klausoma...", "Llama.cpp": "", "LLMs can make mistakes. Verify important information.": "Dideli kalbos modeliai gali klysti. Patikrinkite atsakymų teisingumą.", "Loader": "", "Loading Kokoro.js...": "", "Local": "", "Local Task Model": "", "Location access not allowed": "", "Lost": "", "LTR": "LTR", "Made by Open WebUI Community": "Sukurta OpenWebUI bendruomenės", "Make password visible in the user interface": "", "Make sure to enclose them with": "<PERSON><PERSON><PERSON><PERSON>rin<PERSON><PERSON>, kad įtraukiate viduje:", "Make sure to export a workflow.json file as API format from ComfyUI.": "", "Manage": "<PERSON><PERSON><PERSON><PERSON>", "Manage Direct Connections": "", "Manage Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "<PERSON><PERSON><PERSON><PERSON> procesus", "Manage Tool Servers": "", "March": "<PERSON><PERSON>", "Markdown": "", "Markdown (Header)": "", "Max Speakers": "", "Max Upload Count": "", "Max Upload Size": "", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Daugiausiai trys modeliai gali būti parsisiunčiami vienu metu.", "May": "gegužė", "Memories accessible by LLMs will be shown here.": "Atminitis prieinama kalbos modelio bus rodoma čia.", "Memory": "<PERSON><PERSON><PERSON>", "Memory added successfully": "At<PERSON><PERSON> p<PERSON>", "Memory cleared successfully": "Atmintis ištri<PERSON>k<PERSON>", "Memory deleted successfully": "Atmintis ištri<PERSON>k<PERSON>", "Memory updated successfully": "<PERSON><PERSON><PERSON> at<PERSON>", "Merge Responses": "", "Merged Response": "<PERSON><PERSON><PERSON><PERSON>", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> po nuorodos sukūrimo nebus matomos nuorodos turėtojams. Naudotojai su nuoroda matys žinutes iki nuorodos sukūrimo.", "Microsoft OneDrive": "", "Microsoft OneDrive (personal)": "", "Microsoft OneDrive (work/school)": "", "Mistral OCR": "", "Mistral OCR API Key required.": "", "Model": "", "Model '{{modelName}}' has been successfully downloaded.": "'{{modelName}}' modelis s<PERSON> atsisiųstas.", "Model '{{modelTag}}' is already in queue for downloading.": "Modelis '{{modelTag}}' jau atsisiuntimų eilėje.", "Model {{modelId}} not found": "Modelis {{modelId}} nerastas", "Model {{modelName}} is not vision capable": "Modelis {{modelName}} neturi vaizdo <PERSON>", "Model {{name}} is now {{status}}": "Modelis {{name}} dabar {{status}}", "Model {{name}} is now hidden": "", "Model {{name}} is now visible": "", "Model accepts file inputs": "", "Model accepts image inputs": "", "Model can execute code and perform calculations": "", "Model can generate images based on text prompts": "", "Model can search the web for information": "", "Model created successfully!": "<PERSON>is suku<PERSON>", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Modelio failų sistemos kelias aptiktas. Reikalingas trumpas modelio pavadinimas atnaujinimui.", "Model Filtering": "", "Model ID": "Modelio ID", "Model ID is required.": "", "Model IDs": "", "Model Name": "", "Model Name is required.": "", "Model not selected": "<PERSON><PERSON>", "Model Params": "Modelio parametrai", "Model Permissions": "", "Model unloaded successfully": "", "Model updated successfully": "Modelis atnaujintas sėkmingai", "Model(s) do not support file upload": "", "Modelfile Content": "<PERSON>io failo turinys", "Models": "Model<PERSON><PERSON>", "Models Access": "", "Models configuration saved successfully": "", "Models Public Sharing": "", "Mojeek Search API Key": "", "more": "", "More": "Daugiau", "More Concise": "", "More Options": "", "Name": "Pavadinimas", "Name your knowledge base": "", "Native": "", "New Button": "", "New Chat": "<PERSON><PERSON><PERSON> pokal<PERSON>", "New Folder": "", "New Function": "", "New Note": "", "New Password": "<PERSON><PERSON><PERSON>", "New Tool": "", "new-channel": "", "Next message": "", "No chats found": "", "No chats found for this user.": "", "No chats found.": "", "No content": "", "No content found": "", "No content found in file.": "", "No content to speak": "<PERSON><PERSON><PERSON> t<PERSON>", "No distance available": "", "No feedbacks found": "", "No file selected": "Nėra pasirinktų dokumentų", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No inference engine with management support found": "", "No knowledge found": "", "No memories to clear": "", "No model IDs": "", "No models found": "", "No models selected": "", "No Notes": "", "No results found": "Rezultatų nerasta", "No search query generated": "Paieškos užklausa nesugeneruota", "No source available": "Šaltinių nerasta", "No users were found.": "", "No valves to update": "<PERSON><PERSON>ra <PERSON> įeičių", "None": "Nėra", "Not factually correct": "Faktiškai netikslu", "Not helpful": "", "Note deleted successfully": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Jei turite minimalų įvertį, pai<PERSON>š<PERSON> gražins tik tą informaciją, kuri viršyje šį įvertį", "Notes": "", "Notification Sound": "", "Notification Webhook": "", "Notifications": "Pranešimai", "November": "<PERSON><PERSON><PERSON><PERSON>", "OAuth ID": "OAuth ID", "October": "spalis", "Off": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Okay, Let's Go!": "<PERSON><PERSON><PERSON>, važiuojam!", "OLED Dark": "OLED tamsus", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API settings updated": "", "Ollama Version": "Ollama versija", "On": "Aktyvuota", "OneDrive": "", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>, skaičiai ir brūkšneliai.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only markdown files are allowed": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Regis nuoroda nevalidi. Prašau patikrtinkite ir pabandykite iš naujo.", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Naudojate nepal<PERSON>ą (front-end) web ui rėžimą. Prašau serviruokite WebUI iš back-end", "Open file": "", "Open in full screen": "", "Open modal to configure connection": "", "Open Modal To Manage Floating Quick Actions": "", "Open new chat": "Atverti naują pokalbį", "Open Sidebar": "", "Open User Profile Menu": "", "Open WebUI can use tools provided by any OpenAPI server.": "", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Tortue Chat versija per sena. <PERSON><PERSON><PERSON> (v{{REQUIRED_VERSION}}) versija.", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "Open AI API nustatymai", "OpenAI API Key is required.": "OpenAI API raktas būtinas.", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "OpenAI API nuoroda ir raktas būtini", "openapi.json URL or Path": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "arba", "Ordered List": "", "Organize your users": "", "Other": "<PERSON><PERSON>", "OUTPUT": "", "Output format": "", "Output Format": "", "Overview": "", "page": "", "Paginate": "", "Parameters": "", "Password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Passwords do not match.": "", "Paste Large Text as File": "", "PDF document (.pdf)": "PDF dokumentas (.pdf)", "PDF Extract Images (OCR)": "PDF paveikslėlių skaitymas (OCR)", "pending": "lauki<PERSON>", "Pending": "", "Pending User Overlay Content": "", "Pending User Overlay Title": "", "Permission denied when accessing media devices": "Leidimas atmestas bandant prisijungti prie medijos įrenginių", "Permission denied when accessing microphone": "Mikrofono leid<PERSON> atmestas", "Permission denied when accessing microphone: {{error}}": "Leidimas naudoti mikrofoną atmestas: {{error}}", "Permissions": "", "Perplexity API Key": "", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "Personalizacija", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pinned": "Įsmeigta", "Pioneer insights": "", "Pipe": "", "Pipeline deleted successfully": "Procesas ištrinta<PERSON>", "Pipeline downloaded successfully": "Procesas atsisiųstas sėkmingai", "Pipelines": "Procesai", "Pipelines Not Detected": "Procesai neaptikti", "Pipelines Valves": "Procesų įeitys", "Plain text (.md)": "", "Plain text (.txt)": "<PERSON><PERSON><PERSON> (.txt)", "Playground": "Eksperimentavimo erdv<PERSON>", "Playwright Timeout (ms)": "", "Playwright WebSocket URL": "", "Please carefully review the following warnings:": "Peržiūrėkite šiuos <PERSON>sp<PERSON>mus:", "Please do not close the settings page while loading the model.": "", "Please enter a prompt": "", "Please enter a valid path": "", "Please enter a valid URL": "", "Please fill in all fields.": "", "Please select a model first.": "", "Please select a model.": "", "Please select a reason": "", "Please wait until all files are uploaded.": "", "Port": "", "Positive attitude": "<PERSON><PERSON><PERSON><PERSON><PERSON> el<PERSON>", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Prevent file creation": "", "Preview": "", "Previous 30 days": "Paskutinės 30 dienų", "Previous 7 days": "Paskutinės 7 dienos", "Previous message": "", "Private": "", "Profile Image": "<PERSON><PERSON><PERSON>", "Prompt": "", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Užklausa (pvz. supaprastink šį laišką)", "Prompt Autocompletion": "", "Prompt Content": "Užklausos turinys", "Prompt created successfully": "", "Prompt suggestions": "Užklausų pavyzdžiai", "Prompt updated successfully": "", "Prompts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Prompts Access": "", "Prompts Public Sharing": "", "Public": "", "Pull \"{{searchValue}}\" from Ollama.com": "<PERSON><PERSON><PERSON> \"{{searchValue}}\" iš <PERSON>ma.com", "Pull a model from Ollama.com": "Gauti modelį iš <PERSON>ma.com", "Query Generation Prompt": "", "Quick Actions": "", "RAG Template": "RAG šablonas", "Rating": "", "Re-rank models by topic similarity": "", "Read": "", "Read Aloud": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>i", "Reason": "", "Reasoning Effort": "", "Record": "", "Record voice": "Įrašyti balsą", "Redirecting you to Open WebUI Community": "<PERSON><PERSON><PERSON> į OpenWebUI bendruomenę", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Vadinkite save <PERSON><PERSON><PERSON><PERSON> (pvz. Naudotojas mokosi prancūzų kalbos)", "References from": "", "Refused when it shouldn't have": "Atmesta kai neturėtų būti atmesta", "Regenerate": "Generuoti iš naujo", "Reindex": "", "Reindex Knowledge Base Vectors": "", "Release Notes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Releases": "", "Relevance": "", "Relevance Threshold": "", "Remember Dismissal": "", "Remove": "<PERSON><PERSON><PERSON><PERSON>", "Remove {{MODELID}} from list.": "", "Remove file": "", "Remove File": "", "Remove image": "", "Remove Model": "<PERSON><PERSON><PERSON><PERSON> modelį", "Remove this tag from list": "", "Rename": "<PERSON><PERSON><PERSON><PERSON>", "Reorder Models": "", "Reply in Thread": "", "Reranking Engine": "", "Reranking Model": "Reranking modelis", "Reset": "Atkurti", "Reset All Models": "", "Reset Upload Directory": "Atkurti įkėlimų direktoiją", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Naršyklė neleidžia siųsti pranešimų", "Response splitting": "", "Response Watermark": "", "Result": "", "Retrieval": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "Rolė", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "", "Running": "<PERSON><PERSON><PERSON><PERSON>", "Save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "Save & Create": "Išsaugoti ir sukurti", "Save & Update": "<PERSON>š<PERSON><PERSON><PERSON><PERSON> ir at<PERSON>", "Save As Copy": "", "Save Tag": "Išsaugoti žymą", "Saved": "", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Pokalbių saugojimas naršyklėje nebegalimas.", "Scroll On Branch Change": "", "Search": "Ieškoti", "Search a model": "Ieškoti modelio", "Search Base": "", "Search Chats": "Ieškoti pokalbiuose", "Search Collection": "", "Search Filters": "", "search for archived chats": "", "search for folders": "", "search for pinned chats": "", "search for shared chats": "", "search for tags": "", "Search Functions": "Ieškoti funkcijų", "Search In Models": "", "Search Knowledge": "", "Search Models": "Ieškoti modelių", "Search Notes": "", "Search options": "", "Search Prompts": "Ieškoti užklausų", "Search Result Count": "Paieškos rezultatų skaičius", "Search the internet": "", "Search Tools": "Paieškos įrankiai", "SearchApi API Key": "", "SearchApi Engine": "", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "<PERSON><PERSON><PERSON><PERSON><PERSON> \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "", "Searching the web...": "", "Searxng Query URL": "Searxng užklausos URL", "See readme.md for instructions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> readme.md papildomoms instrukcijoms", "See what's new": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Seed": "Sėkla", "Select a base model": "Pasirinkite bazinį modelį", "Select a conversation to preview": "", "Select a engine": "Pasirinkite variklį", "Select a function": "Pasirinkite funkciją", "Select a group": "", "Select a model": "Pa<PERSON>ink<PERSON> modelį", "Select a pipeline": "Pasirinkite procesą", "Select a pipeline url": "Pasirinkite proceso nuorodą", "Select a tool": "Pasirinkite įrankį", "Select an auth method": "", "Select an Ollama instance": "", "Select Engine": "", "Select Knowledge": "", "Select only one model to call": "Pasirinkite vieną modelį", "Selected model(s) do not support image inputs": "Pasirinkti modeliai nepalaiko vaizdinių užklausų", "semantic": "", "Semantic distance to query": "", "Send": "Si<PERSON>sti", "Send a Message": "Siųsti žinutę", "Send message": "Siųsti žinutę", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "rugsė<PERSON>s", "SerpApi API Key": "", "SerpApi Engine": "", "Serper API Key": "Serper API raktas", "Serply API Key": "Serply API raktas", "Serpstack API Key": "Serpstach API raktas", "Server connection verified": "Serverio sujungimas pat<PERSON>", "Set as default": "Nustatyti numatytąjį", "Set CFG Scale": "", "Set Default Model": "Nustatyti numatytąjį modelį", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "Nustatyti embedding modelį", "Set Image Size": "Nustatyti paveikslėlių dydį", "Set reranking model (e.g. {{model}})": "<PERSON>ust<PERSON><PERSON><PERSON> reranking modelį", "Set Sampler": "", "Set Scheduler": "", "Set Steps": "<PERSON><PERSON><PERSON><PERSON> etapus", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "Set whisper model": "", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets how far back for the model to look back to prevent repetition.": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "", "Sets the size of the context window used to generate the next token.": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "Nustatymai", "Settings saved successfully!": "Parametrai sėkmingai išsaugoti!", "Share": "<PERSON><PERSON><PERSON>", "Share Chat": "<PERSON><PERSON><PERSON> p<PERSON>", "Share to Open WebUI Community": "Dalintis su OpenWebUI bendruomene", "Sharing Permissions": "", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "<PERSON><PERSON><PERSON>", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "<PERSON><PERSON><PERSON><PERSON>us duomenis lauki<PERSON> paskyros pat<PERSON>", "Show All": "", "Show Formatting Toolbar": "", "Show image preview": "", "Show Less": "", "Show Model": "", "Show shortcuts": "<PERSON><PERSON><PERSON> trumpinius", "Show your support!": "Palaikykite", "Showcased creativity": "Kūrybingų užklausų paroda", "Sign in": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "<PERSON>si<PERSON><PERSON><PERSON>", "Sign up": "Sukurti paskyrą", "Sign up to {{WEBUI_NAME}}": "", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to False.": "", "Signing in to {{WEBUI_NAME}}": "", "Sink List": "", "sk-1234": "", "Skip Cache": "", "Skip the cache and re-run the inference. Defaults to False.": "", "Sougou Search API sID": "", "Sougou Search API SK": "", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Speech Playback Speed": "", "Speech recognition error: {{error}}": "<PERSON>lso <PERSON> problema: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "<PERSON><PERSON>o <PERSON>", "Stop": "", "Stop Generating": "", "Stop Sequence": "<PERSON><PERSON>", "Stream Chat Response": "", "Stream Delta Chunk Size": "", "Strikethrough": "", "Strip Existing OCR": "", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "", "STT Model": "STT modelis", "STT Settings": "STT nustatymai", "Stylized PDF Export": "", "Subtitle (e.g. about the Roman Empire)": "Subtitras", "Success": "Sėkmingai", "Successfully updated.": "Sėkmingai atnaujinta.", "Suggest a change": "", "Suggested": "<PERSON><PERSON><PERSON><PERSON>", "Support": "<PERSON><PERSON><PERSON><PERSON>", "Support this plugin:": "Palaikykite šitą modulį", "Supported MIME Types": "", "Sync directory": "", "System": "Sistema", "System Instructions": "", "System Prompt": "Sistemos užklausa", "Tags": "", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "", "Talk to model": "", "Tap to interrupt": "Paspauskite norėdami <PERSON>", "Task List": "", "Task Model": "", "Tasks": "", "Tavily API Key": "Tavily API raktas", "Tavily Extract Depth": "", "Tell us more:": "Papasa<PERSON><PERSON><PERSON>", "Temperature": "Temperat<PERSON>ra", "Temporary Chat": "", "Text Splitter": "", "Text-to-Speech": "", "Text-to-Speech Engine": "<PERSON><PERSON><PERSON> modelis", "Thanks for your feedback!": "<PERSON>č<PERSON><PERSON> už atsiliepimus", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Šis modulis k<PERSON> sa<PERSON>. Palaikykite jų darbus finansiškai arba prisidėdami kodu.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Rezultatas turėtų būti tarp 0.0 (0%) ir 1.0 (100%)", "The stream delta chunk size for the model. Increasing the chunk size will make the model respond with larger pieces of text at once.": "", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "", "The Weight of BM25 Hybrid Search. 0 more lexical, 1 more semantic. Default 0.5": "", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "<PERSON><PERSON>", "Thinking...": "Mąsto...", "This action cannot be undone. Do you wish to continue?": "<PERSON><PERSON> veik<PERSON> negali būti <PERSON>. Ar norite tęsti?", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "", "This chat won't appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "<PERSON>, kad <PERSON> pokalbiai saugiai saugojami duomenų bazėje. Ačiū!", "This feature is experimental and may be modified or discontinued without notice.": "", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Tai eksperimentinė funkcija ir gali veikti nevisada.", "This model is not publicly available. Please select another model.": "", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "<PERSON>", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "", "Thorough explanation": "<PERSON><PERSON><PERSON> p<PERSON>", "Thought for {{DURATION}}": "", "Thought for {{DURATION}} seconds": "", "Thought for less than a second": "", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "Reiklainga Tika serverio nuorodą", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Jei norite pakeisti keletą kintamųjų vieną po kitos, spauskite Tab", "Title": "Pavadinimas", "Title (e.g. Tell me a fun fact)": "Pavadinimas", "Title Auto-Generation": "Automatinis pavadinimų generavimas", "Title cannot be an empty string.": "Pavadinimas negali bū<PERSON>", "Title Generation": "", "Title Generation Prompt": "Pavadinimo generavimo <PERSON>", "TLS": "", "To access the available model names for downloading,": "<PERSON>, kad prieiti prie galimų parsisiųsti modelių", "To access the GGUF models available for downloading,": "<PERSON>, kad prieiti prie galimų parsisiųsti GGUF,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Norėdami prieiti prie programos, susisiekite su administratoriumi, <PERSON><PERSON><PERSON>.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To learn more about powerful prompt variables, click here": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "Norėdami pasirinkti veiksmus, pirmiausia pridėkite juos funkcijų nuostatuose", "To select filters here, add them to the \"Functions\" workspace first.": "Norėdami pasirinkti filtrus, pirmiausia pridėkite juos funkcijų nuostatuose", "To select toolkits here, add them to the \"Tools\" workspace first.": "Norėdami pasirinkti įrankius, pirmiausia pridėkite juos prie įrankių nuostatuose", "Toast notifications for new updates": "", "Today": "Šiandien", "Toggle search": "", "Toggle settings": "Atverti/užverti parametrus", "Toggle sidebar": "Atverti/užverti šoninį meniu", "Toggle whether current connection is active.": "", "Token": "", "Too verbose": "", "Tool created successfully": "Įrankis sukurtas s<PERSON>", "Tool deleted successfully": "Įrankis i<PERSON><PERSON><PERSON><PERSON>", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "Įrankis importuotas sėk<PERSON>ai", "Tool Name": "", "Tool Servers": "", "Tool updated successfully": "Įrankis atnaujintas sėkmingai", "Tools": "Įrankiai", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "Įrankiai gali naudoti funkcijas ir vykdyti kodą", "Tools Function Calling Prompt": "", "Tools have a function calling system that allows arbitrary code execution.": "Įrankiai gali naudoti funkcijas ir leisti vykdyti kodą", "Tools Public Sharing": "", "Top K": "Top K", "Top K Reranker": "", "Transformers": "", "Trouble accessing Ollama?": "Problemos prieinant prie Ollama?", "Trust Proxy Environment": "", "Try Again": "", "TTS Model": "TTS modelis", "TTS Settings": "TTS parametrai", "TTS Voice": "TTS balsas", "Type": "Tipas", "Type Hugging Face Resolve (Download) URL": "Įveskite Hugging Face Resolve nuorodą", "Uh-oh! There was an issue with the response.": "", "UI": "sąsaja", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Underline": "", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "", "Unpin": "<PERSON>semi<PERSON><PERSON>", "Unravel secrets": "", "Unsupported file type.": "", "Untagged": "", "Untitled": "", "Update": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Update and Copy Link": "Atnaujinti ir kopijuoti nuorodą", "Update for the latest features and improvements.": "", "Update password": "<PERSON><PERSON><PERSON><PERSON><PERSON> slaptažodį", "Updated": "", "Updated at": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Updated At": "", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "", "Upload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Upload a GGUF model": "Parsisiųsti GGUF modelį", "Upload Audio": "", "Upload directory": "", "Upload files": "", "Upload Files": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Upload Pipeline": "<PERSON><PERSON><PERSON><PERSON><PERSON> procesą", "Upload Progress": "Įkėlimo progresas", "URL": "", "URL Mode": "URL režimas", "Usage": "", "Use '#' in the prompt input to load and include your knowledge.": "", "Use Gravatar": "<PERSON><PERSON><PERSON>", "Use groups to group your users and assign permissions.": "", "Use Initials": "<PERSON><PERSON><PERSON><PERSON> in<PERSON>", "Use LLM": "", "Use no proxy to fetch page contents.": "", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "", "user": "naudotojas", "User": "", "User Groups": "", "User location successfully retrieved.": "Naudotojo vieta s<PERSON>k<PERSON> gauta", "User menu": "", "User Webhooks": "", "Username": "", "Users": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Using Entire Document": "", "Using Focused Retrieval": "", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Valid time units:": "Teising<PERSON>s laiko vienetai :", "Valves": "Įeitys", "Valves updated": "Įeitys atnaujintos", "Valves updated successfully": "Įeitys at<PERSON><PERSON><PERSON><PERSON>", "variable": "kintamasis", "Verify Connection": "", "Verify SSL Certificate": "", "Version": "<PERSON><PERSON><PERSON>", "Version {{selectedVersion}} of {{totalVersions}}": "", "View Replies": "", "View Result from **{{NAME}}**": "", "Visibility": "", "Vision": "", "Voice": "Balsas", "Voice Input": "", "Voice mode": "", "Warning": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Warning:": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "<PERSON>i pakeisite embedding modelį, turėsite reimportuoti visus dokumentus", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "", "Web": "Web", "Web API": "Web API", "Web Loader Engine": "", "Web Search": "Web paieška", "Web Search Engine": "Web paieškos variklis", "Web Search in Chat": "", "Web Search Query Generation": "", "Webhook URL": "Webhook nuoroda", "WebUI Settings": "WebUI parametrai", "WebUI URL": "", "WebUI will make requests to \"{{url}}\"": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "What are you trying to achieve?": "", "What are you working on?": "", "What's New in": "<PERSON><PERSON>", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "", "Whisper (Local)": "Whisper (lokalus)", "Why?": "", "Widescreen Mode": "Plataus ekrano rėžimas", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "", "Workspace": "<PERSON><PERSON><PERSON><PERSON>", "Workspace Permissions": "", "Write": "", "Write a prompt suggestion (e.g. Who are you?)": "Parašykite užklausą", "Write a summary in 50 words that summarizes [topic or keyword].": "Parašyk santrumpą trumpesnę nei 50 žodžių šiam tekstui: [tekstas]", "Write something...": "", "Yacy Instance URL": "", "Yacy Password": "", "Yacy Username": "", "Yesterday": "<PERSON><PERSON><PERSON>", "You": "<PERSON><PERSON><PERSON>", "You are currently using a trial license. Please contact support to upgrade your license.": "", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "G<PERSON>te pagerinti modelių darbą suteikdami jiems atminties funkcionalumą.", "You cannot upload an empty file.": "", "You do not have permission to upload files.": "", "You have no archived conversations.": "Jūs neturite archyvuotų pokalbių", "You have shared this chat": "Pasidalinote šiuo pokalbiu", "You're a helpful assistant.": "Esi asistentas.", "You're now logged in.": "Esate prisijun<PERSON>ę.", "Your account status is currently pending activation.": "Jūsų paskyra laukia administratoriaus pat<PERSON>.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "Jūsų finansinis prisidėjimas tiesiogiai keliaus modulio kū<PERSON>.", "Youtube": "Youtube", "Youtube Language": "", "Youtube Proxy URL": ""}