{"-1 for no limit, or a positive integer for a specific limit": "-1 piirangu puudumisel või positiivne täisarv konkreetse piirangu jaoks", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' või '-1' aegumiseta.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(nt `sh webui.sh --api --api-auth kasutajanimi_parool`)", "(e.g. `sh webui.sh --api`)": "(nt `sh webui.sh --api`)", "(latest)": "(uusim)", "(leave blank for to use commercial endpoint)": "", "[Last] dddd [at] h:mm A": "", "[Today at] h:mm A": "", "[Yesterday at] h:mm A": "", "{{ models }}": "{{ mud<PERSON>d }}", "{{COUNT}} Available Tools": "", "{{COUNT}} characters": "", "{{COUNT}} hidden lines": "{{COUNT}} pei<PERSON><PERSON> rida", "{{COUNT}} Replies": "{{COUNT}} vastust", "{{COUNT}} words": "", "{{user}}'s Chats": "{{user}} vestlused", "{{webUIName}} Backend Required": "{{webUIName}} taustaserver on vajalik", "*Prompt node ID(s) are required for image generation": "*Vihje sõlme ID(d) on piltide genereerimiseks vajalikud", "A new version (v{{LATEST_VERSION}}) is now available.": "Uus versioon (v{{LATEST_VERSION}}) on saadaval.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Ülesande mudelit kasutatakse selliste toimingute jaoks nagu vestluste pealkirjade ja veebiotsingu päringute genereerimine", "a user": "<PERSON><PERSON><PERSON><PERSON>", "About": "Teave", "Accept autocomplete generation / Jump to prompt variable": "Nõustu automaattäitmisega / Liigu vihjete muutujale", "Access": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Access Control": "Juurdepääsu kontroll", "Accessible to all users": "Kättesaadav kõigile ka<PERSON>e", "Account": "Ko<PERSON>", "Account Activation Pending": "Konto aktiveerimine ootel", "Accurate information": "Täpne informatsioon", "Action": "", "Actions": "<PERSON><PERSON><PERSON><PERSON>", "Activate": "Aktiveeri", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Aktiveeri see käsk, trükkides \"/{{COMMAND}}\" vestluse sisendritta.", "Active Users": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Add": "<PERSON>", "Add a model ID": "<PERSON>", "Add a short description about what this model does": "<PERSON> l<PERSON> k<PERSON>, mida see mudel teeb", "Add a tag": "Lisa silt", "Add Arena Model": "<PERSON>", "Add Connection": "<PERSON>", "Add Content": "<PERSON>", "Add content here": "<PERSON> siia sisu", "Add Custom Parameter": "", "Add custom prompt": "<PERSON> k<PERSON> v<PERSON>", "Add Details": "", "Add Files": "<PERSON> faile", "Add Group": "<PERSON> grupp", "Add Memory": "<PERSON>", "Add Model": "<PERSON>el", "Add Reaction": "<PERSON>", "Add Tag": "Lisa silt", "Add Tags": "<PERSON> silte", "Add text content": "<PERSON>", "Add User": "<PERSON>", "Add User Group": "<PERSON>", "Additional Config": "", "Additional configuration options for marker. This should be a JSON string with key-value pairs. For example, '{\"key\": \"value\"}'. Supported keys include: disable_links, keep_pageheader_in_output, keep_pagefooter_in_output, filter_blank_pages, drop_repeated_text, layout_coverage_threshold, merge_threshold, height_tolerance, gap_threshold, image_threshold, min_line_length, level_count, default_level": "", "Adjusting these settings will apply changes universally to all users.": "Nende seadete kohandamine rakendab muudatused universaalselt kõigile kasu<PERSON>.", "admin": "admin", "Admin": "Administraator", "Admin Panel": "Administraatori paneel", "Admin Settings": "Administraatori seaded", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Administraatoritel on alati juurdepääs kõigile tööriistadele; kasutajatele tuleb tööriistad määrata mudeli põhiselt tööruumis.", "Advanced Parameters": "<PERSON><PERSON><PERSON><PERSON> para<PERSON>", "Advanced Params": "<PERSON><PERSON><PERSON><PERSON> para<PERSON>", "AI": "", "All": "Kõik", "All Documents": "Kõik dokumendid", "All models deleted successfully": "<PERSON><PERSON><PERSON> mudelid edukalt kustutatud", "Allow Call": "", "Allow Chat Controls": "Luba vestluse kontrollnupud", "Allow Chat Delete": "Luba vestluse kustutamine", "Allow Chat Deletion": "Luba vestluse kustutamine", "Allow Chat Edit": "<PERSON><PERSON> vestluse muutmine", "Allow Chat Export": "", "Allow Chat Params": "", "Allow Chat Share": "", "Allow Chat System Prompt": "", "Allow Chat Valves": "", "Allow File Upload": "Luba failide üleslaadimine", "Allow Multiple Models in Chat": "", "Allow non-local voices": "<PERSON><PERSON> mitte-lo<PERSON><PERSON><PERSON> hääled", "Allow Speech to Text": "", "Allow Temporary Chat": "<PERSON><PERSON> ajutine vestlus", "Allow Text to Speech": "", "Allow User Location": "<PERSON><PERSON> ka<PERSON>ta<PERSON>", "Allow Voice Interruption in Call": "Lu<PERSON> hääle katkestamine kõnes", "Allowed Endpoints": "<PERSON><PERSON><PERSON> l<PERSON>-punktid", "Allowed File Extensions": "", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "", "Already have an account?": "Kas teil on juba konto?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "Alternatiiv top_p-le ja e<PERSON><PERSON><PERSON>rk on tagada kvaliteedi ja mitmekesisuse tasakaal. Parameeter p esindab minimaalset tõenäosust tokeni arvesse võtmiseks, võrreldes kõige tõenäolisema tokeni tõenäosusega. Näiteks p=0.05 korral, kui kõige tõenäolisema tokeni tõenäosus on 0.9, filtreeritakse välja logitid väärtusega alla 0.045.", "Always": "<PERSON><PERSON>", "Always Collapse Code Blocks": "", "Always Expand Details": "", "Always Play Notification Sound": "", "Amazing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "an assistant": "assistent", "Analytics": "", "Analyzed": "Analüüsitud", "Analyzing...": "Analüüsimine...", "and": "ja", "and {{COUNT}} more": "ja veel {{COUNT}}", "and create a new shared link.": "ja looge uus jagatud link.", "Android": "", "API": "", "API Base URL": "API baas-URL", "API Base URL for Datalab Marker service. Defaults to: https://www.datalab.to/api/v1/marker": "", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "API võti", "API Key created.": "API võti loodud.", "API Key Endpoint Restrictions": "API võtme lõpp-punkti p<PERSON>ud", "API keys": "API võtmed", "API Version": "", "Application DN": "Rakenduse DN", "Application DN Password": "Rakenduse DN parool", "applies to all users with the \"user\" role": "k<PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON> kasu<PERSON> \"kasutaja\" rolliga", "April": "Aprill", "Archive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Archive All Chats": "Arhiveeri kõik vestlused", "Archived Chats": "Arhiveeritud vestlused", "archived-chat-export": "arhiveeritud-vestluste-eksport", "Are you sure you want to clear all memories? This action cannot be undone.": "<PERSON><PERSON> olete kindel, et soovite kustutada kõik mälestused? Seda toimingut ei saa tagasi võtta.", "Are you sure you want to delete this channel?": "Kas olete kindel, et soovite selle kanali kustutada?", "Are you sure you want to delete this message?": "Kas olete kindel, et soovite selle sõnumi kustutada?", "Are you sure you want to unarchive all archived chats?": "<PERSON><PERSON> o<PERSON>e kindel, et soovite kõik arhiveeritud vestlused arhiivist eemaldada?", "Are you sure?": "Kas olete kindel?", "Arena Models": "<PERSON><PERSON>", "Artifacts": "Tek<PERSON><PERSON> objektid", "Ask": "<PERSON><PERSON><PERSON>", "Ask a question": "<PERSON><PERSON><PERSON>", "Assistant": "Assistent", "Attach file from knowledge": "Lisa fail tead<PERSON>e baasist", "Attention to detail": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Attribute for Mail": "E-posti atribuut", "Attribute for Username": "<PERSON><PERSON><PERSON><PERSON><PERSON> atribuut", "Audio": "<PERSON><PERSON>", "August": "August", "Auth": "", "Authenticate": "<PERSON><PERSON><PERSON>", "Authentication": "Autentimine", "Auto": "", "Auto-Copy Response to Clipboard": "Kopeeri vastus automaatselt lõikelauale", "Auto-playback response": "Mängi vastus automaatselt", "Autocomplete Generation": "Automaattäitmise genereerimine", "Autocomplete Generation Input Max Length": "Automaattäitmise genereerimise sisendi maksimaalne pikkus", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111 API autentimise string", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 baas-URL", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 baas-URL on nõutav.", "Available list": "Saadaolevate nimekiri", "Available Tools": "", "available!": "saadaval!", "Awful": "<PERSON><PERSON><PERSON><PERSON>", "Azure AI Speech": "Azure AI Kõne", "Azure Region": "Azure regioon", "Back": "Tagasi", "Bad Response": "<PERSON><PERSON> vastus", "Banners": "Bännerid", "Base Model (From)": "<PERSON><PERSON> mudel (Allikas)", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "", "before": "enne", "Being lazy": "Lai<PERSON><PERSON><PERSON>", "Beta": "<PERSON><PERSON>", "Bing Search V7 Endpoint": "Bing Search V7 lõpp-punkt", "Bing Search V7 Subscription Key": "Bing Search V7 tellimuse võti", "BM25 Weight": "", "Bocha Search API Key": "Bocha otsingu API võti", "Bold": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "Konkreetsete tokenite võimendamine või karistamine piiratud vastuste jaoks. Kallutatuse väärtused piiratakse vahemikku -100 kuni 100 (kaasa arvatud). (Vaikimisi: puudub)", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "", "Brave Search API Key": "Brave Search API võti", "Bullet List": "", "Button ID": "", "Button Label": "", "Button Prompt": "", "By {{name}}": "Autor: {{name}}", "Bypass Embedding and Retrieval": "Möödaminek sisestamisest ja taastami<PERSON>t", "Bypass Web Loader": "", "Cache Base Model List": "", "Calendar": "<PERSON><PERSON><PERSON>", "Call": "K<PERSON>ne", "Call feature is not supported when using Web STT engine": "Kõnefunktsioon ei ole Web STT mootorit kasutades toetatud", "Camera": "Ka<PERSON><PERSON>", "Cancel": "<PERSON><PERSON><PERSON><PERSON>", "Capabilities": "Võimekused", "Capture": "Jäädvusta", "Capture Audio": "", "Certificate Path": "<PERSON><PERSON><PERSON><PERSON><PERSON> tee", "Change Password": "<PERSON><PERSON> parooli", "Channel Name": "<PERSON><PERSON><PERSON> nimi", "Channels": "<PERSON><PERSON><PERSON>", "Character": "<PERSON><PERSON><PERSON>", "Character limit for autocomplete generation input": "Märkide piirang automaattäitmise genereerimise sisendile", "Chart new frontiers": "Kaardista uusi piire", "Chat": "Vestlus", "Chat Background Image": "Vestluse taustapilt", "Chat Bubble UI": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Chat Controls": "Vestlus<PERSON> juht<PERSON>d", "Chat direction": "Vestluse suund", "Chat ID": "", "Chat Overview": "Vestluse ülevaade", "Chat Permissions": "Vestlus<PERSON> õigused", "Chat Tags Auto-Generation": "Vestluse siltide automaatnegeneerimine", "Chats": "Vestlused", "Check Again": "<PERSON><PERSON><PERSON><PERSON>i", "Check for updates": "<PERSON><PERSON><PERSON><PERSON>i", "Checking for updates...": "Uuenduste kontrollimine...", "Choose a model before saving...": "Valige mudel enne salvestamist...", "Chunk Overlap": "Tükkide ülekate", "Chunk Size": "<PERSON><PERSON><PERSON> su<PERSON>", "Ciphers": "<PERSON><PERSON><PERSON>", "Citation": "Viide", "Citations": "", "Clear memory": "<PERSON><PERSON><PERSON><PERSON><PERSON> mälu", "Clear Memory": "<PERSON><PERSON><PERSON><PERSON><PERSON> mälu", "click here": "klõpsake siia", "Click here for filter guides.": "Filtri juhiste jaoks klõpsake siia.", "Click here for help.": "Abi saamiseks klõpsake siia.", "Click here to": "Klõ<PERSON><PERSON>, et", "Click here to download user import template file.": "Klõpsake siia kasutajate importimise mallifaili allalaadimiseks.", "Click here to learn more about faster-whisper and see the available models.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, et teada saada rohkem faster-whisper kohta ja näha saadaolevaid mudeleid.", "Click here to see available models.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, et näha saadaolevaid mudeleid.", "Click here to select": "Klõpsake siia valimiseks", "Click here to select a csv file.": "Klõpsake siia csv-faili valimiseks.", "Click here to select a py file.": "Klõpsake siia py-faili valimiseks.", "Click here to upload a workflow.json file.": "Klõpsake siia workflow.json faili üleslaadimiseks.", "click here.": "klõpsake siia.", "Click on the user role button to change a user's role.": "Kasutaja rolli muutmiseks klõpsake kasutaja rolli nuppu.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Lõikelaua kirjutamisõigust ei antud. Kontrollige oma brauseri seadeid, et anda vajalik juurdepääs.", "Clone": "<PERSON><PERSON><PERSON>", "Clone Chat": "<PERSON><PERSON><PERSON>", "Clone of {{TITLE}}": "{{TITLE}} koopia", "Close": "Sulge", "Close Banner": "", "Close Configure Connection Modal": "", "Close modal": "", "Close settings modal": "", "Close Sidebar": "", "Code Block": "", "Code execution": "<PERSON><PERSON><PERSON> t<PERSON>", "Code Execution": "<PERSON><PERSON><PERSON> t<PERSON>", "Code Execution Engine": "<PERSON><PERSON><PERSON> täit<PERSON> mootor", "Code Execution Timeout": "Ko<PERSON>i täitmise aegumine", "Code formatted successfully": "<PERSON><PERSON> vorm<PERSON>d edukalt", "Code Interpreter": "<PERSON><PERSON><PERSON> interpretaator", "Code Interpreter Engine": "<PERSON><PERSON><PERSON> interpretaatori mootor", "Code Interpreter Prompt Template": "Koodi interpretaatori vihje mall", "Collapse": "<PERSON><PERSON><PERSON>", "Collection": "<PERSON><PERSON>", "Color": "<PERSON><PERSON><PERSON><PERSON>", "ComfyUI": "ComfyUI", "ComfyUI API Key": "ComfyUI API võti", "ComfyUI Base URL": "ComfyUI baas-URL", "ComfyUI Base URL is required.": "ComfyUI baas-URL on nõutav.", "ComfyUI Workflow": "ComfyUI töövoog", "ComfyUI Workflow Nodes": "ComfyUI töövoo sõlmed", "Command": "Käsk", "Comment": "", "Completions": "L<PERSON>pet<PERSON><PERSON>", "Compress Images in Channels": "", "Concurrent Requests": "<PERSON><PERSON><PERSON><PERSON>", "Configure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Confirm": "<PERSON><PERSON><PERSON>", "Confirm Password": "<PERSON><PERSON><PERSON>", "Confirm your action": "<PERSON><PERSON><PERSON> oma toiming", "Confirm your new password": "<PERSON><PERSON><PERSON> oma uus parool", "Confirm Your Password": "", "Connect to your own OpenAI compatible API endpoints.": "Ühendu oma OpenAI-ga ühilduvate API lõpp-punktidega.", "Connect to your own OpenAPI compatible external tool servers.": "", "Connection failed": "", "Connection successful": "", "Connection Type": "", "Connections": "<PERSON><PERSON><PERSON>", "Connections saved successfully": "", "Connections settings updated": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "Piirab arutluse pingutust arutlusvõimelistele mudelitele. Kohaldatav ainult konkreetsete pakkujate arutlusmudelitele, mis toetavad arutluspingutust.", "Contact Admin for WebUI Access": "Võtke WebUI juurdepääsu saamiseks ühendust administraatoriga", "Content": "Si<PERSON>", "Content Extraction Engine": "Sisu ekstraheerimise mootor", "Continue Response": "Jätka <PERSON>", "Continue with {{provider}}": "Jätka {{provider}}-ga", "Continue with Email": "Jätka e-postiga", "Continue with LDAP": "Jätka LDAP-ga", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "<PERSON><PERSON><PERSON><PERSON>, kuidas sõnumitekst on jagatud TTS-päringute jaoks. '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>' jagab la<PERSON>, 'l<PERSON><PERSON><PERSON>' jagab lõikudeks ja 'puudub' hoiab sõnumi ühe stringina.", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "Kontrollige tokeni järjestuste kordumist genereeritud tekstis. Kõrgem väärtus (nt 1,5) karistab kordusi tugevamalt, samas kui madalam väärtus (nt 1,1) on leebem. Väärtuse 1 korral on see keelatud.", "Controls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "Kontrollib väljundi sidususe ja mitmekesisuse vahelist tasa<PERSON>. Madalam väärtus annab tulemuseks fokuseerituma ja sidusamaja teksti.", "Copied": "Kopeeritud", "Copied link to clipboard": "", "Copied shared chat URL to clipboard!": "Jagatud vestluse URL kopeeritud lõikelauale!", "Copied to clipboard": "Kopeeritud l<PERSON>", "Copy": "<PERSON><PERSON><PERSON>", "Copy Formatted Text": "", "Copy last code block": "Kopeeri viimane koodiplokk", "Copy last response": "Ko<PERSON><PERSON> viimane vastus", "Copy link": "", "Copy Link": "Kopeeri link", "Copy to clipboard": "<PERSON><PERSON><PERSON>", "Copying to clipboard was successful!": "Lõikelauale kopeerimine <PERSON>!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "Teenusepakkuja peab nõuetekohaselt konfigureerima CORS-i, et lubada päringuid Open WebUI-lt.", "Create": "Loo", "Create a knowledge base": "Loo teadmiste baas", "Create a model": "Loo mudel", "Create Account": "Loo konto", "Create Admin Account": "Loo administraatori konto", "Create Channel": "Loo kanal", "Create Folder": "", "Create Group": "Loo grupp", "Create Knowledge": "Loo teadmised", "Create new key": "Loo uus võti", "Create new secret key": "Loo uus salavõti", "Create Note": "", "Create your first note by clicking on the plus button below.": "", "Created at": "Loomise aeg", "Created At": "Loomise aeg", "Created by": "Autor", "CSV Import": "CSV import", "Ctrl+Enter to Send": "Ctrl+Enter saatmiseks", "Current Model": "<PERSON><PERSON><PERSON><PERSON> mudel", "Current Password": "<PERSON><PERSON><PERSON><PERSON> parool", "Custom": "<PERSON><PERSON><PERSON><PERSON>", "Custom description enabled": "", "Custom Parameter Name": "", "Custom Parameter Value": "", "Danger Zone": "Ohutsoon", "Dark": "<PERSON><PERSON>", "Database": "<PERSON><PERSON><PERSON><PERSON>", "Datalab Marker API": "", "Datalab Marker API Key required.": "", "DD/MM/YYYY": "", "December": "Detsember", "Default": "<PERSON><PERSON><PERSON><PERSON>", "Default (Open AI)": "<PERSON><PERSON><PERSON><PERSON> (Open AI)", "Default (SentenceTransformers)": "<PERSON><PERSON><PERSON><PERSON> (SentenceTransformers)", "Default action buttons will be used.": "", "Default description enabled": "", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "", "Default Model": "Vaikim<PERSON> mudel", "Default model updated": "Vaikimisi mudel uuendatud", "Default Models": "<PERSON>ai<PERSON><PERSON>", "Default permissions": "Vaikimisi õ<PERSON>used", "Default permissions updated successfully": "Vaikimisi õigused edukalt uuendatud", "Default Prompt Suggestions": "Vaikimisi vihjete soovitused", "Default to 389 or 636 if TLS is enabled": "Vaikimisi 389 või 636, <PERSON><PERSON> on lubatud", "Default to ALL": "Vaikimisi KÕIK", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "", "Default User Role": "<PERSON><PERSON><PERSON><PERSON> kasutaja roll", "Delete": "Kustuta", "Delete a model": "<PERSON><PERSON><PERSON> mudel", "Delete All Chats": "Kustuta kõik vestlused", "Delete All Models": "Kustuta kõik <PERSON>d", "Delete chat": "<PERSON><PERSON><PERSON> vestlus", "Delete Chat": "<PERSON><PERSON><PERSON> vestlus", "Delete chat?": "<PERSON><PERSON><PERSON><PERSON> vestlus?", "Delete folder?": "<PERSON><PERSON><PERSON><PERSON> kaust?", "Delete function?": "Kustutada funktsioon?", "Delete Message": "Kustuta sõnum", "Delete message?": "Kustutada sõnum?", "Delete note?": "", "Delete prompt?": "<PERSON>st<PERSON><PERSON> vihjed?", "delete this link": "kust<PERSON> see link", "Delete tool?": "Kustutada tööriist?", "Delete User": "<PERSON><PERSON><PERSON> ka<PERSON>ta<PERSON>", "Deleted {{deleteModelTag}}": "Kustutatud {{deleteModelTag}}", "Deleted {{name}}": "Kustutatud {{name}}", "Deleted User": "Kustutatud kasutaja", "Deployment names are required for Azure OpenAI": "", "Describe Pictures in Documents": "", "Describe your knowledge base and objectives": "<PERSON><PERSON><PERSON><PERSON><PERSON> oma tead<PERSON>e baasi ja e<PERSON>rke", "Description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Detect Artifacts Automatically": "", "Dictate": "", "Didn't fully follow instructions": "<PERSON><PERSON> jä<PERSON> tä<PERSON> juhiseid", "Direct": "", "Direct Connections": "<PERSON><PERSON><PERSON><PERSON>", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "Otsesed ühendused võimaldavad kasutajatel ühenduda oma OpenAI-ga ühilduvate API lõpp-punktidega.", "Direct Tool Servers": "", "Disable Code Interpreter": "", "Disable Image Extraction": "", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "", "Disabled": "Keelatud", "Discover a function": "Avasta funktsioon", "Discover a model": "<PERSON><PERSON> mudel", "Discover a prompt": "Avasta vihje", "Discover a tool": "Avasta tööriist", "Discover how to use Open WebUI and seek support from the community.": "<PERSON><PERSON><PERSON>, kuidas kasutada Open WebUI-d ja otsige tuge kogukonnalt.", "Discover wonders": "<PERSON><PERSON> imesid", "Discover, download, and explore custom functions": "<PERSON><PERSON>, laadi alla ja uuri kohandatud <PERSON>", "Discover, download, and explore custom prompts": "<PERSON><PERSON>, laadi alla ja uuri kohandatud vih<PERSON>id", "Discover, download, and explore custom tools": "<PERSON><PERSON>, laadi alla ja uuri kohandatud tö<PERSON>ri<PERSON>u", "Discover, download, and explore model presets": "<PERSON><PERSON>, laadi alla ja uuri mudeli e<PERSON>eadist<PERSON>i", "Display": "<PERSON><PERSON>", "Display Emoji in Call": "<PERSON><PERSON> kõnes emoji", "Display Multi-model Responses in Tabs": "", "Display the username instead of You in the Chat": "<PERSON><PERSON> '<PERSON><PERSON>' <PERSON><PERSON><PERSON>", "Displays citations in the response": "Kuvab vastuses viited", "Dive into knowledge": "<PERSON><PERSON><PERSON> teadmistesse", "Do not install functions from sources you do not fully trust.": "<PERSON>rge installige funktsioone allikatest, mida te täielikult ei usalda.", "Do not install tools from sources you do not fully trust.": "<PERSON>rge installige tööri<PERSON>u all<PERSON>, mida te täiel<PERSON> ei usalda.", "Docling": "", "Docling Server URL required.": "", "Document": "Dokument", "Document Intelligence": "Dokumendi intelligentsus", "Document Intelligence endpoint and key required.": "Dokumendi intelligentsuse lõpp-punkt ja võti on nõutavad.", "Documentation": "Dokumentatsioon", "Documents": "Dokumendid", "does not make any external connections, and your data stays securely on your locally hosted server.": "ei loo väliseid ühendusi ja teie andmed j<PERSON>d turvaliselt teie kohalikult majutatud serverisse.", "Domain Filter List": "<PERSON><PERSON> filtri ni<PERSON>ri", "Don't have an account?": "Pole kontot?", "don't install random functions from sources you don't trust.": "ärge installige juhuslikke funktsioone allikatest, mida te ei usalda.", "don't install random tools from sources you don't trust.": "ärge installige juhuslikke tööriistu allika<PERSON>, mida te ei usalda.", "Don't like the style": "Stiil ei meeldi", "Done": "Val<PERSON>", "Download": "<PERSON>adi alla", "Download as SVG": "Laadi alla SVG-na", "Download canceled": "Allalaadimine tühis<PERSON>ud", "Download Database": "Laadi alla and<PERSON><PERSON><PERSON>", "Drag and drop a file to upload or select a file to view": "Lohistage ja kukutage fail üleslaadimiseks või valige fail vaatamiseks", "Draw": "Jo<PERSON><PERSON>", "Drop any files here to upload": "", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "nt '30s', '10m'. <PERSON><PERSON><PERSON><PERSON> a<PERSON> on 's', 'm', 'h'.", "e.g. \"json\" or a JSON schema": "", "e.g. 60": "nt 60", "e.g. A filter to remove profanity from text": "nt filter, mis eemaldab tekstist roppused", "e.g. en": "", "e.g. My Filter": "nt Minu Filter", "e.g. My Tools": "nt Minu Tööriistad", "e.g. my_filter": "nt minu_filter", "e.g. my_tools": "nt minu_toriistad", "e.g. pdf, docx, txt": "", "e.g. Tools for performing various operations": "nt tööriistad mitmesuguste operatsioonide teostamiseks", "e.g., 3, 4, 5 (leave blank for default)": "", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "", "e.g., westus (leave blank for eastus)": "", "Edit": "<PERSON><PERSON>", "Edit Arena Model": "<PERSON><PERSON>", "Edit Channel": "<PERSON><PERSON> kanalit", "Edit Connection": "<PERSON>uda ü<PERSON>", "Edit Default Permissions": "<PERSON><PERSON> v<PERSON>", "Edit Folder": "", "Edit Memory": "<PERSON><PERSON> mälu", "Edit User": "<PERSON><PERSON>", "Edit User Group": "<PERSON><PERSON> ka<PERSON>g<PERSON>", "Edited": "", "Editing": "", "Eject": "", "ElevenLabs": "ElevenLabs", "Email": "E-post", "Embark on adventures": "<PERSON><PERSON><PERSON>", "Embedding": "Manustamine", "Embedding Batch Size": "Manustamise partii suurus", "Embedding Model": "<PERSON><PERSON><PERSON><PERSON> mudel", "Embedding Model Engine": "<PERSON><PERSON><PERSON><PERSON> mootor", "Embedding model set to \"{{embedding_model}}\"": "<PERSON><PERSON><PERSON><PERSON> mudel mä<PERSON>ud kui \"{{embedding_model}}\"", "Enable API Key": "Luba API võti", "Enable autocomplete generation for chat messages": "Luba automaattäitmise genereerimine vestlussõnumitele", "Enable Code Execution": "<PERSON><PERSON> koodi täitmine", "Enable Code Interpreter": "<PERSON><PERSON> koodi interpretaator", "Enable Community Sharing": "<PERSON><PERSON> kogukonnaga jagamine", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Luba mälu lukustamine (mlock), et vältida mudeli andmete vahetamist RAM-ist välja. See valik lukustab mudeli töökomplekti lehed RAM-i, tagades, et neid ei vahetata kettale. See aitab s<PERSON>ili<PERSON><PERSON> j<PERSON>, vältides lehevigu ja tagades kiire andmete juurdepääsu.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Luba mälu kaardistamine (mmap) mudeli andmete laadimiseks. See valik võimaldab süsteemil kasutada kettamahtu RAM-i laiendusena, koheldes kettafaile nii, nagu need oleksid RAM-is. See võib parandada mudeli jõudlust, võimaldades kiiremat andmete juurdepääsu. See ei pruugi siiski kõigi süsteemidega õigesti töötada ja võib tarbida märkimisväärse koguse kettaruumi.", "Enable Message Rating": "Luba sõnumite hindamine", "Enable Mirostat sampling for controlling perplexity.": "Luba Mirostat'i valim perplekssuse juhtimiseks.", "Enable New Sign Ups": "Luba uued registreerimised", "Enabled": "<PERSON><PERSON><PERSON>", "Endpoint URL": "", "Enforce Temporary Chat": "", "Enhance": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "<PERSON><PERSON><PERSON><PERSON>, et teie CSV-fail sisaldab 4 veergu selles jär<PERSON>korras: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>.", "Enter {{role}} message here": "Sisestage {{role}} sõnum siia", "Enter a detail about yourself for your LLMs to recall": "Sisestage detail enda kohta, mida teie LLM-id saavad meen<PERSON>da", "Enter a title for the pending user info overlay. Leave empty for default.": "", "Enter a watermark for the response. Leave empty for none.": "", "Enter api auth string (e.g. username:password)": "Sisestage api autentimisstring (nt kasutajanimi:parool)", "Enter Application DN": "Sisestage rakenduse DN", "Enter Application DN Password": "Sisestage rakenduse DN parool", "Enter Bing Search V7 Endpoint": "Sisestage Bing Search V7 lõpp-punkt", "Enter Bing Search V7 Subscription Key": "Sisestage Bing Search V7 tellimuse võti", "Enter Bocha Search API Key": "Sisestage Bocha Search API võti", "Enter Brave Search API Key": "Sisestage Brave Search API võti", "Enter certificate path": "<PERSON><PERSON><PERSON> sertifikaadi tee", "Enter CFG Scale (e.g. 7.0)": "Sisestage CFG skaala (nt 7.0)", "Enter Chunk Overlap": "Sisestage tükkide ülekate", "Enter Chunk Size": "<PERSON><PERSON><PERSON> tüki suurus", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "Sisestage komadega eraldatud \"token:kallut<PERSON>_väärtus\" paarid (näide: 5432:100, 413:-100)", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "", "Enter Datalab Marker API Base URL": "", "Enter Datalab Marker API Key": "", "Enter description": "<PERSON><PERSON><PERSON> kirje<PERSON>", "Enter Docling OCR Engine": "", "Enter Docling OCR Language(s)": "", "Enter Docling Server URL": "", "Enter Document Intelligence Endpoint": "Sisestage dokumendi intelligentsuse lõpp-punkt", "Enter Document Intelligence Key": "Sisestage dokumendi intelligentsuse võti", "Enter domains separated by commas (e.g., example.com,site.org)": "Sisestage domeenid komadega eraldatult (nt example.com,site.org)", "Enter Exa API Key": "Sisestage Exa API võti", "Enter External Document Loader API Key": "", "Enter External Document Loader URL": "", "Enter External Web Loader API Key": "", "Enter External Web Loader URL": "", "Enter External Web Search API Key": "", "Enter External Web Search URL": "", "Enter Firecrawl API Base URL": "", "Enter Firecrawl API Key": "", "Enter folder name": "", "Enter Github Raw URL": "Sisestage Github toorURL", "Enter Google PSE API Key": "Sisestage Google PSE API võti", "Enter Google PSE Engine Id": "Sisestage Google PSE mootori ID", "Enter Image Size (e.g. 512x512)": "Sisestage pildi suurus (nt 512x512)", "Enter Jina API Key": "Sisestage Jina API võti", "Enter JSON config (e.g., {\"disable_links\": true})": "", "Enter Jupyter Password": "<PERSON>sestage Jupyter parool", "Enter Jupyter Token": "<PERSON><PERSON><PERSON> Ju<PERSON> token", "Enter Jupyter URL": "Sisestage Jupyter URL", "Enter Kagi Search API Key": "Sisestage Kagi Search API võti", "Enter Key Behavior": "Sisestage võtme käitumine", "Enter language codes": "<PERSON><PERSON><PERSON>", "Enter Mistral API Key": "", "Enter Model ID": "Sisestage mudeli ID", "Enter model tag (e.g. {{modelTag}})": "Sisestage mudeli silt (nt {{modelTag}})", "Enter Mojeek Search API Key": "Sisestage Mojeek Search API võti", "Enter name": "", "Enter New Password": "", "Enter Number of Steps (e.g. 50)": "Sisestage sammude arv (nt 50)", "Enter Perplexity API Key": "Sisestage Perplexity API võti", "Enter Playwright Timeout": "", "Enter Playwright WebSocket URL": "", "Enter proxy URL (e.g. **************************:port)": "Sisestage puhverserveri URL (nt ****************************:port)", "Enter reasoning effort": "<PERSON><PERSON><PERSON>", "Enter Sampler (e.g. Euler a)": "Sisestage valimismeetod (nt Euler a)", "Enter Scheduler (e.g. Karras)": "Sisestage planeerija (nt Karras)", "Enter Score": "<PERSON><PERSON><PERSON> skoor", "Enter SearchApi API Key": "Sisestage SearchApi API võti", "Enter SearchApi Engine": "Sisestage SearchApi mootor", "Enter Searxng Query URL": "Sisestage Searxng päringu URL", "Enter Seed": "<PERSON><PERSON><PERSON> seeme", "Enter SerpApi API Key": "Sisestage SerpApi API võti", "Enter SerpApi Engine": "Sisestage SerpApi mootor", "Enter Serper API Key": "Sisestage Serper API võti", "Enter Serply API Key": "Sisestage Serply API võti", "Enter Serpstack API Key": "Sisestage Serpstack API võti", "Enter server host": "Sisestage serveri host", "Enter server label": "Sisestage serveri silt", "Enter server port": "Sisestage serveri port", "Enter Sougou Search API sID": "", "Enter Sougou Search API SK": "", "Enter stop sequence": "Sisestage lõpetamise j<PERSON>us", "Enter system prompt": "Sisestage süsteemi vihjed", "Enter system prompt here": "", "Enter Tavily API Key": "Sisestage Tavily API võti", "Enter Tavily Extract Depth": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "Sisestage oma WebUI avalik URL. Seda URL-i kasutatakse teadaannetes linkide genereerimiseks.", "Enter the URL of the function to import": "", "Enter the URL to import": "", "Enter Tika Server URL": "Sisestage Tika serveri URL", "Enter timeout in seconds": "Sisestage aegumine sekundites", "Enter to Send": "<PERSON>ter saat<PERSON>", "Enter Top K": "Sisestage Top K", "Enter Top K Reranker": "", "Enter URL (e.g. http://127.0.0.1:7860/)": "Sisestage URL (nt http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Sisestage URL (nt http://localhost:11434)", "Enter Yacy Password": "", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "", "Enter Yacy Username": "", "Enter your current password": "<PERSON><PERSON><PERSON> oma praegune parool", "Enter Your Email": "Sisestage oma e-post", "Enter Your Full Name": "<PERSON><PERSON><PERSON> oma t<PERSON>", "Enter your message": "Sisestage oma sõnum", "Enter your name": "", "Enter Your Name": "", "Enter your new password": "<PERSON><PERSON><PERSON> oma uus parool", "Enter Your Password": "<PERSON><PERSON><PERSON> oma parool", "Enter Your Role": "Sisestage oma roll", "Enter Your Username": "<PERSON><PERSON><PERSON> o<PERSON>", "Enter your webhook URL": "Sisestage oma webhook URL", "Error": "Viga", "ERROR": "VIGA", "Error accessing Google Drive: {{error}}": "Viga Google Drive'i juurdepääsul: {{error}}", "Error accessing media devices.": "", "Error starting recording.": "", "Error unloading model: {{error}}": "", "Error uploading file: {{error}}": "Viga faili üleslaadimisel: {{error}}", "Evaluations": "Hindamised", "Everyone": "", "Exa API Key": "Exa API võti", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Näide: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Näide: ALL", "Example: mail": "Näide: mail", "Example: ou=users,dc=foo,dc=example": "Näide: ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "Näide: sAMAccountName või uid või userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "Ületasite litsentsis määratud istekohtade arvu. Palun võtke ühendust toega, et suurendada istekohtade arvu.", "Exclude": "Välista", "Execute code for analysis": "Käivita kood analüüsimiseks", "Executing **{{NAME}}**...": "", "Expand": "<PERSON><PERSON><PERSON>", "Experimental": "Katsetuslik", "Explain": "<PERSON><PERSON><PERSON><PERSON>", "Explore the cosmos": "<PERSON><PERSON>", "Export": "Ekspordi", "Export All Archived Chats": "Ekspordi kõik arhiveeritud vestlused", "Export All Chats (All Users)": "Ekspordi kõik vestlused (kõik kasutajad)", "Export chat (.json)": "<PERSON><PERSON><PERSON><PERSON> (.json)", "Export Chats": "Ekspordi vestlused", "Export Config to JSON File": "Ekspordi seadistus JSON-failina", "Export Functions": "Ekspordi funktsioonid", "Export Models": "Ekspor<PERSON>", "Export Presets": "Ekspordi eelseadistused", "Export Prompt Suggestions": "", "Export Prompts": "Ekspordi vihjed", "Export to CSV": "Ekspordi CSV-na", "Export Tools": "Ekspordi tööriistad", "Export Users": "", "External": "", "External Document Loader URL required.": "", "External Task Model": "", "External Web Loader API Key": "", "External Web Loader URL": "", "External Web Search API Key": "", "External Web Search URL": "", "Fade Effect for Streaming Text": "", "Failed to add file.": "<PERSON><PERSON>i lisamine e<PERSON>.", "Failed to connect to {{URL}} OpenAPI tool server": "", "Failed to copy link": "", "Failed to create API Key.": "API võtme loomine ebaõnnestus.", "Failed to delete note": "", "Failed to extract content from the file: {{error}}": "", "Failed to extract content from the file.": "", "Failed to fetch models": "Mudelite toomine e<PERSON>", "Failed to generate title": "", "Failed to load chat preview": "", "Failed to load file content.": "", "Failed to read clipboard contents": "<PERSON><PERSON><PERSON><PERSON><PERSON> sisu lugemine e<PERSON>", "Failed to save connections": "", "Failed to save models configuration": "Mudelite konfigu<PERSON><PERSON><PERSON> salves<PERSON> e<PERSON>", "Failed to update settings": "Seadete uuendamine e<PERSON>", "Failed to upload file.": "<PERSON><PERSON>i <PERSON>adi<PERSON> e<PERSON>.", "Features": "Funktsioonid", "Features Permissions": "Funktsioonide <PERSON>", "February": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Feedback Details": "", "Feedback History": "Tagasiside ajalugu", "Feedbacks": "Tagasisided", "Feel free to add specific details": "Võite lisada konkreetseid üksikasju", "File": "Fail", "File added successfully.": "Fail edukalt lisatud.", "File content updated successfully.": "Faili sisu edukalt uuendatud.", "File Mode": "Faili režii<PERSON>", "File not found.": "Faili ei leitud.", "File removed successfully.": "Fail edukalt eemaldatud.", "File size should not exceed {{maxSize}} MB.": "Faili suurus ei tohiks ületada {{maxSize}} MB.", "File Upload": "", "File uploaded successfully": "Fail edukalt ü<PERSON>d", "Files": "<PERSON><PERSON><PERSON>", "Filter": "", "Filter is now globally disabled": "Filter on nüüd globaalselt keelatud", "Filter is now globally enabled": "Filter on nüüd globaalselt lubatud", "Filters": "Filtrid", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Tuvastati sõrmejälje võltsimine: initsiaalide kasutamine avatarina pole võimalik. Kasutatakse vaikimisi profiilikujutist.", "Firecrawl API Base URL": "", "Firecrawl API Key": "", "Floating Quick Actions": "", "Focus chat input": "Fokuseeri vestluse sisendile", "Folder deleted successfully": "Kaust edukalt kustutatud", "Folder Name": "", "Folder name cannot be empty.": "<PERSON><PERSON>a nimi ei saa olla tühi.", "Folder name updated successfully": "<PERSON><PERSON>a nimi edukalt uuenda<PERSON>d", "Folder updated successfully": "", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "Force OCR": "", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "Loo uusi radu", "Form": "Vorm", "Format Lines": "", "Format the lines in the output. Defaults to False. If set to True, the lines will be formatted to detect inline math and styles.": "", "Format your variables using brackets like this:": "Vormindage oma muutujad sulgudega nagu siin:", "Forwards system user session credentials to authenticate": "", "Full Context Mode": "Täiskonteksti režiim", "Function": "Funktsioon", "Function Calling": "Funktsiooni kutsumine", "Function created successfully": "Funktsioon edukalt loodud", "Function deleted successfully": "Funktsioon edukalt kustutatud", "Function Description": "Funktsiooni kirje<PERSON>", "Function ID": "Funktsiooni ID", "Function imported successfully": "", "Function is now globally disabled": "Funktsioon on nüüd globaalselt keelatud", "Function is now globally enabled": "Funktsioon on nüüd globaalselt lubatud", "Function Name": "<PERSON>ts<PERSON><PERSON> nimi", "Function updated successfully": "Funktsioon edukalt uuendatud", "Functions": "Funktsioonid", "Functions allow arbitrary code execution.": "Funktsioonid võimaldavad suvalise koodi käivitamist.", "Functions imported successfully": "Funktsioonid edukalt imporditud", "Gemini": "Gemini", "Gemini API Config": "Gemini API seadistus", "Gemini API Key is required.": "Gemini API võti on nõutav.", "General": "<PERSON><PERSON><PERSON>", "Generate": "", "Generate an image": "<PERSON><PERSON><PERSON> pilt", "Generate Image": "<PERSON><PERSON><PERSON> pilt", "Generate prompt pair": "<PERSON><PERSON>i vih<PERSON>e paar", "Generating search query": "Otsinguküsimuse genereerimine", "Generating...": "", "Get information on {{name}} in the UI": "", "Get started": "<PERSON><PERSON><PERSON>", "Get started with {{WEBUI_NAME}}": "Alusta {{WEBUI_NAME}} kasutamist", "Global": "Globaalne", "Good Response": "<PERSON>a vastus", "Google Drive": "Google Drive", "Google PSE API Key": "Google PSE API võti", "Google PSE Engine Id": "Google PSE mootori ID", "Group created successfully": "Grupp edukalt loodud", "Group deleted successfully": "Grupp edukalt kustutatud", "Group Description": "<PERSON><PERSON><PERSON> k<PERSON>", "Group Name": "Grupi nimi", "Group updated successfully": "Grupp edukalt uuendatud", "Groups": "Grupid", "H1": "", "H2": "", "H3": "", "Haptic Feedback": "Haptiline tagasiside", "Hello, {{name}}": "<PERSON><PERSON>, {{name}}", "Help": "<PERSON><PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "<PERSON>ake meil luua parim kog<PERSON>, jaga<PERSON> oma tag<PERSON> a<PERSON>lug<PERSON>!", "Hex Color": "Hex värv", "Hex Color - Leave empty for default color": "Hex värv - jätke tühjaks vaikevärvi jaoks", "Hide": "<PERSON><PERSON><PERSON>", "Hide from Sidebar": "", "Hide Model": "", "High Contrast Mode": "", "Home": "<PERSON><PERSON><PERSON>", "Host": "Host", "How can I help you today?": "Kuidas saan teid täna aidata?", "How would you rate this response?": "Kuidas hindaksite seda vastust?", "HTML": "", "Hybrid Search": "H<PERSON><PERSON><PERSON><PERSON>ing", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "<PERSON><PERSON><PERSON><PERSON>, et olen lugenud ja mõistan oma tegevuse tagajärgi. Olen teadlik suvalise koodi käivitamisega seotud riskidest ja olen kontrollinud allika usaldusväärsust.", "ID": "ID", "iframe Sandbox Allow Forms": "", "iframe Sandbox Allow Same Origin": "", "Ignite curiosity": "<PERSON><PERSON><PERSON><PERSON>", "Image": "Pilt", "Image Compression": "<PERSON><PERSON><PERSON> t<PERSON>", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "<PERSON><PERSON>i genereer<PERSON>", "Image Generation (Experimental)": "<PERSON><PERSON><PERSON> (katsetuslik)", "Image Generation Engine": "<PERSON><PERSON>i genereerimise mootor", "Image Max Compression Size": "<PERSON><PERSON><PERSON> maks<PERSON><PERSON> tihendami<PERSON> suurus", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "<PERSON><PERSON><PERSON> vihje gene<PERSON>", "Image Prompt Generation Prompt": "<PERSON><PERSON><PERSON> vihje genereerimise vihje", "Image Settings": "<PERSON><PERSON><PERSON> seaded", "Images": "<PERSON><PERSON><PERSON>", "Import": "", "Import Chats": "<PERSON><PERSON><PERSON> vestlused", "Import Config from JSON File": "<PERSON><PERSON><PERSON> seadistus JSON-failist", "Import From Link": "", "Import Functions": "Impordi funktsioonid", "Import Models": "<PERSON><PERSON><PERSON>", "Import Notes": "", "Import Presets": "<PERSON><PERSON><PERSON> e<PERSON>", "Import Prompt Suggestions": "", "Import Prompts": "Impordi v<PERSON>d", "Import Tools": "Impordi tööriistad", "Include": "<PERSON><PERSON>", "Include `--api-auth` flag when running stable-diffusion-webui": "Lisage `--api-auth` lipp stable-diffusion-<PERSON><PERSON> käivitamisel", "Include `--api` flag when running stable-diffusion-webui": "Lisage `--api` lipp stable-diffusion-<PERSON><PERSON> käivitamisel", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kui kiiresti algoritm reageerib genereeritud teksti tagasisidele. Madalam õppimiskiirus annab tulemuseks a<PERSON>lasemad k<PERSON>, samas kui kõ<PERSON>m õppimiskiirus muudab algoritmi tundlikumaks.", "Info": "Info", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "", "Input": "", "Input commands": "Sisendkäsud", "Input Variables": "", "Insert": "", "Insert Follow-Up Prompt to Input": "", "Insert Prompt as Rich Text": "", "Install from Github URL": "Installige Github URL-ilt", "Instant Auto-Send After Voice Transcription": "Kohene automaatne saatmine pärast hääle transkriptsiooni", "Integration": "Integratsioon", "Interface": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Invalid file content": "", "Invalid file format.": "Vigane failiform<PERSON>t.", "Invalid JSON file": "", "Invalid JSON format in Additional Config": "", "Invalid Tag": "Vigane silt", "is typing...": "kirjutab...", "Italic": "", "January": "<PERSON><PERSON><PERSON><PERSON>", "Jina API Key": "Jina API võti", "join our Discord for help.": "liituge abi saamiseks meie Discordiga.", "JSON": "JSON", "JSON Preview": "JSON eelvaade", "July": "<PERSON><PERSON>", "June": "<PERSON><PERSON>", "Jupyter Auth": "<PERSON><PERSON><PERSON> autentimine", "Jupyter URL": "Jupyter URL", "JWT Expiration": "JWT aegumine", "JWT Token": "JWT token", "Kagi Search API Key": "Kagi Search API võti", "Keep Follow-Up Prompts in Chat": "", "Keep in Sidebar": "", "Key": "<PERSON><PERSON><PERSON>", "Keyboard shortcuts": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>ed", "Knowledge": "Teadmised", "Knowledge Access": "Teadmiste juurdepääs", "Knowledge Base": "", "Knowledge created successfully.": "Teadmised edukalt loodud.", "Knowledge deleted successfully.": "Teadmised edukalt kustutatud.", "Knowledge Public Sharing": "", "Knowledge reset successfully.": "Teadmised edukalt lähtestatud.", "Knowledge updated successfully": "Teadmised edukalt uuendatud", "Kokoro.js (Browser)": "Kokoro.js (brauser)", "Kokoro.js Dtype": "Kokoro.js andmetüüp", "Label": "Silt", "Landing Page Mode": "Maandumislehe režiim", "Language": "<PERSON><PERSON>", "Language Locales": "", "Last Active": "Viimati aktiivne", "Last Modified": "Viimati muudetud", "Last reply": "<PERSON><PERSON><PERSON><PERSON> vastus", "LDAP": "LDAP", "LDAP server updated": "LDAP server uuendatud", "Leaderboard": "<PERSON><PERSON><PERSON>", "Learn More": "", "Learn more about OpenAPI tool servers.": "", "Leave empty for no compression": "", "Leave empty for unlimited": "<PERSON><PERSON><PERSON> pii<PERSON> ka<PERSON>", "Leave empty to include all models from \"{{url}}\" endpoint": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{url}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "<PERSON><PERSON><PERSON>, et kaasata kõik mudelid või vali konkreetsed mudelid", "Leave empty to use the default prompt, or enter a custom prompt": "<PERSON><PERSON><PERSON>, et kasutada vai<PERSON> vihjet, või sisesta kohandatud vihje", "Leave model field empty to use the default model.": "<PERSON><PERSON><PERSON> mudeli väli t<PERSON>, et kasutada vaikimisi mudelit.", "lexical": "", "License": "Litsents", "Lift List": "", "Light": "<PERSON><PERSON>", "Listening...": "Kuulamine...", "Llama.cpp": "Llama.cpp", "LLMs can make mistakes. Verify important information.": "LLM-id võivad teha vigu. Kontrollige olulist teavet.", "Loader": "Laadija", "Loading Kokoro.js...": "Kokoro.js laadimine...", "Local": "<PERSON><PERSON><PERSON>", "Local Task Model": "", "Location access not allowed": "<PERSON><PERSON><PERSON><PERSON> ju<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pole lubatud", "Lost": "<PERSON><PERSON><PERSON><PERSON>", "LTR": "LTR", "Made by Open WebUI Community": "Loodud Open WebUI kogukonna poolt", "Make password visible in the user interface": "", "Make sure to enclose them with": "<PERSON><PERSON><PERSON><PERSON>, et need on ümbritsetud järgmisega:", "Make sure to export a workflow.json file as API format from ComfyUI.": "<PERSON><PERSON><PERSON><PERSON>, et ekspordite workflow.json faili API formaadis ComfyUI-st.", "Manage": "<PERSON><PERSON>", "Manage Direct Connections": "<PERSON><PERSON>", "Manage Models": "<PERSON><PERSON>", "Manage Ollama": "<PERSON><PERSON>'t", "Manage Ollama API Connections": "Halda Ollama API ühendusi", "Manage OpenAI API Connections": "Halda OpenAI API ühendusi", "Manage Pipelines": "<PERSON><PERSON>", "Manage Tool Servers": "", "March": "<PERSON><PERSON><PERSON>", "Markdown": "", "Markdown (Header)": "", "Max Speakers": "", "Max Upload Count": "Maksimaalne üleslaadimiste arv", "Max Upload Size": "<PERSON><PERSON><PERSON><PERSON><PERSON> suurus", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Korraga saab alla laadida maksimaalselt 3 mudelit. <PERSON>lun proovige hiljem uuesti.", "May": "<PERSON>", "Memories accessible by LLMs will be shown here.": "LLM-idele ligi<PERSON>äsetavad mälestused kuvatakse siin.", "Memory": "Mälu", "Memory added successfully": "Mälu edukalt lisatud", "Memory cleared successfully": "Mälu edukalt tühje<PERSON>tud", "Memory deleted successfully": "Mälu edukalt kustutatud", "Memory updated successfully": "Mälu edukalt uuendatud", "Merge Responses": "Ühenda vastused", "Merged Response": "Kombineeritud vastus", "Message rating should be enabled to use this feature": "<PERSON><PERSON> ka<PERSON> peaks sõnumite hindamine olema lubatud", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Teie sa<PERSON>tud sõnumeid pärast lingi loomist ei jagata. Kasutajad, kellel on URL, saavad vaadata jagatud vestlust.", "Microsoft OneDrive": "", "Microsoft OneDrive (personal)": "", "Microsoft OneDrive (work/school)": "", "Mistral OCR": "", "Mistral OCR API Key required.": "", "Model": "<PERSON><PERSON>", "Model '{{modelName}}' has been successfully downloaded.": "Mudel '{{modelName}}' on edukalt alla laaditud.", "Model '{{modelTag}}' is already in queue for downloading.": "Mudel '{{modelTag}}' on juba allalaadimise järje<PERSON>.", "Model {{modelId}} not found": "Mudelit {{modelId}} ei leitud", "Model {{modelName}} is not vision capable": "Mudel {{modelName}} ei ole võimeline visuaalseid sisendeid töötlema", "Model {{name}} is now {{status}}": "Mudel {{name}} on nüüd {{status}}", "Model {{name}} is now hidden": "", "Model {{name}} is now visible": "", "Model accepts file inputs": "", "Model accepts image inputs": "<PERSON><PERSON> v<PERSON><PERSON>b vastu pilte sisendina", "Model can execute code and perform calculations": "", "Model can generate images based on text prompts": "", "Model can search the web for information": "", "Model created successfully!": "Mudel edukalt loodud!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Tuvas<PERSON><PERSON> mudeli failisüsteemi tee. <PERSON><PERSON>dam<PERSON>ks on vajalik mudeli lühinimi, ei saa jätkata.", "Model Filtering": "<PERSON><PERSON><PERSON>", "Model ID": "<PERSON><PERSON><PERSON>", "Model ID is required.": "", "Model IDs": "<PERSON><PERSON><PERSON>-d", "Model Name": "<PERSON><PERSON><PERSON> nimi", "Model Name is required.": "", "Model not selected": "<PERSON>del pole valitud", "Model Params": "<PERSON><PERSON><PERSON>", "Model Permissions": "<PERSON><PERSON><PERSON>", "Model unloaded successfully": "", "Model updated successfully": "<PERSON><PERSON> edukalt u<PERSON>d", "Model(s) do not support file upload": "", "Modelfile Content": "<PERSON><PERSON><PERSON>", "Models": "<PERSON><PERSON><PERSON>", "Models Access": "<PERSON><PERSON><PERSON> ju<PERSON><PERSON><PERSON>", "Models configuration saved successfully": "Mudelite seadistus eduka<PERSON> sa<PERSON>", "Models Public Sharing": "", "Mojeek Search API Key": "Mojeek Search API võti", "more": "rohkem", "More": "Rohkem", "More Concise": "", "More Options": "", "Name": "<PERSON><PERSON>", "Name your knowledge base": "Nimetage oma tead<PERSON>e baas", "Native": "Omane", "New Button": "", "New Chat": "<PERSON><PERSON> vestlus", "New Folder": "<PERSON><PERSON>", "New Function": "", "New Note": "", "New Password": "<PERSON><PERSON> parool", "New Tool": "", "new-channel": "uus-kanal", "Next message": "", "No chats found": "", "No chats found for this user.": "", "No chats found.": "", "No content": "", "No content found": "<PERSON>su ei leitud", "No content found in file.": "", "No content to speak": "Pole mida rääkida", "No distance available": "<PERSON><PERSON><PERSON> pole saadaval", "No feedbacks found": "Tagasisidet ei leitud", "No file selected": "<PERSON><PERSON><PERSON> pole valitud", "No groups with access, add a group to grant access": "Puuduvad juurdepääsuõigustega grupid, lisage grupp juurdepääsu andmiseks", "No HTML, CSS, or JavaScript content found.": "HTML, CSS ega JavaScript sisu ei leitud.", "No inference engine with management support found": "Järeldusmootorit haldamise toega ei leitud", "No knowledge found": "Teadmisi ei leitud", "No memories to clear": "<PERSON> m<PERSON>, mida kustutada", "No model IDs": "<PERSON><PERSON>i <PERSON>-d puuduvad", "No models found": "Mudeleid ei leitud", "No models selected": "Mudeleid pole valitud", "No Notes": "", "No results found": "Tulemusi ei leitud", "No search query generated": "Otsingupäringut ei genereeritud", "No source available": "<PERSON><PERSON><PERSON> pole saadaval", "No users were found.": "<PERSON><PERSON><PERSON><PERSON><PERSON> ei leitud.", "No valves to update": "<PERSON> klappe, mida uuendada", "None": "<PERSON><PERSON>", "Not factually correct": "Faktiliselt ebakorrektne", "Not helpful": "Pole abistav", "Note deleted successfully": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Märkus: kui mä<PERSON>rate minimaalse skoori, tag<PERSON><PERSON> otsing ainult dokumendid, mille skoor on suurem või võrdne minimaalse skooriga.", "Notes": "Märkmed", "Notification Sound": "<PERSON><PERSON><PERSON><PERSON> heli", "Notification Webhook": "<PERSON><PERSON><PERSON><PERSON> webhook", "Notifications": "<PERSON><PERSON><PERSON><PERSON>", "November": "November", "OAuth ID": "OAuth ID", "October": "Oktoober", "Off": "<PERSON><PERSON><PERSON><PERSON>", "Okay, Let's Go!": "<PERSON>a küll, l<PERSON>hme!", "OLED Dark": "OLED tume", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API settings updated": "Ollama API seaded uuendatud", "Ollama Version": "Ollama versioon", "On": "Sees", "OneDrive": "OneDrive", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "<PERSON><PERSON><PERSON> on ainult tähtede-numbrite kombinatsioonid ja sidekriipsud", "Only alphanumeric characters and hyphens are allowed in the command string.": "<PERSON><PERSON><PERSON><PERSON><PERSON> on lubatud ainult tähtede-numbrite kombinatsioonid ja sidekriipsud.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "<PERSON><PERSON> saab ainult kogusid, dokumentide muutmiseks/lisamiseks looge uus teadmiste baas.", "Only markdown files are allowed": "", "Only select users and groups with permission can access": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on ainult valitud õigustega kasutajatel ja gruppidel", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Oih! URL tundub olevat vigane. Palun kontrollige ja proovige uuesti.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Oih! Failide üleslaadimine on veel pooleli. <PERSON><PERSON><PERSON>, kuni üleslaadimine lõpeb.", "Oops! There was an error in the previous response.": "Oih! Eelmises vastuses oli viga.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Oih! Kasutate toetamatut meetodit (ain<PERSON> ka<PERSON><PERSON>). Palun serveerige WebUI tagarakendusest.", "Open file": "Ava fail", "Open in full screen": "<PERSON> täisekraanil", "Open modal to configure connection": "", "Open Modal To Manage Floating Quick Actions": "", "Open new chat": "<PERSON> uus vestlus", "Open Sidebar": "", "Open User Profile Menu": "", "Open WebUI can use tools provided by any OpenAPI server.": "", "Open WebUI uses faster-whisper internally.": "Open WebUI kasutab sisemiselt faster-whisper'it.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI kasutab SpeechT5 ja CMU Arctic kõneleja manustamisi.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Open WebUI versioon (v{{OPEN_WEBUI_VERSION}}) on madalam kui nõutav versioon (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API seadistus", "OpenAI API Key is required.": "OpenAI API võti on nõutav.", "OpenAI API settings updated": "OpenAI API seaded uuendatud", "OpenAI URL/Key required.": "OpenAI URL/võti on nõutav.", "openapi.json URL or Path": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "või", "Ordered List": "", "Organize your users": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "Other": "<PERSON><PERSON>", "OUTPUT": "VÄLJUND", "Output format": "Väljundformaat", "Output Format": "", "Overview": "Ülevaade", "page": "leht", "Paginate": "", "Parameters": "", "Password": "<PERSON><PERSON><PERSON>", "Passwords do not match.": "", "Paste Large Text as File": "K<PERSON>bi suur tekst failina", "PDF document (.pdf)": "PDF dokument (.pdf)", "PDF Extract Images (OCR)": "PDF-ist piltide väljavõtmine (OCR)", "pending": "ootel", "Pending": "", "Pending User Overlay Content": "", "Pending User Overlay Title": "", "Permission denied when accessing media devices": "Juurdepääs meediumiseadmetele keelatud", "Permission denied when accessing microphone": "Juurdepä<PERSON>s mikrofonile <PERSON>", "Permission denied when accessing microphone: {{error}}": "Juurdep<PERSON><PERSON><PERSON> mikrofonile keelatud: {{error}}", "Permissions": "Õigused", "Perplexity API Key": "Perplexity API võti", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "Isikup<PERSON>ras<PERSON>", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "<PERSON><PERSON><PERSON>", "Pinned": "Kinnitatud", "Pioneer insights": "<PERSON><PERSON>", "Pipe": "", "Pipeline deleted successfully": "Torustik edukalt kustutatud", "Pipeline downloaded successfully": "Torustik edukalt alla laaditud", "Pipelines": "<PERSON><PERSON><PERSON><PERSON>", "Pipelines Not Detected": "<PERSON>ust<PERSON><PERSON> ei tuvastatud", "Pipelines Valves": "<PERSON><PERSON><PERSON>", "Plain text (.md)": "", "Plain text (.txt)": "Lihttekst (.txt)", "Playground": "Mänguväljak", "Playwright Timeout (ms)": "", "Playwright WebSocket URL": "", "Please carefully review the following warnings:": "<PERSON><PERSON><PERSON> vaadake hoolikalt läbi järgmised hoiatused:", "Please do not close the settings page while loading the model.": "<PERSON><PERSON><PERSON> ärge sulgege seadete lehte mudeli laadimise ajal.", "Please enter a prompt": "<PERSON><PERSON><PERSON> si<PERSON> vihje", "Please enter a valid path": "", "Please enter a valid URL": "", "Please fill in all fields.": "Palun täitke kõik väljad.", "Please select a model first.": "<PERSON><PERSON><PERSON> valige esmalt mudel.", "Please select a model.": "<PERSON><PERSON><PERSON> valige mudel.", "Please select a reason": "<PERSON><PERSON><PERSON> valige p<PERSON>", "Please wait until all files are uploaded.": "", "Port": "Port", "Positive attitude": "Positiivne suhtumine", "Prefix ID": "Prefiksi ID", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "Prefiksi ID-d kasutatakse teiste ühendustega konfliktide vältimiseks, lisades mudeli ID-dele prefiksi - jätke tühjaks keelami<PERSON>", "Prevent file creation": "", "Preview": "", "Previous 30 days": "Eelmised 30 päeva", "Previous 7 days": "Eelmised 7 päeva", "Previous message": "", "Private": "", "Profile Image": "Profiilipilt", "Prompt": "<PERSON><PERSON><PERSON>", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Vihje (nt Räägi mulle üks huvitav fakt Rooma impeeriumi kohta)", "Prompt Autocompletion": "", "Prompt Content": "<PERSON><PERSON><PERSON>", "Prompt created successfully": "<PERSON><PERSON><PERSON> eduka<PERSON> lood<PERSON>", "Prompt suggestions": "<PERSON><PERSON><PERSON> soovit<PERSON>", "Prompt updated successfully": "Vihje edukalt uuendatud", "Prompts": "<PERSON><PERSON><PERSON><PERSON>", "Prompts Access": "Vihjete juurdepääs", "Prompts Public Sharing": "", "Public": "", "Pull \"{{searchValue}}\" from Ollama.com": "T<PERSON><PERSON> \"{{searchValue}}\" Ollama.com-ist", "Pull a model from Ollama.com": "Tõmba mudel Ollama.com-ist", "Query Generation Prompt": "Päringu genereerimise vihje", "Quick Actions": "", "RAG Template": "RAG mall", "Rating": "<PERSON><PERSON><PERSON>", "Re-rank models by topic similarity": "<PERSON><PERSON><PERSON><PERSON><PERSON> mudelid teema sarnasuse alusel ümber", "Read": "Loe", "Read Aloud": "<PERSON><PERSON> v<PERSON>", "Reason": "", "Reasoning Effort": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Record": "", "Record voice": "Salvesta h<PERSON>", "Redirecting you to Open WebUI Community": "Suunamine Open WebUI kogukonda", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "Vähendab mõttetuste genereerimise tõenäosust. Kõrgem väärtus (nt 100) annab mit<PERSON><PERSON><PERSON><PERSON><PERSON> vastuseid, samas kui madalam väärtus (nt 10) on konservatiivsem.", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Viita endale kui \"<PERSON><PERSON><PERSON><PERSON>\" (nt \"<PERSON><PERSON><PERSON><PERSON> his<PERSON> keelt\")", "References from": "<PERSON><PERSON><PERSON> allika<PERSON>", "Refused when it shouldn't have": "<PERSON><PERSON><PERSON>, kui ei oleks pidanud", "Regenerate": "<PERSON><PERSON><PERSON><PERSON>", "Reindex": "", "Reindex Knowledge Base Vectors": "", "Release Notes": "Väljalaskemärkmed", "Releases": "", "Relevance": "<PERSON><PERSON><PERSON><PERSON>", "Relevance Threshold": "", "Remember Dismissal": "", "Remove": "<PERSON><PERSON><PERSON>", "Remove {{MODELID}} from list.": "", "Remove file": "", "Remove File": "", "Remove image": "", "Remove Model": "<PERSON><PERSON><PERSON> mudel", "Remove this tag from list": "", "Rename": "<PERSON><PERSON><PERSON>", "Reorder Models": "<PERSON><PERSON> mudelite j<PERSON>", "Reply in Thread": "Vasta lõimes", "Reranking Engine": "", "Reranking Model": "Ümberjärjestamise mudel", "Reset": "Lähtesta", "Reset All Models": "Lähtesta kõik mudelid", "Reset Upload Directory": "Lähtesta üleslaadimiste kataloog", "Reset Vector Storage/Knowledge": "Lähtesta vektormälu/teadmised", "Reset view": "Lähtesta vaade", "Response": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Vastuste tea<PERSON>tusi ei saa aktiveerida, kuna veebisaidi õigused on keelatud. Vajalike juurdepääsude andmiseks külastage oma brauseri seadeid.", "Response splitting": "Vastuse tükeldamine", "Response Watermark": "", "Result": "<PERSON><PERSON><PERSON>", "Retrieval": "Taastamine", "Retrieval Query Generation": "<PERSON><PERSON><PERSON><PERSON> pä<PERSON>u genereerimine", "Rich Text Input for Chat": "Rikasteksti sisend vestluse jaoks", "RK": "RK", "Role": "Roll", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "Käivita", "Running": "Töötab", "Save": "Salvesta", "Save & Create": "Salvesta ja loo", "Save & Update": "Salvesta ja uuenda", "Save As Copy": "Salvesta koopiana", "Save Tag": "Salvesta silt", "Saved": "Salvestatud", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Vestluslogi salvestamine otse teie brauseri mällu pole enam toetatud. <PERSON><PERSON><PERSON> võ<PERSON><PERSON> hetk, et alla laadida ja kustutada oma vestluslogi, kl<PERSON><PERSON>ates allpool olevat nuppu. <PERSON><PERSON> m<PERSON>, saate hõlpsasti oma vestluslogi tagarakendusse uuesti importida, kasutades", "Scroll On Branch Change": "", "Search": "<PERSON><PERSON><PERSON>", "Search a model": "<PERSON><PERSON><PERSON>", "Search Base": "<PERSON><PERSON><PERSON><PERSON> baas", "Search Chats": "<PERSON><PERSON><PERSON>", "Search Collection": "<PERSON><PERSON><PERSON> kogust", "Search Filters": "Otsing<PERSON> filtrid", "search for archived chats": "", "search for folders": "", "search for pinned chats": "", "search for shared chats": "", "search for tags": "otsi silte", "Search Functions": "<PERSON><PERSON><PERSON>", "Search In Models": "", "Search Knowledge": "Otsi <PERSON>dmis<PERSON>", "Search Models": "<PERSON><PERSON><PERSON>", "Search Notes": "", "Search options": "<PERSON><PERSON><PERSON><PERSON> valikud", "Search Prompts": "<PERSON><PERSON><PERSON>", "Search Result Count": "Otsingutulemuste arv", "Search the internet": "Otsi internetist", "Search Tools": "<PERSON><PERSON><PERSON>", "SearchApi API Key": "SearchApi API võti", "SearchApi Engine": "<PERSON><PERSON><PERSON> mootor", "Searched {{count}} sites": "O<PERSON><PERSON> {{count}} saidilt", "Searching \"{{searchQuery}}\"": "Otsimine: \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "Teadmistest otsimine: \"{{searchQuery}}\"", "Searching the web...": "", "Searxng Query URL": "Searxng päringu URL", "See readme.md for instructions": "Juhiste saami<PERSON>ks vaadake readme.md", "See what's new": "<PERSON><PERSON><PERSON>, mis on uut", "Seed": "<PERSON><PERSON>", "Select a base model": "<PERSON>ige baas mudel", "Select a conversation to preview": "", "Select a engine": "<PERSON><PERSON> mootor", "Select a function": "Valige funktsioon", "Select a group": "Valige grupp", "Select a model": "Valige mudel", "Select a pipeline": "<PERSON>ige to<PERSON>", "Select a pipeline url": "Valige torustiku URL", "Select a tool": "Valige tööriist", "Select an auth method": "Valige autentimismeetod", "Select an Ollama instance": "Valige Ollama instants", "Select Engine": "<PERSON><PERSON> mootor", "Select Knowledge": "Valige teadmised", "Select only one model to call": "Valige ainult üks mudel kutsumiseks", "Selected model(s) do not support image inputs": "<PERSON><PERSON><PERSON> mudel(id) ei toeta pilte sisendina", "semantic": "", "Semantic distance to query": "<PERSON><PERSON><PERSON><PERSON> kaug<PERSON> p<PERSON>", "Send": "Saada", "Send a Message": "<PERSON>ada s<PERSON>num", "Send message": "<PERSON>ada s<PERSON>num", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Saadab `stream_options: { include_usage: true }` päringus.\nToetatud teenusepakkujad tagastavad määramisel vastuses tokeni kasutuse teabe.", "September": "September", "SerpApi API Key": "SerpApi API võti", "SerpApi Engine": "<PERSON><PERSON><PERSON><PERSON> mootor", "Serper API Key": "Serper API võti", "Serply API Key": "Serply API võti", "Serpstack API Key": "Serpstack API võti", "Server connection verified": "<PERSON><PERSON> ühendus kontrollitud", "Set as default": "Mää<PERSON> vai<PERSON>", "Set CFG Scale": "Määra CFG skaala", "Set Default Model": "Määra vaikim<PERSON> mudel", "Set embedding model": "<PERSON><PERSON><PERSON><PERSON> man<PERSON> mudel", "Set embedding model (e.g. {{model}})": "<PERSON><PERSON><PERSON><PERSON> man<PERSON> mudel (nt {{model}})", "Set Image Size": "<PERSON><PERSON><PERSON><PERSON> pildi suurus", "Set reranking model (e.g. {{model}})": "<PERSON><PERSON><PERSON>ra ümberjärjesta<PERSON> mudel (nt {{model}})", "Set Sampler": "<PERSON><PERSON><PERSON><PERSON> vali<PERSON>", "Set Scheduler": "Määra planeerija", "Set Steps": "<PERSON><PERSON><PERSON><PERSON>", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "Määrake kihtide arv, mis laaditakse GPU-le. Selle väärtuse suurendamine võib oluliselt parandada jõudlust mudelite puhul, mis on optimeeritud GPU kiirenduse jaoks, kuid võib tarbida rohkem energiat ja GPU ressursse.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "Määrake arvutusteks kasutatavate töölõimede arv. See valik kontrollib, mitu lõime kasutatakse saabuvate päringute samaaegseks töötlemiseks. Selle väärtuse suurendamine võib parandada jõudlust suure samaaegsusega töökoormuste korral, kuid võib tarbida rohkem CPU ressursse.", "Set Voice": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>l", "Set whisper model": "Määra whisper mudel", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "Seab tasase kallutatuse tokenite vastu, mis on esinenud vähemalt üks kord. Kõrgem väärtus (nt 1,5) ka<PERSON><PERSON> kordusi tugevamalt, samas kui madalam väärtus (nt 0,9) on leebem. Väärtuse 0 korral on see keelatud.", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "Seab skaleeritava kallutatuse tokenite vastu korduste karistamiseks, p<PERSON><PERSON><PERSON> sellel, mitu korda need on esinenud. Kõrgem väärtus (nt 1,5) karistab kordusi tugevamalt, samas kui madalam väärtus (nt 0,9) on leebem. Väärtuse 0 korral on see keelatud.", "Sets how far back for the model to look back to prevent repetition.": "<PERSON><PERSON><PERSON><PERSON>, kui kaugele mudel tagasi vaatab, et vältida kordusi.", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "Määrab genereerimiseks kasutatava juhusliku arvu seemne. Se<PERSON> määramine kindlale numbrile paneb mudeli genereerima sama teksti sama vihje korral.", "Sets the size of the context window used to generate the next token.": "Määrab järgmise tokeni genereerimiseks kasutatava konteksti akna suuruse.", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "<PERSON><PERSON><PERSON>rab kasutatavad lõpetamise järjestused. <PERSON><PERSON> see muster kohatakse, lõpetab LLM teksti genereerimise ja tagastab. Mitme lõpetamise mustri saab määrata, täpsustades modelfile'is mitu eraldi lõpetamise parameetrit.", "Settings": "Seaded", "Settings saved successfully!": "Seaded edukalt salvestatud!", "Share": "J<PERSON>", "Share Chat": "<PERSON><PERSON>", "Share to Open WebUI Community": "Jaga Open WebUI kogukonnaga", "Sharing Permissions": "", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "Näita", "Show \"What's New\" modal on login": "<PERSON><PERSON><PERSON> \"Mis on uut\" modaalakent sisselogimisel", "Show Admin Details in Account Pending Overlay": "Näita administraatori üksikasju konto ootel kattekihil", "Show All": "", "Show Formatting Toolbar": "", "Show image preview": "", "Show Less": "", "Show Model": "", "Show shortcuts": "<PERSON><PERSON><PERSON>", "Show your support!": "<PERSON><PERSON>ita oma toetust!", "Showcased creativity": "Näitas loovust", "Sign in": "<PERSON><PERSON> sisse", "Sign in to {{WEBUI_NAME}}": "<PERSON><PERSON> sisse {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "<PERSON>gi sisse {{WEBUI_NAME}} LDAP-ga", "Sign Out": "Logi välja", "Sign up": "Registreeru", "Sign up to {{WEBUI_NAME}}": "Registreeru {{WEBUI_NAME}}", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to False.": "", "Signing in to {{WEBUI_NAME}}": "Sisselogimine {{WEBUI_NAME}}", "Sink List": "", "sk-1234": "sk-1234", "Skip Cache": "", "Skip the cache and re-run the inference. Defaults to False.": "", "Sougou Search API sID": "", "Sougou Search API SK": "", "Source": "<PERSON><PERSON><PERSON>", "Speech Playback Speed": "<PERSON><PERSON><PERSON> ta<PERSON>ituse kiirus", "Speech recognition error: {{error}}": "Kõnetuvastuse viga: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "Kõne-tekstiks mootor", "Stop": "<PERSON><PERSON><PERSON>", "Stop Generating": "", "Stop Sequence": "<PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>", "Stream Chat Response": "<PERSON><PERSON><PERSON><PERSON><PERSON> vestluse vastust", "Stream Delta Chunk Size": "", "Strikethrough": "", "Strip Existing OCR": "", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "", "STT Model": "STT mudel", "STT Settings": "STT seaded", "Stylized PDF Export": "", "Subtitle (e.g. about the Roman Empire)": "Alampealkiri (nt Rooma impeeriumi kohta)", "Success": "Õnnestus", "Successfully updated.": "<PERSON><PERSON><PERSON> u<PERSON>.", "Suggest a change": "", "Suggested": "<PERSON><PERSON><PERSON><PERSON>", "Support": "<PERSON><PERSON>", "Support this plugin:": "Toeta seda pistikprogrammi:", "Supported MIME Types": "", "Sync directory": "<PERSON><PERSON>nk<PERSON><PERSON><PERSON> kataloog", "System": "<PERSON><PERSON><PERSON><PERSON>", "System Instructions": "<PERSON><PERSON><PERSON><PERSON><PERSON> juh<PERSON>", "System Prompt": "Süsteemi vihje", "Tags": "", "Tags Generation": "Siltide genereerimine", "Tags Generation Prompt": "Siltide genereerimise vihje", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "Saba vaba valimit kasutatakse väljundis vähem tõenäoliste tokenite mõju vähendamiseks. Kõrgem väärtus (nt 2,0) vähendab mõju rohkem, samas kui väärtus 1,0 keelab selle seade.", "Talk to model": "<PERSON><PERSON><PERSON><PERSON>", "Tap to interrupt": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "Task List": "", "Task Model": "", "Tasks": "Ülesanded", "Tavily API Key": "Tavily API võti", "Tavily Extract Depth": "", "Tell us more:": "<PERSON><PERSON><PERSON><PERSON> me<PERSON> lä<PERSON>t:", "Temperature": "Temperatuur", "Temporary Chat": "<PERSON><PERSON><PERSON> vest<PERSON>", "Text Splitter": "Teksti tükeldaja", "Text-to-Speech": "", "Text-to-Speech Engine": "Tekst-kõneks mootor", "Thanks for your feedback!": "Täname tagasiside eest!", "The Application Account DN you bind with for search": "Rakenduse konto DN, millega seote otsingu jaoks", "The base to search for users": "<PERSON>as kasuta<PERSON> o<PERSON>", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "Partii suurus määrab, mitu tekstipäringut töödeldakse korraga. Suurem partii suurus võib suurendada mudeli jõudlust ja kiirust, kuid see nõuab ka rohkem mälu.", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "<PERSON><PERSON> pistikprogrammi taga olevad arendajad on kogukonna pühendunud vabatahtlikud. <PERSON><PERSON> le<PERSON>, et see pistikprogramm on kasulik, palun kaaluge selle arendamise toetamist.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "Hindamise edetabel põhineb Elo hindamissüsteemil ja seda uuendatakse reaal<PERSON>.", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "LDAP atribuut, mis kaardistab e-posti, mida kasuta<PERSON>d kasuta<PERSON>d sisselogim<PERSON>.", "The LDAP attribute that maps to the username that users use to sign in.": "LDAP <PERSON>, mis ka<PERSON><PERSON><PERSON>, mida kasuta<PERSON>d kasuta<PERSON>d sisselog<PERSON>.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "<PERSON><PERSON><PERSON> on praegu beetaversioonina ja me võime kohandada hindamisarvutusi algoritmi täiustamisel.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "<PERSON><PERSON><PERSON><PERSON>ne failisuurus MB-des. Kui failisuurus ületab seda piiri, faili ei laadita <PERSON>.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "<PERSON><PERSON><PERSON><PERSON><PERSON> failide arv, mida saab korraga vestluses kasutada. Kui failide arv ületab selle piiri, faile ei laadita <PERSON>.", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Skoor peaks olema väärtus vahemikus 0,0 (0%) kuni 1,0 (100%).", "The stream delta chunk size for the model. Increasing the chunk size will make the model respond with larger pieces of text at once.": "", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "<PERSON><PERSON><PERSON> temperatuur. Temperatuuri suurendamine paneb mudeli vastama loova<PERSON>t.", "The Weight of BM25 Hybrid Search. 0 more lexical, 1 more semantic. Default 0.5": "", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "<PERSON><PERSON>", "Thinking...": "Mõtleb...", "This action cannot be undone. Do you wish to continue?": "Seda toimingut ei saa tagasi võtta. Kas soovite jätkata?", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "", "This chat won't appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "See tagab, et teie väärtuslikud vestlused salvestatakse turvaliselt teie tagarakenduse andmebaasi. Täname!", "This feature is experimental and may be modified or discontinued without notice.": "", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "See on katsetuslik funktsioon, see ei pruugi toimida ootuspäraselt ja võib igal ajal muutuda.", "This model is not publicly available. Please select another model.": "", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "See valik kont<PERSON>, mitu tokenit säilitatakse konteksti värskendamisel. Näiteks kui see on määratud 2-le, säilitatakse vestluse konteksti viimased 2 tokenit. Konteksti säilitamine võib aidata säilitada vestluse järjepidevust, kuid võib vähendada võimet reageerida uutele teemadele.", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "See valik mä<PERSON>rab maksimaalse tokenite arvu, mida mudel saab oma vastuses genereerida. <PERSON><PERSON> piirm<PERSON>ra suurendamine võimaldab mudelil anda pikemaid vastuseid, kuid võib suurendada ka ebavajaliku või ebaolulise sisu genereerimise tõenäosust.", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "See valik kustutab kõik olemasolevad failid kogust ja asendab need äsja üleslaaditud failidega.", "This response was generated by \"{{model}}\"": "<PERSON><PERSON> genereeris \"{{model}}\"", "This will delete": "See kust<PERSON>b", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "See kustutab <strong>{{NAME}}</strong> ja <strong>kogu selle sisu</strong>.", "This will delete all models including custom models": "See kust<PERSON><PERSON> kõ<PERSON> mudelid, <PERSON><PERSON><PERSON> kohandatud mudelid", "This will delete all models including custom models and cannot be undone.": "See k<PERSON><PERSON><PERSON> kõ<PERSON> mudelid, <PERSON><PERSON><PERSON> kohandatud mudelid, ja seda ei saa tagasi võtta.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "See lähtestab teadmiste baasi ja sünkroniseerib kõik failid. Kas soovite jätkata?", "Thorough explanation": "<PERSON><PERSON><PERSON><PERSON><PERSON> selgitus", "Thought for {{DURATION}}": "Mõtles {{DURATION}}", "Thought for {{DURATION}} seconds": "Mõtles {{DURATION}} sekundit", "Thought for less than a second": "", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "Tika serveri URL on nõutav.", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Nõuanne: Värskendage mitut muutuja kohta jär<PERSON>st<PERSON>, vajutades pärast iga asendust vestluse sisendis tabeldusklahvi.", "Title": "<PERSON><PERSON><PERSON><PERSON>", "Title (e.g. Tell me a fun fact)": "Pealkiri (nt Räägi mulle üks huvitav fakt)", "Title Auto-Generation": "Pealkirja automaatne genereerimine", "Title cannot be an empty string.": "<PERSON><PERSON><PERSON><PERSON> ei saa olla tühi string.", "Title Generation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Title Generation Prompt": "Peal<PERSON><PERSON>ja genereer<PERSON> vih<PERSON>", "TLS": "TLS", "To access the available model names for downloading,": "Juurdepääsuks saadaolevatele mudelinimedele allalaadimiseks,", "To access the GGUF models available for downloading,": "Juurdepääsuks allalaadimiseks saadaolevatele GGUF mudelitele,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "WebUI-le juurdepääsuks võtke ühendust administraatoriga. Administraatorid saavad hallata kasutajate staatuseid administraatori paneelist.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "Teadmiste baasi siia lisamiseks lisage need esmalt \"Teadmiste\" tööalale.", "To learn more about available endpoints, visit our documentation.": "Saadaolevate lõpp-punktide kohta rohkem teada saamiseks külastage meie dokumentatsiooni.", "To learn more about powerful prompt variables, click here": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "Teie privaatsuse kaitsmiseks jagatakse teie tagasisidest ainult hinnanguid, mudeli <PERSON>-sid, silte ja metaandmeid - teie vestluslogi jääb privaatseks ja neid ei kaasata.", "To select actions here, add them to the \"Functions\" workspace first.": "Toimingute siit valimiseks lisage need esmalt \"Funktsioonide\" tööalale.", "To select filters here, add them to the \"Functions\" workspace first.": "Filtrite siit valimiseks lisage need esmalt \"Funktsioonide\" tööalale.", "To select toolkits here, add them to the \"Tools\" workspace first.": "Tööriistakomplektide siit valimiseks lisage need esmalt \"Tööriistade\" tööalale.", "Toast notifications for new updates": "Hüpikmärguanded uuenduste kohta", "Today": "<PERSON><PERSON><PERSON>", "Toggle search": "", "Toggle settings": "<PERSON><PERSON><PERSON><PERSON> seaded", "Toggle sidebar": "<PERSON><PERSON><PERSON><PERSON>", "Toggle whether current connection is active.": "", "Token": "Token", "Too verbose": "Liiga paljusõnaline", "Tool created successfully": "Tööriist ed<PERSON><PERSON> lo<PERSON>", "Tool deleted successfully": "Tööriist edukalt kust<PERSON>", "Tool Description": "Tööri<PERSON> kirje<PERSON>", "Tool ID": "Tööriista ID", "Tool imported successfully": "Tööriist edukalt imporditud", "Tool Name": "Tööri<PERSON> nimi", "Tool Servers": "", "Tool updated successfully": "Tööriist eduka<PERSON>", "Tools": "Tööriistad", "Tools Access": "Tööriistade juurdepääs", "Tools are a function calling system with arbitrary code execution": "Tööriistad on funktsioonide kutsumise süsteem suvalise koodi täitmisega", "Tools Function Calling Prompt": "Tööriistade funktsioonide kutsumise vihje", "Tools have a function calling system that allows arbitrary code execution.": "Tööriistadel on funktsioonide kutsumise süsteem, mis võimaldab suvalise koodi täitmist.", "Tools Public Sharing": "", "Top K": "Top K", "Top K Reranker": "", "Transformers": "Transformers", "Trouble accessing Ollama?": "Probleeme Ollama juurdepääsuga?", "Trust Proxy Environment": "<PERSON><PERSON><PERSON> kes<PERSON>", "Try Again": "", "TTS Model": "TTS mudel", "TTS Settings": "TTS seaded", "TTS Voice": "TTS hääl", "Type": "<PERSON><PERSON><PERSON><PERSON>", "Type Hugging Face Resolve (Download) URL": "Sisestage Hugging Face Resolve (Allalaadimise) URL", "Uh-oh! There was an issue with the response.": "Oi-oi! Vastusega oli probleem.", "UI": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Unarchive All": "<PERSON><PERSON><PERSON> k<PERSON> a<PERSON>", "Unarchive All Archived Chats": "<PERSON><PERSON><PERSON> k<PERSON>ik arhiveeritud vestlused arhiivist", "Unarchive Chat": "<PERSON><PERSON><PERSON> ar<PERSON>", "Underline": "", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "<PERSON> mõistatused", "Unpin": "<PERSON><PERSON><PERSON>i", "Unravel secrets": "Ava saladused", "Unsupported file type.": "", "Untagged": "Sildistamata", "Untitled": "", "Update": "<PERSON><PERSON><PERSON>", "Update and Copy Link": "Uuenda ja kopeeri link", "Update for the latest features and improvements.": "<PERSON><PERSON><PERSON><PERSON>, et saada uusimad funktsioonid ja täiustused.", "Update password": "<PERSON><PERSON><PERSON> parooli", "Updated": "Uuendatud", "Updated at": "Uuendamise aeg", "Updated At": "Uuendamise aeg", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "Uuendage litsentseeritud plaanile täiustatud võimaluste jao<PERSON>, sealhulgas kohandatud teemad ja bränding ning pühendatud tugi.", "Upload": "<PERSON><PERSON>", "Upload a GGUF model": "Laadige üles GGUF mudel", "Upload Audio": "", "Upload directory": "Üleslaadimise kataloog", "Upload files": "Laadi failid <PERSON>", "Upload Files": "Laadi failid <PERSON>", "Upload Pipeline": "<PERSON><PERSON>", "Upload Progress": "Üleslaadimise progress", "URL": "URL", "URL Mode": "URL režiim", "Usage": "", "Use '#' in the prompt input to load and include your knowledge.": "Kasutage '#' v<PERSON><PERSON><PERSON>, et laadida ja kaasata oma teadmised.", "Use Gravatar": "<PERSON><PERSON><PERSON>", "Use groups to group your users and assign permissions.": "Kasutage gruppe oma kasutajate grupeerimiseks ja õiguste määramiseks.", "Use Initials": "<PERSON><PERSON><PERSON>", "Use LLM": "", "Use no proxy to fetch page contents.": "", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "", "user": "<PERSON><PERSON><PERSON><PERSON>", "User": "<PERSON><PERSON><PERSON><PERSON>", "User Groups": "", "User location successfully retrieved.": "<PERSON><PERSON><PERSON><PERSON> asuk<PERSON>t edukalt hangitud.", "User menu": "", "User Webhooks": "", "Username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Users": "Ka<PERSON><PERSON><PERSON><PERSON>", "Using Entire Document": "", "Using Focused Retrieval": "", "Using the default arena model with all models. Click the plus button to add custom models.": "Kasutatakse vaikimisi areena mudelit kõigi mudelitega. Kohandatud mudelite lisamiseks klõpsake plussmärgiga nuppu.", "Valid time units:": "Kehtivad ajaühikud:", "Valves": "<PERSON><PERSON><PERSON>", "Valves updated": "<PERSON><PERSON><PERSON>", "Valves updated successfully": "<PERSON><PERSON><PERSON> eduka<PERSON> u<PERSON>", "variable": "muutu<PERSON>", "Verify Connection": "", "Verify SSL Certificate": "", "Version": "Versioon", "Version {{selectedVersion}} of {{totalVersions}}": "Versioon {{selectedVersion}} / {{totalVersions}}", "View Replies": "<PERSON><PERSON><PERSON>", "View Result from **{{NAME}}**": "", "Visibility": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Vision": "", "Voice": "<PERSON><PERSON><PERSON><PERSON>", "Voice Input": "<PERSON><PERSON><PERSON><PERSON>d", "Voice mode": "", "Warning": "<PERSON><PERSON><PERSON>", "Warning:": "Hoiatus:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Hoiatus: <PERSON><PERSON> lubamine võ<PERSON>lda<PERSON> kasutajatel üles laadida suvalist koodi serverisse.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Hoiatus: <PERSON><PERSON> uuendate või muudate oma manustami<PERSON> mud<PERSON>t, peate kõik dokumendid uuesti importima.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "Hoiatus: <PERSON><PERSON><PERSON> t<PERSON> võimaldab suvalise koodi käivitamist, mis kujutab endast tõsist turvariski - jätkake äärmise ettevaatus<PERSON>a.", "Web": "<PERSON><PERSON><PERSON>", "Web API": "Veebi API", "Web Loader Engine": "", "Web Search": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Web Search Engine": "<PERSON><PERSON><PERSON> o<PERSON><PERSON>", "Web Search in Chat": "Veebiotsing vestluses", "Web Search Query Generation": "<PERSON><PERSON><PERSON><PERSON><PERSON> genereerimine", "Webhook URL": "Webhooki URL", "WebUI Settings": "WebUI seaded", "WebUI URL": "WebUI URL", "WebUI will make requests to \"{{url}}\"": "", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI teeb päringuid aadressile \"{{url}}/api/chat\"", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI teeb päringuid aadressile \"{{url}}/chat/completions\"", "What are you trying to achieve?": "Mida te püüate saavutada?", "What are you working on?": "Millega te tegelete?", "What's New in": "Mis on uut", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "Kui see on lubatud, vastab mudel igale vestlussõ<PERSON><PERSON> reaalajas, genereerides vastuse niipea, kui kasutaja sõnumi saadab. See režiim on kasulik reaalajas vestlusrakendustes, kuid võib mõjutada jõudlust aeglasema riistvara puhul.", "wherever you are": "kus iganes te olete", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "", "Whisper (Local)": "Whisper (lokaalne)", "Why?": "Miks?", "Widescreen Mode": "Laiekraani režiim", "Won": "<PERSON><PERSON><PERSON>", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "Töötab koos top-k-ga. K<PERSON>rgem väärtus (nt 0,95) annab tulemuseks mitmekesisema teksti, samas kui madalam väärtus (nt 0,5) genereerib keskendunuma ja konservatiivsema teksti.", "Workspace": "Tööala", "Workspace Permissions": "Tööala õigused", "Write": "<PERSON><PERSON><PERSON><PERSON>", "Write a prompt suggestion (e.g. Who are you?)": "<PERSON><PERSON><PERSON><PERSON> vihje soovitus (nt <PERSON> sa oled?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Kirjutage 50-<PERSON><PERSON><PERSON><PERSON> k<PERSON>, mis võta<PERSON> kokku [teema või märksõna].", "Write something...": "<PERSON><PERSON><PERSON><PERSON> midagi...", "Yacy Instance URL": "", "Yacy Password": "", "Yacy Username": "", "Yesterday": "<PERSON><PERSON>", "You": "<PERSON><PERSON>", "You are currently using a trial license. Please contact support to upgrade your license.": "Kasutate praegu proovilitsentsi. Palun võtke ühend<PERSON> toega, et oma litsentsi uuendada.", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "<PERSON><PERSON> korraga vestelda maksima<PERSON>elt {{maxCount}} faili(ga).", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "<PERSON><PERSON> is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> oma su<PERSON>t LLM-idega, lisades mälestusi alumise '<PERSON><PERSON>' nupu kaudu, mu<PERSON> need kasulik<PERSON>ks ja teile kohandatum<PERSON>.", "You cannot upload an empty file.": "Te ei saa üles la<PERSON>da tühja faili.", "You do not have permission to upload files.": "<PERSON><PERSON> pole õigust faile <PERSON><PERSON>.", "You have no archived conversations.": "Teil pole arhive<PERSON>tud <PERSON>i.", "You have shared this chat": "<PERSON><PERSON> seda vestlust jaganud", "You're a helpful assistant.": "<PERSON>d abi<PERSON> assistent.", "You're now logged in.": "<PERSON><PERSON> n<PERSON>üd sisse logitud.", "Your account status is currently pending activation.": "<PERSON><PERSON> konto sta<PERSON> on praegu ootel aktiveerimist.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "Ko<PERSON> teie toetus läheb otse pistikprogrammi arendajale; Open WebUI ei võta mingit protsenti. Kuid valitud rahastamisplatvormil võivad olla oma tasud.", "Youtube": "Youtube", "Youtube Language": "Youtube keel", "Youtube Proxy URL": "Youtube puhverserveri URL"}