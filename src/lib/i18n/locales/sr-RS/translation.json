{"-1 for no limit, or a positive integer for a specific limit": "-1 за бесконачно или позитивни број за одређено ограничење", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "„s“, „m“, „h“, „d“, „w“ или „-1“ за без истека.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(нпр. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(нпр. `sh webui.sh --api`)", "(latest)": "(најновије)", "(leave blank for to use commercial endpoint)": "", "[Last] dddd [at] h:mm A": "", "[Today at] h:mm A": "", "[Yesterday at] h:mm A": "", "{{ models }}": "{{ модели }}", "{{COUNT}} Available Tools": "", "{{COUNT}} characters": "", "{{COUNT}} hidden lines": "", "{{COUNT}} Replies": "{{COUNT}} одговора", "{{COUNT}} words": "", "{{user}}'s Chats": "Ћаскања корисника {{user}}", "{{webUIName}} Backend Required": "Захтева се {{webUIName}} позадинац", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Модел задатка се користи приликом извршавања задатака као што су генерисање наслова за ћаскања и упите за Веб претрагу", "a user": "корисник", "About": "О нама", "Accept autocomplete generation / Jump to prompt variable": "", "Access": "Приступ", "Access Control": "Контрола приступа", "Accessible to all users": "Доступно свим корисницима", "Account": "Налог", "Account Activation Pending": "Налози за активирање", "Accurate information": "Прецизне информације", "Action": "", "Actions": "Радње", "Activate": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Покрените ову наредбу куцањем \"/{{COMMAND}}\" у ћаскање.", "Active Users": "Активни корисници", "Add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Add a model ID": "Додај ИБ модела", "Add a short description about what this model does": "Додавање кратког описа о томе шта овај модел ради", "Add a tag": "Дода<PERSON> ознаку", "Add Arena Model": "Додај модел Арене", "Add Connection": "Додај везу", "Add Content": "Додај садржај", "Add content here": "Додај садржај овде", "Add Custom Parameter": "", "Add custom prompt": "Додај прилагођен упит", "Add Details": "", "Add Files": "Додај датотеке", "Add Group": "Дода<PERSON> групу", "Add Memory": "Додај сећање", "Add Model": "Додај модел", "Add Reaction": "", "Add Tag": "Дода<PERSON> ознаку", "Add Tags": "Додај ознаке", "Add text content": "Додај садржај текста", "Add User": "Дод<PERSON><PERSON> корисника", "Add User Group": "Додај корисничку групу", "Additional Config": "", "Additional configuration options for marker. This should be a JSON string with key-value pairs. For example, '{\"key\": \"value\"}'. Supported keys include: disable_links, keep_pageheader_in_output, keep_pagefooter_in_output, filter_blank_pages, drop_repeated_text, layout_coverage_threshold, merge_threshold, height_tolerance, gap_threshold, image_threshold, min_line_length, level_count, default_level": "", "Adjusting these settings will apply changes universally to all users.": "Прилагођавање ових подешавања ће применити промене на све кориснике.", "admin": "админ", "Admin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Admin Panel": "Ад<PERSON>ин табла", "Admin Settings": "Админ део", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Админи имају приступ свим алатима у сваком тренутку, корисницима је потребно доделити алате по моделу у радном простору", "Advanced Parameters": "Напредни параметри", "Advanced Params": "Напредни парамови", "AI": "", "All": "", "All Documents": "Сви документи", "All models deleted successfully": "Сви модели су успешно обрисани", "Allow Call": "", "Allow Chat Controls": "Дозволи контроле ћаскања", "Allow Chat Delete": "Дозволи брисање ћаскања", "Allow Chat Deletion": "Дозволи брисање ћаскања", "Allow Chat Edit": "Дозволи измену ћаскања", "Allow Chat Export": "", "Allow Chat Params": "", "Allow Chat Share": "", "Allow Chat System Prompt": "", "Allow Chat Valves": "", "Allow File Upload": "Дозволи отпремање датотека", "Allow Multiple Models in Chat": "", "Allow non-local voices": "Дозволи нелокалне гласове", "Allow Speech to Text": "", "Allow Temporary Chat": "Дозволи привремена ћаскања", "Allow Text to Speech": "", "Allow User Location": "Дозволи корисничку локацију", "Allow Voice Interruption in Call": "Дозволи прекид гласа у позиву", "Allowed Endpoints": "", "Allowed File Extensions": "", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "", "Already have an account?": "Већ имате налог?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "", "Always": "", "Always Collapse Code Blocks": "", "Always Expand Details": "", "Always Play Notification Sound": "", "Amazing": "Невероватно", "an assistant": "помоћник", "Analytics": "", "Analyzed": "", "Analyzing...": "", "and": "и", "and {{COUNT}} more": "и још {{COUNT}}", "and create a new shared link.": "и направи нову дељену везу.", "Android": "", "API": "", "API Base URL": "Основна адреса API-ја", "API Base URL for Datalab Marker service. Defaults to: https://www.datalab.to/api/v1/marker": "", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "API кључ", "API Key created.": "API кључ направљен.", "API Key Endpoint Restrictions": "", "API keys": "API кључеви", "API Version": "", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "<PERSON><PERSON>рил", "Archive": "<PERSON>р<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Archive All Chats": "Архивирај сва ћаскања", "Archived Chats": "Архиве", "archived-chat-export": "izvezena-arhiva", "Are you sure you want to clear all memories? This action cannot be undone.": "", "Are you sure you want to delete this channel?": "Да ли сигурно желите обрисати овај канал?", "Are you sure you want to delete this message?": "Да ли сигурно желите обрисати ову поруку?", "Are you sure you want to unarchive all archived chats?": "Да ли сигурно желите деархивирати све архиве?", "Are you sure?": "Да ли сте сигурни?", "Arena Models": "Модели са Арене", "Artifacts": "Артефакти", "Ask": "", "Ask a question": "Постави питање", "Assistant": "Помоћник", "Attach file from knowledge": "", "Attention to detail": "Пажња на детаље", "Attribute for Mail": "Особина е-поруке", "Attribute for Username": "Особина корисника", "Audio": "Звук", "August": "Август", "Auth": "", "Authenticate": "Идентификација", "Authentication": "", "Auto": "", "Auto-Copy Response to Clipboard": "Самостално копирање одговора у оставу", "Auto-playback response": "Самостално пуштање одговора", "Autocomplete Generation": "Стварање самодовршавања", "Autocomplete Generation Input Max Length": "Најдужи улаз стварања самодовршавања", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "Automatic1111 Api ниска идентификације", "AUTOMATIC1111 Base URL": "Основна адреса за AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "Потребна је основна адреса за AUTOMATIC1111.", "Available list": "Списак доступног", "Available Tools": "", "available!": "доступно!", "Awful": "Грозно", "Azure AI Speech": "Azure AI говор", "Azure Region": "Azure област", "Back": "Назад", "Bad Response": "<PERSON><PERSON><PERSON> одговор", "Banners": "Барјаке", "Base Model (From)": "Основни модел (од)", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "", "before": "пре", "Being lazy": "<PERSON>ити лењ", "Beta": "Бета", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "BM25 Weight": "", "Bocha Search API Key": "", "Bold": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "", "Brave Search API Key": "Апи кључ за храбру претрагу", "Bullet List": "", "Button ID": "", "Button Label": "", "Button Prompt": "", "By {{name}}": "Од {{name}}", "Bypass Embedding and Retrieval": "", "Bypass Web Loader": "", "Cache Base Model List": "", "Calendar": "", "Call": "Позив", "Call feature is not supported when using Web STT engine": "", "Camera": "Камера", "Cancel": "Откажи", "Capabilities": "Могућности", "Capture": "Ухвати", "Capture Audio": "", "Certificate Path": "Путања до сертификата", "Change Password": "Промени лозинку", "Channel Name": "Назив канала", "Channels": "Канали", "Character": "Знак", "Character limit for autocomplete generation input": "", "Chart new frontiers": "Откриј нове границе", "Chat": "Ћаскање", "Chat Background Image": "Позадинска слика ћаскања", "Chat Bubble UI": "Прочеље балона ћаскања", "Chat Controls": "Контроле ћаскања", "Chat direction": "Смер ћаскања", "Chat ID": "", "Chat Overview": "Преглед ћаскања", "Chat Permissions": "Дозволе ћаскања", "Chat Tags Auto-Generation": "Самостварање ознака ћаскања", "Chats": "Ћаскања", "Check Again": "Провери поново", "Check for updates": "Потра<PERSON>и ажурирања", "Checking for updates...": "Траже се ажурирања...", "Choose a model before saving...": "Изабери модел пре чувања...", "Chunk Overlap": "Преклапање делова", "Chunk Size": "Величина дела", "Ciphers": "Шифре", "Citation": "Цитат", "Citations": "", "Clear memory": "Очисти сећања", "Clear Memory": "", "click here": "кликни овде", "Click here for filter guides.": "Кликни овде за упутства филтера.", "Click here for help.": "Кликните овде за помоћ.", "Click here to": "Кликните овде да", "Click here to download user import template file.": "", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to see available models.": "", "Click here to select": "Кликните овде да изаберете", "Click here to select a csv file.": "Кликните овде да изаберете csv датотеку.", "Click here to select a py file.": "", "Click here to upload a workflow.json file.": "", "click here.": "кликните овде.", "Click on the user role button to change a user's role.": "Кликните на дугме за улогу корисника да промените улогу корисника.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "", "Clone": "Клонир<PERSON><PERSON>", "Clone Chat": "Клонир<PERSON>ј ћаскање", "Clone of {{TITLE}}": "", "Close": "Затвори", "Close Banner": "", "Close Configure Connection Modal": "", "Close modal": "", "Close settings modal": "", "Close Sidebar": "", "Code Block": "", "Code execution": "Извршавање кода", "Code Execution": "", "Code Execution Engine": "", "Code Execution Timeout": "", "Code formatted successfully": "Код форматиран успешно", "Code Interpreter": "", "Code Interpreter Engine": "", "Code Interpreter Prompt Template": "", "Collapse": "", "Collection": "Колекција", "Color": "Боја", "ComfyUI": "ComfyUI", "ComfyUI API Key": "ComfyUI API кључ", "ComfyUI Base URL": "Основна адреса за ComfyUI", "ComfyUI Base URL is required.": "Потребна је основна адреса за ComfyUI.", "ComfyUI Workflow": "ComfyUI радни ток", "ComfyUI Workflow Nodes": "ComfyUI чворови радног тока", "Command": "Наредба", "Comment": "", "Completions": "Допуне", "Compress Images in Channels": "", "Concurrent Requests": "Упоредни захтеви", "Configure": "Подеси", "Confirm": "Потврди", "Confirm Password": "Потврди лозинку", "Confirm your action": "Потврди радњу", "Confirm your new password": "Потврди нову лозинку", "Confirm Your Password": "", "Connect to your own OpenAI compatible API endpoints.": "", "Connect to your own OpenAPI compatible external tool servers.": "", "Connection failed": "", "Connection successful": "", "Connection Type": "", "Connections": "Везе", "Connections saved successfully": "", "Connections settings updated": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "", "Contact Admin for WebUI Access": "Пишите админима за приступ на WebUI", "Content": "Садржај", "Content Extraction Engine": "", "Continue Response": "Настави одговор", "Continue with {{provider}}": "Настави са {{provider}}", "Continue with Email": "Настави са е-адресом", "Continue with LDAP": "Настави са ЛДАП-ом", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "", "Controls": "Контроле", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "", "Copied": "Копирано", "Copied link to clipboard": "", "Copied shared chat URL to clipboard!": "Адреса дељеног ћаскања ископирана у оставу!", "Copied to clipboard": "Копирано у оставу", "Copy": "Коп<PERSON><PERSON><PERSON><PERSON>", "Copy Formatted Text": "", "Copy last code block": "Копирај последњи блок кода", "Copy last response": "Копирај последњи одговор", "Copy link": "", "Copy Link": "Копирај везу", "Copy to clipboard": "Копи<PERSON><PERSON><PERSON> у оставу", "Copying to clipboard was successful!": "Успешно копирање у оставу!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "", "Create": "Направи", "Create a knowledge base": "Направи базу знања", "Create a model": "Креирање модела", "Create Account": "Направи налог", "Create Admin Account": "Направи админ налог", "Create Channel": "Направи канал", "Create Folder": "", "Create Group": "Направи групу", "Create Knowledge": "Направи знање", "Create new key": "Направи нови кључ", "Create new secret key": "Направи нови тајни кључ", "Create Note": "", "Create your first note by clicking on the plus button below.": "", "Created at": "Направљено у", "Created At": "Направљено у", "Created by": "Направио/ла", "CSV Import": "Увоз CSV-а", "Ctrl+Enter to Send": "", "Current Model": "Тренутни модел", "Current Password": "Тренутна лозинка", "Custom": "Прилагођено", "Custom description enabled": "", "Custom Parameter Name": "", "Custom Parameter Value": "", "Danger Zone": "", "Dark": "Тамна", "Database": "База података", "Datalab Marker API": "", "Datalab Marker API Key required.": "", "DD/MM/YYYY": "", "December": "Децембар", "Default": "Подразумевано", "Default (Open AI)": "Подразумевано (Open AI)", "Default (SentenceTransformers)": "Подразумевано (SentenceTransformers)", "Default action buttons will be used.": "", "Default description enabled": "", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "", "Default Model": "Подразумевани модел", "Default model updated": "Подразумевани модел ажуриран", "Default Models": "Подразумевани модели", "Default permissions": "Подразумевана овлашћења", "Default permissions updated successfully": "", "Default Prompt Suggestions": "Подразумевани предлози упита", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "", "Default User Role": "Подразумевана улога корисника", "Delete": "Об<PERSON><PERSON><PERSON>и", "Delete a model": "Обриши модел", "Delete All Chats": "Обриши сва ћаскања", "Delete All Models": "Обриши све моделе", "Delete chat": "Обриши ћаскање", "Delete Chat": "Обриши ћаскање", "Delete chat?": "Обрисати ћаскање?", "Delete folder?": "Обрисати фасциклу?", "Delete function?": "Обрисати функцију?", "Delete Message": "Обриши поруку", "Delete message?": "", "Delete note?": "", "Delete prompt?": "Обрисати упит?", "delete this link": "обриши ову везу", "Delete tool?": "Обрисати алат?", "Delete User": "Обриши корисника", "Deleted {{deleteModelTag}}": "Обрисано {{deleteModelTag}}", "Deleted {{name}}": "Избрисан<PERSON> {{наме}}", "Deleted User": "Обрисани корисници", "Deployment names are required for Azure OpenAI": "", "Describe Pictures in Documents": "", "Describe your knowledge base and objectives": "Опишите вашу базу знања и циљеве", "Description": "<PERSON><PERSON><PERSON><PERSON>", "Detect Artifacts Automatically": "", "Dictate": "", "Didn't fully follow instructions": "Упутства нису праћена у потпуности", "Direct": "", "Direct Connections": "", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "", "Direct Tool Servers": "", "Disable Code Interpreter": "", "Disable Image Extraction": "", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "", "Disabled": "Онемогућено", "Discover a function": "Откријте функцију", "Discover a model": "Откријте модел", "Discover a prompt": "Отк<PERSON><PERSON>ј упит", "Discover a tool": "Откријте алат", "Discover how to use Open WebUI and seek support from the community.": "", "Discover wonders": "Откријте чудеса", "Discover, download, and explore custom functions": "Откријте, преузмите и истражите прилагођене функције", "Discover, download, and explore custom prompts": "Откријте, преузмите и истражите прилагођене упите", "Discover, download, and explore custom tools": "Откријте, преузмите и истражите прилагођене алате", "Discover, download, and explore model presets": "Откријте, преузмите и истражите образце модела", "Display": "Приказ", "Display Emoji in Call": "Прикажи емоџије у позиву", "Display Multi-model Responses in Tabs": "", "Display the username instead of You in the Chat": "Прикажи корисничко уместо Ти у ћаскању", "Displays citations in the response": "Прикажи цитате у одговору", "Dive into knowledge": "Ускочите у знање", "Do not install functions from sources you do not fully trust.": "", "Do not install tools from sources you do not fully trust.": "", "Docling": "", "Docling Server URL required.": "", "Document": "Документ", "Document Intelligence": "", "Document Intelligence endpoint and key required.": "", "Documentation": "Документација", "Documents": "Документи", "does not make any external connections, and your data stays securely on your locally hosted server.": "не отвара никакве спољне везе и ваши подаци остају сигурно на вашем локално хостованом серверу.", "Domain Filter List": "", "Don't have an account?": "Немате налог?", "don't install random functions from sources you don't trust.": "", "don't install random tools from sources you don't trust.": "", "Don't like the style": "Не свиђа ми се стил", "Done": "Готово", "Download": "Преузми", "Download as SVG": "", "Download canceled": "Преузимање отказано", "Download Database": "Преузми базу података", "Drag and drop a file to upload or select a file to view": "", "Draw": "Нацр<PERSON><PERSON><PERSON>", "Drop any files here to upload": "", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "нпр. '30s', '10m'. Важеће временске јединице су 's', 'm', 'h'.", "e.g. \"json\" or a JSON schema": "", "e.g. 60": "", "e.g. A filter to remove profanity from text": "", "e.g. en": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. pdf, docx, txt": "", "e.g. Tools for performing various operations": "", "e.g., 3, 4, 5 (leave blank for default)": "", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "", "e.g., westus (leave blank for eastus)": "", "Edit": "Измени", "Edit Arena Model": "Измени модел арене", "Edit Channel": "Измени канал", "Edit Connection": "Измени везу", "Edit Default Permissions": "Измени подразумевана овлашћења", "Edit Folder": "", "Edit Memory": "Измени сећање", "Edit User": "Измени корисника", "Edit User Group": "Измени корисничку групу", "Edited": "", "Editing": "", "Eject": "", "ElevenLabs": "", "Email": "Е-пошта", "Embark on adventures": "Започните пустоловину", "Embedding": "", "Embedding Batch Size": "", "Embedding Model": "Модел уградње", "Embedding Model Engine": "Мотор модела уградње", "Embedding model set to \"{{embedding_model}}\"": "Модел уградње подешен на \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Code Execution": "", "Enable Code Interpreter": "", "Enable Community Sharing": "Омогући дељење заједнице", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "", "Enable Mirostat sampling for controlling perplexity.": "", "Enable New Sign Ups": "Омогући нове пријаве", "Enabled": "Омогућено", "Endpoint URL": "", "Enforce Temporary Chat": "", "Enhance": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Уверите се да ваша CSV датотека укључује 4 колоне у овом редоследу: Име, Е-пошта, Лозинка, Улога.", "Enter {{role}} message here": "Унесите {{role}} поруку овде", "Enter a detail about yourself for your LLMs to recall": "Унесите детаље за себе да ће LLMs преузимати", "Enter a title for the pending user info overlay. Leave empty for default.": "", "Enter a watermark for the response. Leave empty for none.": "", "Enter api auth string (e.g. username:password)": "", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Bocha Search API Key": "", "Enter Brave Search API Key": "Унесите БРАВЕ Сеарцх АПИ кључ", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "", "Enter Chunk Overlap": "Унесите преклапање делова", "Enter Chunk Size": "Унесите величину дела", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "", "Enter Datalab Marker API Base URL": "", "Enter Datalab Marker API Key": "", "Enter description": "", "Enter Docling OCR Engine": "", "Enter Docling OCR Language(s)": "", "Enter Docling Server URL": "", "Enter Document Intelligence Endpoint": "", "Enter Document Intelligence Key": "", "Enter domains separated by commas (e.g., example.com,site.org)": "", "Enter Exa API Key": "", "Enter External Document Loader API Key": "", "Enter External Document Loader URL": "", "Enter External Web Loader API Key": "", "Enter External Web Loader URL": "", "Enter External Web Search API Key": "", "Enter External Web Search URL": "", "Enter Firecrawl API Base URL": "", "Enter Firecrawl API Key": "", "Enter folder name": "", "Enter Github Raw URL": "Унесите Гитхуб Раw УРЛ адресу", "Enter Google PSE API Key": "Унесите Гоогле ПСЕ АПИ кључ", "Enter Google PSE Engine Id": "Унесите Гоогле ПСЕ ИД машине", "Enter Image Size (e.g. 512x512)": "Унесите величину слике (нпр. 512x512)", "Enter Jina API Key": "", "Enter JSON config (e.g., {\"disable_links\": true})": "", "Enter Jupyter Password": "", "Enter Jupyter Token": "", "Enter Jupyter URL": "", "Enter Kagi Search API Key": "", "Enter Key Behavior": "", "Enter language codes": "Унесите кодове језика", "Enter Mistral API Key": "", "Enter Model ID": "", "Enter model tag (e.g. {{modelTag}})": "Унесите ознаку модела (нпр. {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name": "", "Enter New Password": "", "Enter Number of Steps (e.g. 50)": "Унесите број корака (нпр. 50)", "Enter Perplexity API Key": "", "Enter Playwright Timeout": "", "Enter Playwright WebSocket URL": "", "Enter proxy URL (e.g. **************************:port)": "", "Enter reasoning effort": "", "Enter Sampler (e.g. Euler a)": "", "Enter Scheduler (e.g. Karras)": "", "Enter Score": "Унесите резултат", "Enter SearchApi API Key": "", "Enter SearchApi Engine": "", "Enter Searxng Query URL": "Унесите УРЛ адресу Сеарxнг упита", "Enter Seed": "", "Enter SerpApi API Key": "", "Enter SerpApi Engine": "", "Enter Serper API Key": "Унесите Серпер АПИ кључ", "Enter Serply API Key": "", "Enter Serpstack API Key": "Унесите Серпстацк АПИ кључ", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter Sougou Search API sID": "", "Enter Sougou Search API SK": "", "Enter stop sequence": "Унесите секвенцу заустављања", "Enter system prompt": "Унеси системски упит", "Enter system prompt here": "", "Enter Tavily API Key": "", "Enter Tavily Extract Depth": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter the URL of the function to import": "", "Enter the URL to import": "", "Enter Tika Server URL": "", "Enter timeout in seconds": "", "Enter to Send": "", "Enter Top K": "Унесите Топ К", "Enter Top K Reranker": "", "Enter URL (e.g. http://127.0.0.1:7860/)": "Унесите адресу (нпр. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Унесите адресу (нпр. http://localhost:11434)", "Enter Yacy Password": "", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "", "Enter Yacy Username": "", "Enter your current password": "", "Enter Your Email": "Унесите вашу е-пошту", "Enter Your Full Name": "Унесите ваше име и презиме", "Enter your message": "", "Enter your name": "", "Enter Your Name": "", "Enter your new password": "", "Enter Your Password": "Унесите вашу лозинку", "Enter Your Role": "Унесите вашу улогу", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "Грешка", "ERROR": "ГРЕШКА", "Error accessing Google Drive: {{error}}": "", "Error accessing media devices.": "", "Error starting recording.": "", "Error unloading model: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "Процењивања", "Everyone": "", "Exa API Key": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: mail": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "", "Exclude": "", "Execute code for analysis": "", "Executing **{{NAME}}**...": "", "Expand": "", "Experimental": "Експериментално", "Explain": "", "Explore the cosmos": "", "Export": "Извоз", "Export All Archived Chats": "Извези све архиве", "Export All Chats (All Users)": "Извези сва ћаскања (сви корисници)", "Export chat (.json)": "Извези ћаскање (.json)", "Export Chats": "Извези ћаскања", "Export Config to JSON File": "", "Export Functions": "Извези функције", "Export Models": "Извези моделе", "Export Presets": "", "Export Prompt Suggestions": "", "Export Prompts": "Извези упите", "Export to CSV": "", "Export Tools": "", "Export Users": "", "External": "", "External Document Loader URL required.": "", "External Task Model": "", "External Web Loader API Key": "", "External Web Loader URL": "", "External Web Search API Key": "", "External Web Search URL": "", "Fade Effect for Streaming Text": "", "Failed to add file.": "", "Failed to connect to {{URL}} OpenAPI tool server": "", "Failed to copy link": "", "Failed to create API Key.": "Неуспешно стварање API кључа.", "Failed to delete note": "", "Failed to extract content from the file: {{error}}": "", "Failed to extract content from the file.": "", "Failed to fetch models": "", "Failed to generate title": "", "Failed to load chat preview": "", "Failed to load file content.": "", "Failed to read clipboard contents": "Неуспешно читање садржаја оставе", "Failed to save connections": "", "Failed to save models configuration": "", "Failed to update settings": "", "Failed to upload file.": "", "Features": "", "Features Permissions": "", "February": "Фебру<PERSON>р", "Feedback Details": "", "Feedback History": "Историјат повратних података", "Feedbacks": "Повратни подаци", "Feel free to add specific details": "Слободно додајте специфичне детаље", "File": "Датотека", "File added successfully.": "", "File content updated successfully.": "", "File Mode": "Режим датотеке", "File not found.": "Датотека није пронађена.", "File removed successfully.": "", "File size should not exceed {{maxSize}} MB.": "", "File Upload": "", "File uploaded successfully": "", "Files": "Датотеке", "Filter": "", "Filter is now globally disabled": "", "Filter is now globally enabled": "", "Filters": "", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Откривено лажно представљање отиска прста: Немогуће је користити иницијале као аватар. Прелазак на подразумевану профилну слику.", "Firecrawl API Base URL": "", "Firecrawl API Key": "", "Floating Quick Actions": "", "Focus chat input": "Усредсредите унос ћаскања", "Folder deleted successfully": "", "Folder Name": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Folder updated successfully": "", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "Упутства су савршено праћена", "Force OCR": "", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "", "Form": "", "Format Lines": "", "Format the lines in the output. Defaults to False. If set to True, the lines will be formatted to detect inline math and styles.": "", "Format your variables using brackets like this:": "", "Forwards system user session credentials to authenticate": "", "Full Context Mode": "", "Function": "", "Function Calling": "", "Function created successfully": "", "Function deleted successfully": "", "Function Description": "", "Function ID": "", "Function imported successfully": "", "Function is now globally disabled": "", "Function is now globally enabled": "", "Function Name": "", "Function updated successfully": "", "Functions": "Функције", "Functions allow arbitrary code execution.": "", "Functions imported successfully": "", "Gemini": "", "Gemini API Config": "", "Gemini API Key is required.": "", "General": "Опште", "Generate": "", "Generate an image": "", "Generate Image": "", "Generate prompt pair": "", "Generating search query": "Генерисање упита претраге", "Generating...": "", "Get information on {{name}} in the UI": "", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "", "Good Response": "Добар одговор", "Google Drive": "", "Google PSE API Key": "Гоогле ПСЕ АПИ кључ", "Google PSE Engine Id": "Гоогле ПСЕ ИД мотора", "Group created successfully": "Група направљена успешно", "Group deleted successfully": "Група обрисана успешно", "Group Description": "О<PERSON>ис групе", "Group Name": "Назив групе", "Group updated successfully": "Група измењена успешно", "Groups": "Гру<PERSON>е", "H1": "", "H2": "", "H3": "", "Haptic Feedback": "Вибрација као одговор", "Hello, {{name}}": "Здраво, {{name}}", "Help": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "Хекс боја", "Hex Color - Leave empty for default color": "Хекс боја (празно за подразумевано)", "Hide": "Сакриј", "Hide from Sidebar": "", "Hide Model": "", "High Contrast Mode": "", "Home": "", "Host": "<PERSON>о<PERSON><PERSON><PERSON><PERSON><PERSON>", "How can I help you today?": "Како могу да вам помогнем данас?", "How would you rate this response?": "", "HTML": "", "Hybrid Search": "Х<PERSON>б<PERSON><PERSON>дна претрага", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "", "ID": "ИБ", "iframe Sandbox Allow Forms": "", "iframe Sandbox Allow Same Origin": "", "Ignite curiosity": "Покрени знатижељу", "Image": "Слика", "Image Compression": "Компресија слике", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "Стварање слике", "Image Generation (Experimental)": "Стварање слика (експериментално)", "Image Generation Engine": "Мотор за стварање слика", "Image Max Compression Size": "", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "", "Image Prompt Generation Prompt": "", "Image Settings": "Подешавања слике", "Images": "Слике", "Import": "", "Import Chats": "Увези ћаскања", "Import Config from JSON File": "", "Import From Link": "", "Import Functions": "Увези функције", "Import Models": "Увези моделе", "Import Notes": "", "Import Presets": "", "Import Prompt Suggestions": "", "Import Prompts": "Увези упите", "Import Tools": "", "Include": "", "Include `--api-auth` flag when running stable-diffusion-webui": "", "Include `--api` flag when running stable-diffusion-webui": "Укључи `--api` заставицу при покретању stable-diffusion-webui", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "", "Info": "Инфо", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "", "Input": "", "Input commands": "Унеси наредбе", "Input Variables": "", "Insert": "", "Insert Follow-Up Prompt to Input": "", "Insert Prompt as Rich Text": "", "Install from Github URL": "Инста<PERSON><PERSON><PERSON><PERSON><PERSON> из Гитхуб УРЛ адресе", "Instant Auto-Send After Voice Transcription": "", "Integration": "", "Interface": "Изглед", "Invalid file content": "", "Invalid file format.": "", "Invalid JSON file": "", "Invalid JSON format in Additional Config": "", "Invalid Tag": "Неисправна ознака", "is typing...": "", "Italic": "", "January": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Jina API Key": "", "join our Discord for help.": "придружите се нашем Дискорду за помоћ.", "JSON": "JSON", "JSON Preview": "ЈСОН Преглед", "July": "<PERSON><PERSON>л", "June": "<PERSON><PERSON><PERSON>", "Jupyter Auth": "", "Jupyter URL": "", "JWT Expiration": "Истек JWT-а", "JWT Token": "JWT жетон", "Kagi Search API Key": "", "Keep Follow-Up Prompts in Chat": "", "Keep in Sidebar": "", "Key": "Кључ", "Keyboard shortcuts": "Пречице на тастатури", "Knowledge": "Знање", "Knowledge Access": "Присту<PERSON> знању", "Knowledge Base": "", "Knowledge created successfully.": "", "Knowledge deleted successfully.": "", "Knowledge Public Sharing": "", "Knowledge reset successfully.": "", "Knowledge updated successfully": "", "Kokoro.js (Browser)": "", "Kokoro.js Dtype": "", "Label": "Етикета", "Landing Page Mode": "Режим почетне стране", "Language": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Language Locales": "", "Last Active": "Последња активност", "Last Modified": "Последња измена", "Last reply": "Последњи одговор", "LDAP": "ЛДАП", "LDAP server updated": "ЛДАП сервер измењен", "Leaderboard": "Ранг листа", "Learn More": "", "Learn more about OpenAPI tool servers.": "", "Leave empty for no compression": "", "Leave empty for unlimited": "", "Leave empty to include all models from \"{{url}}\" endpoint": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{url}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "", "Leave model field empty to use the default model.": "", "lexical": "", "License": "", "Lift List": "", "Light": "Светла", "Listening...": "Слушам...", "Llama.cpp": "Llama.cpp", "LLMs can make mistakes. Verify important information.": "ВЈМ-ови (LLM-ови) могу правити грешке. Проверите важне податке.", "Loader": "", "Loading Kokoro.js...": "", "Local": "Локално", "Local Task Model": "", "Location access not allowed": "", "Lost": "Пораза", "LTR": "ЛНД", "Made by Open WebUI Community": "Израдила OpenWebUI заједница", "Make password visible in the user interface": "", "Make sure to enclose them with": "Уверите се да их затворите са", "Make sure to export a workflow.json file as API format from ComfyUI.": "", "Manage": "Управљај", "Manage Direct Connections": "", "Manage Models": "Управљај моделима", "Manage Ollama": "Управљај Ollama-ом", "Manage Ollama API Connections": "Управљај Ollama АПИ везама", "Manage OpenAI API Connections": "Управљај OpenAI АПИ везама", "Manage Pipelines": "Управљање цевоводима", "Manage Tool Servers": "", "March": "Ма<PERSON><PERSON>", "Markdown": "", "Markdown (Header)": "", "Max Speakers": "", "Max Upload Count": "", "Max Upload Size": "", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Највише 3 модела могу бити преузета истовремено. Покушајте поново касније.", "May": "М<PERSON>ј", "Memories accessible by LLMs will be shown here.": "Памћења које ће бити појављена од овог LLM-а ће бити приказана овде.", "Memory": "Сећања", "Memory added successfully": "Сећање успешно додато", "Memory cleared successfully": "Сећање успешно очишћено", "Memory deleted successfully": "Сећање успешно обрисано", "Memory updated successfully": "Сећање успешно измењено", "Merge Responses": "Спој одговоре", "Merged Response": "Спојени одговор", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Поруке које пошаљете након стварања ваше везе неће бити подељене. Корисници са URL-ом ће моћи да виде дељено ћаскање.", "Microsoft OneDrive": "", "Microsoft OneDrive (personal)": "", "Microsoft OneDrive (work/school)": "", "Mistral OCR": "", "Mistral OCR API Key required.": "", "Model": "<PERSON>о<PERSON><PERSON><PERSON>", "Model '{{modelName}}' has been successfully downloaded.": "<PERSON><PERSON><PERSON><PERSON><PERSON> „{{modelName}}“ је успешно преузет.", "Model '{{modelTag}}' is already in queue for downloading.": "<PERSON>о<PERSON><PERSON><PERSON> „{{modelTag}}“ је већ у реду за преузимање.", "Model {{modelId}} not found": "Модел {{modelId}} није пронађен", "Model {{modelName}} is not vision capable": "Модел {{моделНаме}} није способан за вид", "Model {{name}} is now {{status}}": "Модел {{наме}} је сада {{статус}}", "Model {{name}} is now hidden": "", "Model {{name}} is now visible": "", "Model accepts file inputs": "", "Model accepts image inputs": "", "Model can execute code and perform calculations": "", "Model can generate images based on text prompts": "", "Model can search the web for information": "", "Model created successfully!": "", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Откривена путања система датотека модела. За ажурирање је потребан кратак назив модела, не може се наставити.", "Model Filtering": "", "Model ID": "ИД модела", "Model ID is required.": "", "Model IDs": "", "Model Name": "", "Model Name is required.": "", "Model not selected": "Модел није изабран", "Model Params": "Модел Парамс", "Model Permissions": "", "Model unloaded successfully": "", "Model updated successfully": "", "Model(s) do not support file upload": "", "Modelfile Content": "Садр<PERSON><PERSON><PERSON> модел-датотеке", "Models": "Модели", "Models Access": "", "Models configuration saved successfully": "", "Models Public Sharing": "", "Mojeek Search API Key": "", "more": "више", "More": "Више", "More Concise": "", "More Options": "", "Name": "Име", "Name your knowledge base": "", "Native": "", "New Button": "", "New Chat": "Ново ћаскање", "New Folder": "", "New Function": "", "New Note": "", "New Password": "Нова лозинка", "New Tool": "", "new-channel": "", "Next message": "", "No chats found": "", "No chats found for this user.": "", "No chats found.": "", "No content": "", "No content found": "", "No content found in file.": "", "No content to speak": "", "No distance available": "", "No feedbacks found": "", "No file selected": "", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No inference engine with management support found": "", "No knowledge found": "", "No memories to clear": "", "No model IDs": "", "No models found": "", "No models selected": "", "No Notes": "", "No results found": "Нема резултата", "No search query generated": "Није генерисан упит за претрагу", "No source available": "Нема доступног извора", "No users were found.": "Нема корисника.", "No valves to update": "", "None": "Нико", "Not factually correct": "Није чињенично тачно", "Not helpful": "Није од помоћи", "Note deleted successfully": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Напомена: ако подесите најмањи резултат, претрага ће вратити само документе са резултатом већим или једнаким најмањем резултату.", "Notes": "Белешке", "Notification Sound": "Звук обавештења", "Notification Webhook": "Веб-кука обавештења", "Notifications": "Обавештења", "November": "Новембар", "OAuth ID": "", "October": "Октобар", "Off": "Искључено", "Okay, Let's Go!": "У реду, хајде да кренемо!", "OLED Dark": "OLED тамна", "Ollama": "Ollama", "Ollama API": "Оллама АПИ", "Ollama API settings updated": "", "Ollama Version": "Издање Ollama-е", "On": "Укључено", "OneDrive": "", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "Само алфанумерички знакови и цртице су дозвољени у низу наредби.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only markdown files are allowed": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Упс! Изгледа да је адреса неважећа. Молимо вас да проверите и покушате поново.", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Упс! Користите неподржани метод (само фронтенд). Молимо вас да покренете WebUI са бекенда.", "Open file": "", "Open in full screen": "", "Open modal to configure connection": "", "Open Modal To Manage Floating Quick Actions": "", "Open new chat": "Покрени ново ћаскање", "Open Sidebar": "", "Open User Profile Menu": "", "Open WebUI can use tools provided by any OpenAPI server.": "", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "Подешавање OpenAI API-ја", "OpenAI API Key is required.": "Потребан је OpenAI API кључ.", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "Потребан је OpenAI URL/кључ.", "openapi.json URL or Path": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "или", "Ordered List": "", "Organize your users": "Организујте ваше кориснике", "Other": "Остало", "OUTPUT": "ИЗЛАЗ", "Output format": "Формат излаза", "Output Format": "", "Overview": "Преглед", "page": "страница", "Paginate": "", "Parameters": "", "Password": "Лозинка", "Passwords do not match.": "", "Paste Large Text as File": "Убаци велики текст као датотеку", "PDF document (.pdf)": "PDF документ (.pdf)", "PDF Extract Images (OCR)": "Извлачење PDF слика (OCR)", "pending": "на чекању", "Pending": "", "Pending User Overlay Content": "", "Pending User Overlay Title": "", "Permission denied when accessing media devices": "Приступ медијским уређајима одбијен", "Permission denied when accessing microphone": "Приступ микрофону је одбијен", "Permission denied when accessing microphone: {{error}}": "Приступ микрофону је одбијен: {{error}}", "Permissions": "", "Perplexity API Key": "", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "Прилагођавање", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "Зака<PERSON>и", "Pinned": "Закачено", "Pioneer insights": "", "Pipe": "", "Pipeline deleted successfully": "Цевовод успешно обрисан", "Pipeline downloaded successfully": "Цевовод успешно преузет", "Pipelines": "Цевоводи", "Pipelines Not Detected": "Цевоводи нису уочени", "Pipelines Valves": "Вентили за цевоводе", "Plain text (.md)": "", "Plain text (.txt)": "Обичан текст (.txt)", "Playground": "Игралиште", "Playwright Timeout (ms)": "", "Playwright WebSocket URL": "", "Please carefully review the following warnings:": "", "Please do not close the settings page while loading the model.": "", "Please enter a prompt": "", "Please enter a valid path": "", "Please enter a valid URL": "", "Please fill in all fields.": "", "Please select a model first.": "", "Please select a model.": "", "Please select a reason": "", "Please wait until all files are uploaded.": "", "Port": "", "Positive attitude": "Позити<PERSON><PERSON>н став", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Prevent file creation": "", "Preview": "", "Previous 30 days": "Претходних 30 дана", "Previous 7 days": "Претходних 7 дана", "Previous message": "", "Private": "", "Profile Image": "Слика профила", "Prompt": "", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Упит (нпр. „подели занимљивост о Римском царству“)", "Prompt Autocompletion": "", "Prompt Content": "Сад<PERSON><PERSON><PERSON><PERSON> упита", "Prompt created successfully": "", "Prompt suggestions": "Предлози упита", "Prompt updated successfully": "Упит измењен успешно", "Prompts": "Упити", "Prompts Access": "Приступ упитима", "Prompts Public Sharing": "", "Public": "", "Pull \"{{searchValue}}\" from Ollama.com": "Повуците \"{{searchValue}}\" са Ollama.com", "Pull a model from Ollama.com": "Повуците модел са Ollama.com", "Query Generation Prompt": "", "Quick Actions": "", "RAG Template": "RAG шаблон", "Rating": "Оцена", "Re-rank models by topic similarity": "", "Read": "Читање", "Read Aloud": "Прочитај наглас", "Reason": "", "Reasoning Effort": "Ја<PERSON><PERSON>на размишљања", "Record": "", "Record voice": "Сними глас", "Redirecting you to Open WebUI Community": "Преусмеравање на OpenWebUI заједницу", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "", "References from": "Референце од", "Refused when it shouldn't have": "Одбијено када није требало", "Regenerate": "Поново створи", "Reindex": "", "Reindex Knowledge Base Vectors": "", "Release Notes": "Напомене о издању", "Releases": "", "Relevance": "Примењивост", "Relevance Threshold": "", "Remember Dismissal": "", "Remove": "Уклони", "Remove {{MODELID}} from list.": "", "Remove file": "", "Remove File": "", "Remove image": "", "Remove Model": "Уклони модел", "Remove this tag from list": "", "Rename": "Преим<PERSON><PERSON><PERSON><PERSON>", "Reorder Models": "", "Reply in Thread": "", "Reranking Engine": "", "Reranking Model": "Модел поновног рангирања", "Reset": "Поврати", "Reset All Models": "Поврати све моделе", "Reset Upload Directory": "", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "", "Response splitting": "", "Response Watermark": "", "Result": "Исход", "Retrieval": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "Богат унос текста у ћаскању", "RK": "", "Role": "Улога", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "ДНЛ", "Run": "Покрени", "Running": "Покрећем", "Save": "Сачувај", "Save & Create": "Сачувај и направи", "Save & Update": "Сачувај и ажурирај", "Save As Copy": "Сачувај као копију", "Save Tag": "Сачувај ознаку", "Saved": "Сачувано", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Чување ћаскања директно у складиште вашег прегледача више није подржано. Одвојите тренутак да преузмете и избришете ваша ћаскања кликом на дугме испод. Не брините, можете лако поново увезти ваша ћаскања у бекенд кроз", "Scroll On Branch Change": "", "Search": "Претра<PERSON>и", "Search a model": "Претражи модел", "Search Base": "Претражи базу", "Search Chats": "Претраж<PERSON> ћаскања", "Search Collection": "Претражи колекцију", "Search Filters": "Претражи филтере", "search for archived chats": "", "search for folders": "", "search for pinned chats": "", "search for shared chats": "", "search for tags": "", "Search Functions": "Претражи функције", "Search In Models": "", "Search Knowledge": "Претра<PERSON><PERSON> знање", "Search Models": "Модели претраге", "Search Notes": "", "Search options": "Опције претраге", "Search Prompts": "Претражи упите", "Search Result Count": "Број резултата претраге", "Search the internet": "", "Search Tools": "Алати претраге", "SearchApi API Key": "", "SearchApi Engine": "", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "", "Searching Knowledge for \"{{searchQuery}}\"": "", "Searching the web...": "", "Searxng Query URL": "УРЛ адреса Сеарxнг упита", "See readme.md for instructions": "Погледај readme.md за упутства", "See what's new": "Погледај шта је ново", "Seed": "Семе", "Select a base model": "Избор основног модела", "Select a conversation to preview": "", "Select a engine": "Изабери мотор", "Select a function": "Изабери функцију", "Select a group": "Изабери групу", "Select a model": "Изабери модел", "Select a pipeline": "Избор цевовода", "Select a pipeline url": "Избор урл адресе цевовода", "Select a tool": "Изабери алат", "Select an auth method": "", "Select an Ollama instance": "", "Select Engine": "Изабери мотор", "Select Knowledge": "Изабери знање", "Select only one model to call": "", "Selected model(s) do not support image inputs": "Изабрани модели не подржавају уносе слика", "semantic": "", "Semantic distance to query": "", "Send": "Пошаљи", "Send a Message": "Пошаљи поруку", "Send message": "Пошаљи поруку", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "Септембар", "SerpApi API Key": "", "SerpApi Engine": "", "Serper API Key": "Серпер АПИ кључ", "Serply API Key": "", "Serpstack API Key": "Серпстацк АПИ кључ", "Server connection verified": "Веза са сервером потврђена", "Set as default": "Подеси као подразумевано", "Set CFG Scale": "", "Set Default Model": "Подеси као подразумевани модел", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "Подеси модел уградње (нпр. {{model}})", "Set Image Size": "Подеси величину слике", "Set reranking model (e.g. {{model}})": "Подеси модел поновног рангирања (нпр. {{model}})", "Set Sampler": "", "Set Scheduler": "", "Set Steps": "Подеси кораке", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "Подеси глас", "Set whisper model": "", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets how far back for the model to look back to prevent repetition.": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "", "Sets the size of the context window used to generate the next token.": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "Подешавања", "Settings saved successfully!": "Подешавања успешно сачувана!", "Share": "Подели", "Share Chat": "Подели ћаскање", "Share to Open WebUI Community": "Подели са OpenWebUI заједницом", "Sharing Permissions": "", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "Прикажи", "Show \"What's New\" modal on login": "Прика<PERSON><PERSON> \"Погледај шта је ново\" прозорче при пријави", "Show Admin Details in Account Pending Overlay": "", "Show All": "", "Show Formatting Toolbar": "", "Show image preview": "", "Show Less": "", "Show Model": "", "Show shortcuts": "Прикажи пречице", "Show your support!": "", "Showcased creativity": "Приказана креативност", "Sign in": "Пријави се", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "Одјави се", "Sign up": "Региструј се", "Sign up to {{WEBUI_NAME}}": "", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to False.": "", "Signing in to {{WEBUI_NAME}}": "", "Sink List": "", "sk-1234": "", "Skip Cache": "", "Skip the cache and re-run the inference. Defaults to False.": "", "Sougou Search API sID": "", "Sougou Search API SK": "", "Source": "Извор", "Speech Playback Speed": "", "Speech recognition error: {{error}}": "Грешка у препознавању говора: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "Мотор за говор у текст", "Stop": "Заустави", "Stop Generating": "", "Stop Sequence": "Секвенца заустављања", "Stream Chat Response": "", "Stream Delta Chunk Size": "", "Strikethrough": "", "Strip Existing OCR": "", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "", "STT Model": "STT модел", "STT Settings": "STT подешавања", "Stylized PDF Export": "", "Subtitle (e.g. about the Roman Empire)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (нпр. о Римском царству)", "Success": "Успех", "Successfully updated.": "Успешно ажурирано.", "Suggest a change": "", "Suggested": "Предложено", "Support": "Подршка", "Support this plugin:": "Подржите овај прикључак", "Supported MIME Types": "", "Sync directory": "Фасцикла усклађивања", "System": "Систем", "System Instructions": "Системске инструкције", "System Prompt": "Системски упит", "Tags": "", "Tags Generation": "Стварање ознака", "Tags Generation Prompt": "Упит стварања ознака", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "", "Talk to model": "", "Tap to interrupt": "", "Task List": "", "Task Model": "", "Tasks": "", "Tavily API Key": "", "Tavily Extract Depth": "", "Tell us more:": "Реците нам више:", "Temperature": "Температура", "Temporary Chat": "Привремено ћаскање", "Text Splitter": "Раздва<PERSON><PERSON>ч текста", "Text-to-Speech": "", "Text-to-Speech Engine": "Мотор за текст у говор", "Thanks for your feedback!": "Хвала на вашем коментару!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Резултат треба да буде вредност између 0.0 (0%) и 1.0 (100%).", "The stream delta chunk size for the model. Increasing the chunk size will make the model respond with larger pieces of text at once.": "", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "", "The Weight of BM25 Hybrid Search. 0 more lexical, 1 more semantic. Default 0.5": "", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "Тема", "Thinking...": "Размишљам...", "This action cannot be undone. Do you wish to continue?": "Ова радња се не може опозвати. Да ли желите наставити?", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "", "This chat won't appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Ово осигурава да су ваши вредни разговори безбедно сачувани у вашој бекенд бази података. Хвала вам!", "This feature is experimental and may be modified or discontinued without notice.": "", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "", "This model is not publicly available. Please select another model.": "", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "Ово ће обрисати", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "Ово ће обрисати <strong>{{NAME}}</strong> и <strong>сав садржај унутар</strong>.", "This will delete all models including custom models": "Ово ће обрисати све моделе укључујући прилагођене моделе", "This will delete all models including custom models and cannot be undone.": "Ово ће обрисати све моделе укључујући прилагођене моделе и не може се опозвати.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Ово ће обрисати базу знања и ускладити све датотеке. Да ли желите наставити?", "Thorough explanation": "Детаљно објашњење", "Thought for {{DURATION}}": "", "Thought for {{DURATION}} seconds": "", "Thought for less than a second": "", "Tika": "", "Tika Server URL required.": "", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Савет: ажурирајте више променљивих слотова узастопно притиском на тастер Таб у уносу ћаскања након сваке замене.", "Title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Title (e.g. Tell me a fun fact)": "<PERSON>а<PERSON><PERSON><PERSON> (нпр. „реци ми занимљивост“)", "Title Auto-Generation": "Самостално стварање наслова", "Title cannot be an empty string.": "Наслов не може бити празан низ.", "Title Generation": "", "Title Generation Prompt": "Упит за стварање наслова", "TLS": "ТЛС", "To access the available model names for downloading,": "Да бисте приступили доступним именима модела за преузимање,", "To access the GGUF models available for downloading,": "Да бисте приступили GGUF моделима доступним за преузимање,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To learn more about powerful prompt variables, click here": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "", "To select filters here, add them to the \"Functions\" workspace first.": "", "To select toolkits here, add them to the \"Tools\" workspace first.": "", "Toast notifications for new updates": "Тост-обавештења за нове исправке", "Today": "<PERSON><PERSON><PERSON><PERSON>", "Toggle search": "", "Toggle settings": "Пребаци подешавања", "Toggle sidebar": "Пребаци бочну траку", "Toggle whether current connection is active.": "", "Token": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Too verbose": "Преопширно", "Tool created successfully": "Алат направљен успешно", "Tool deleted successfully": "Алат обрисан успешно", "Tool Description": "<PERSON><PERSON><PERSON><PERSON> алата", "Tool ID": "ИБ алата", "Tool imported successfully": "Алат увезен успешно", "Tool Name": "Назив алата", "Tool Servers": "", "Tool updated successfully": "Алат аж<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> успешно", "Tools": "<PERSON>ла<PERSON><PERSON>", "Tools Access": "Приступ алатима", "Tools are a function calling system with arbitrary code execution": "", "Tools Function Calling Prompt": "", "Tools have a function calling system that allows arbitrary code execution.": "", "Tools Public Sharing": "", "Top K": "Топ К", "Top K Reranker": "", "Transformers": "", "Trouble accessing Ollama?": "Проблеми са приступом Ollama-и?", "Trust Proxy Environment": "", "Try Again": "", "TTS Model": "TTS модел", "TTS Settings": "TTS подешавања", "TTS Voice": "TTS глас", "Type": "Тип", "Type Hugging Face Resolve (Download) URL": "Унесите Hugging Face Resolve (Download) адресу", "Uh-oh! There was an issue with the response.": "", "UI": "Прочеље", "Unarchive All": "Деархивирај све", "Unarchive All Archived Chats": "Деархивирај све архиве", "Unarchive Chat": "Деар<PERSON>ив<PERSON><PERSON><PERSON><PERSON> ћаскање", "Underline": "", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "Реши мистерије", "Unpin": "Откачи", "Unravel secrets": "Разоткриј тајне", "Unsupported file type.": "", "Untagged": "Неозначено", "Untitled": "", "Update": "Аж<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Update and Copy Link": "Ажу<PERSON><PERSON><PERSON><PERSON><PERSON> и копирај везу", "Update for the latest features and improvements.": "Ажурирајте за најновије могућности и побољшања.", "Update password": "Ажу<PERSON><PERSON><PERSON><PERSON><PERSON> лозинку", "Updated": "Аж<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Updated at": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> у", "Updated At": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> у", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "", "Upload": "Отпреми", "Upload a GGUF model": "Отпреми GGUF модел", "Upload Audio": "", "Upload directory": "Отпреми фасциклу", "Upload files": "Отпремање датотека", "Upload Files": "Отпремање датотека", "Upload Pipeline": "Цевовод отпремања", "Upload Progress": "Напредак отпремања", "URL": "УРЛ", "URL Mode": "Режим адресе", "Usage": "", "Use '#' in the prompt input to load and include your knowledge.": "", "Use Gravatar": "Користи Граватар", "Use groups to group your users and assign permissions.": "Користите групе да бисте разврстали ваше кориснике и доделили овлашћења.", "Use Initials": "Користи иницијале", "Use LLM": "", "Use no proxy to fetch page contents.": "", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "", "user": "корисник", "User": "Корисник", "User Groups": "", "User location successfully retrieved.": "Корисничка локација успешно добављена.", "User menu": "", "User Webhooks": "", "Username": "Корисничко име", "Users": "Корисници", "Using Entire Document": "", "Using Focused Retrieval": "", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Valid time units:": "Важеће временске јединице:", "Valves": "Вентили", "Valves updated": "Вентили ажурирани", "Valves updated successfully": "Вентили успешно ажурирани", "variable": "променљива", "Verify Connection": "", "Verify SSL Certificate": "", "Version": "Издање", "Version {{selectedVersion}} of {{totalVersions}}": "", "View Replies": "Погледај одговоре", "View Result from **{{NAME}}**": "", "Visibility": "Видљивост", "Vision": "", "Voice": "<PERSON><PERSON><PERSON><PERSON>", "Voice Input": "Гласовни унос", "Voice mode": "", "Warning": "Упозорење", "Warning:": "Упозорење:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Упозорење: ако ажурирате или промените ваш модел уградње, мораћете поново да увезете све документе.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "", "Web": "<PERSON>е<PERSON>", "Web API": "Веб АПИ", "Web Loader Engine": "", "Web Search": "Веб претрага", "Web Search Engine": "Веб претра<PERSON><PERSON><PERSON><PERSON>ч", "Web Search in Chat": "", "Web Search Query Generation": "", "Webhook URL": "Адреса веб-куке", "WebUI Settings": "Подешавања веб интерфејса", "WebUI URL": "WebUI адреса", "WebUI will make requests to \"{{url}}\"": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "What are you trying to achieve?": "", "What are you working on?": "", "What's New in": "Шта је ново у", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "", "Whisper (Local)": "", "Why?": "Зашто?", "Widescreen Mode": "Режим широког екрана", "Won": "Победа", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "", "Workspace": "Радни простор", "Workspace Permissions": "", "Write": "<PERSON>и<PERSON>и", "Write a prompt suggestion (e.g. Who are you?)": "Напишите предлог упита (нпр. „ко си ти?“)", "Write a summary in 50 words that summarizes [topic or keyword].": "Напишите сажетак у 50 речи који резимира [тему или кључну реч].", "Write something...": "Упишите нешто...", "Yacy Instance URL": "", "Yacy Password": "", "Yacy Username": "", "Yesterday": "Јуче", "You": "Ти", "You are currently using a trial license. Please contact support to upgrade your license.": "", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Можете учинити разговор са ВЈМ-овима приснијим додавањем сећања користећи „Управљај“ думе испод и тиме их учинити приснијим и кориснијим.", "You cannot upload an empty file.": "", "You do not have permission to upload files.": "", "You have no archived conversations.": "Немате архивиране разговоре.", "You have shared this chat": "Поделили сте ово ћаскање", "You're a helpful assistant.": "Ти си користан помоћник.", "You're now logged in.": "Сада сте пријављени.", "Your account status is currently pending activation.": "", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "", "Youtube": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Youtube Language": "", "Youtube Proxy URL": ""}