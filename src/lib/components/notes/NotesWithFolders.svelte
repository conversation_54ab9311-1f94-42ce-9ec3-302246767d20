<script lang="ts">
	import { onMount, getContext, createEventDispatcher } from 'svelte';
	import type { Writable } from 'svelte/store';
	import { toast } from 'svelte-sonner';
	import { goto } from '$app/navigation';
	import { page } from '$app/stores';
	import { browser } from '$app/environment';
	import { WEBUI_NAME, user, config, models, settings } from '$lib/stores';

	import { createNewNote, getNotes, deleteNoteById } from '$lib/apis/notes';
	import {
		getNotesInFolder,
		getNotesInRoot,
		getNotesInShared,
		getNoteFolderPath,
		moveNoteToFolder
	} from '$lib/apis/note-folders';
	import { getTimeRange, capitalizeFirstLetter, copyToClipboard } from '$lib/utils';
	import type { I18n } from '$lib/models/i18n';

	import dayjs from '$lib/dayjs';
	import duration from 'dayjs/plugin/duration';
	import relativeTime from 'dayjs/plugin/relativeTime';

	dayjs.extend(duration);
	dayjs.extend(relativeTime);

	import { PaneGroup, Pane, PaneResizer } from 'paneforge';

	import FolderTree from './FolderTree.svelte';
	import Search from '../icons/Search.svelte';
	import Plus from '../icons/Plus.svelte';
	import XMark from '../icons/XMark.svelte';
	import ChevronRight from '../icons/ChevronRight.svelte';
	import Spinner from '../common/Spinner.svelte';
	import ChatBubbleOval from '../icons/ChatBubbleOval.svelte';
	import Tooltip from '../common/Tooltip.svelte';
	import NotePanel from './NotePanel.svelte';
	import Chat from './NoteEditor/Chat.svelte';
	import EllipsisHorizontal from '../icons/EllipsisHorizontal.svelte';
	import NoteMenu from './Notes/NoteMenu.svelte';
	import DeleteConfirmDialog from '$lib/components/common/ConfirmDialog.svelte';

	const i18n = getContext<Writable<I18n>>('i18n');
	const dispatch = createEventDispatcher();

	let selectedFolderId: string | null = null;
	let selectedRootType: 'root' | 'shared' = 'root';
	let expandedFolders: Set<string> = new Set();
	let notes: any = {};
	let filteredNotes: any = {};
	let query = '';
	let loaded = false;
	let folderPath: any[] = [];

	// 狀態持久化相關
	const NOTES_STATE_KEY = 'notes_tree_state';

	// 保存樹狀態到 localStorage
	const saveTreeState = () => {
		if (!browser) return;
		
		const state = {
			selectedFolderId,
			selectedRootType,
			expandedFolders: Array.from(expandedFolders)
		};
		
		try {
			localStorage.setItem(NOTES_STATE_KEY, JSON.stringify(state));
		} catch (error) {
			console.warn('Failed to save notes tree state:', error);
		}
	};

	// 從 localStorage 恢復樹狀態
	const restoreTreeState = () => {
		if (!browser) return;
		
		try {
			const savedState = localStorage.getItem(NOTES_STATE_KEY);
			if (savedState) {
				const state = JSON.parse(savedState);
				selectedFolderId = state.selectedFolderId || null;
				selectedRootType = state.selectedRootType || 'root';
				expandedFolders = new Set(state.expandedFolders || []);
			}
		} catch (error) {
			console.warn('Failed to restore notes tree state:', error);
		}
		
		// 檢查 URL 參數是否有指定資料夾
		if (browser && $page.url.searchParams.has('folderId')) {
			const urlFolderId = $page.url.searchParams.get('folderId');
			if (urlFolderId === 'null') {
				selectedFolderId = null;
			} else {
				selectedFolderId = urlFolderId;
			}
		}
		
		if (browser && $page.url.searchParams.has('rootType')) {
			const urlRootType = $page.url.searchParams.get('rootType');
			if (urlRootType === 'root' || urlRootType === 'shared') {
				selectedRootType = urlRootType;
			}
		}
	};

	// 更新 URL 參數以反映當前狀態
	const updateUrlParams = () => {
		if (!browser) return;
		
		const url = new URL($page.url);
		
		if (selectedFolderId) {
			url.searchParams.set('folderId', selectedFolderId);
		} else {
			url.searchParams.set('folderId', 'null');
		}
		
		url.searchParams.set('rootType', selectedRootType);
		
		// 使用 replaceState 避免在瀏覽器歷史中創建新條目
		window.history.replaceState({}, '', url.toString());
	};

	// AI Chat 相關變數
	let showPanel = false;
	let selectedPanel = 'chat';
	let selectedModelId = null;
	let messages = [];
	let editing = false;
	let streaming = false;
	let stopResponseFlag = false;

	// Delete confirmation
	let showDeleteConfirm = false;
	let selectedNote: any = null;

	// 拖放相關變量
	let draggedNoteId: string | null = null;
	let dropTargetId: string | null = null;

	// 初始化
	const init = async () => {
		// 先恢復狀態，再載入筆記
		restoreTreeState();
		await loadNotes();
		// 更新 URL 參數
		updateUrlParams();
	};

	// 載入筆記
	const loadNotes = async () => {
		try {
			let notesList = [];

			if (selectedFolderId === null) {
				if (selectedRootType === 'root') {
					// 載入根目錄筆記
					notesList = await getNotesInRoot(localStorage.token);
				} else {
					// 載入共用區域筆記
					notesList = await getNotesInShared(localStorage.token);
				}
				// 清空資料夾路徑
				folderPath = [];
			} else {
				// 載入指定資料夾筆記
				notesList = await getNotesInFolder(localStorage.token, selectedFolderId);
				// 載入資料夾路徑
				folderPath = await getNoteFolderPath(localStorage.token, selectedFolderId);
			}

			// 按時間分組
			const groupedNotes = {};
			notesList.forEach((note) => {
				const timeRange = getTimeRange(note.updated_at / 1000000000);
				if (!groupedNotes[timeRange]) {
					groupedNotes[timeRange] = [];
				}
				groupedNotes[timeRange].push(note);
			});

			notes = groupedNotes;
			applyFilter();
		} catch (error) {
			toast.error(`${error}`);
		}
	};

	// 應用搜索過濾
	const applyFilter = () => {
		if (!query.trim()) {
			filteredNotes = notes;
			return;
		}

		const filtered = {};
		Object.keys(notes).forEach((timeRange) => {
			const filteredTimeRangeNotes = notes[timeRange].filter(
				(note) =>
					note.title.toLowerCase().includes(query.toLowerCase()) ||
					(note.data?.content?.md || '').toLowerCase().includes(query.toLowerCase())
			);
			if (filteredTimeRangeNotes.length > 0) {
				filtered[timeRange] = filteredTimeRangeNotes;
			}
		});
		filteredNotes = filtered;
	};

	// 資料夾選擇處理
	const handleFolderSelected = async (event: CustomEvent) => {
		selectedFolderId = event.detail.folderId;
		if (event.detail.rootType) {
			selectedRootType = event.detail.rootType;
		}
		
		// 保存狀態並更新 URL
		saveTreeState();
		updateUrlParams();
		
		await loadNotes();
	};

	// 創建新筆記
	const createNoteHandler = async () => {
		try {
			const res = await createNewNote(localStorage.token, {
				title: new Date().toISOString().split('T')[0], // YYYY-MM-DD
				data: {
					content: {
						json: null,
						html: '',
						md: ''
					}
				},
				meta: null,
				access_control: {},
				folder_id: selectedFolderId
			});

			if (res) {
				// 在導航到筆記詳情頁時保持當前狀態參數
				const url = new URL(`/notes/${res.id}`, window.location.origin);
				if (selectedFolderId) {
					url.searchParams.set('returnFolderId', selectedFolderId);
				} else {
					url.searchParams.set('returnFolderId', 'null');
				}
				url.searchParams.set('returnRootType', selectedRootType);
				
				goto(url.toString());
			}
		} catch (error) {
			toast.error(`${error}`);
		}
	};

	// 刪除筆記
	const deleteNoteHandler = async () => {
		if (!selectedNote) return;
		
		try {
			const res = await deleteNoteById(localStorage.token, selectedNote.id);
			if (res) {
				toast.success($i18n.t('Note deleted successfully'));
				await loadNotes();
			}
		} catch (error) {
			toast.error(`${error}`);
		}
		showDeleteConfirm = false;
		selectedNote = null;
	};

	// 監聽搜索查詢變化
	$: if (query !== undefined) {
		applyFilter();
	}

	// 初始化模型選擇
	const initModel = () => {
		if ($settings?.models) {
			selectedModelId = $settings?.models[0];
		} else if ($config?.default_models) {
			selectedModelId = $config?.default_models.split(',')[0];
		} else {
			selectedModelId = '';
		}

		if (selectedModelId) {
			const model = $models
				.filter((model) => model.id === selectedModelId && !(model?.info?.meta?.hidden ?? false))
				.find((model) => model.id === selectedModelId);

			if (!model) {
				selectedModelId = '';
			}
		}

		if (!selectedModelId) {
			selectedModelId = $models.at(0)?.id || '';
		}
	};

	// 拖放事件處理函數
	const handleNoteDragStart = (event: DragEvent, note: any) => {
		if (!event.dataTransfer) return;
		
		draggedNoteId = note.id;
		event.dataTransfer.setData('text/plain', note.id);
		event.dataTransfer.effectAllowed = 'move';
		
		// 添加拖拽樣式
		if (event.target instanceof HTMLElement) {
			event.target.classList.add('dragging');
		}
	};

	const handleNoteDragEnd = (event: DragEvent) => {
		draggedNoteId = null;
		dropTargetId = null;
		
		// 移除拖拽樣式
		if (event.target instanceof HTMLElement) {
			event.target.classList.remove('dragging');
		}
	};

	const handleFolderDragOver = (event: DragEvent) => {
		event.preventDefault();
		if (event.dataTransfer) {
			event.dataTransfer.dropEffect = 'move';
		}
	};

	const handleFolderDragEnter = (event: DragEvent, folderId: string | null) => {
		event.preventDefault();
		dropTargetId = folderId;
	};

	const handleFolderDragLeave = (event: DragEvent) => {
		// 只有當離開整個拖放區域時才清除高亮
		if (event.currentTarget && event.currentTarget instanceof Element && !event.currentTarget.contains(event.relatedTarget as Node)) {
			dropTargetId = null;
		}
	};

	const handleFolderDrop = async (event: DragEvent, folderId: string | null) => {
		event.preventDefault();
		dropTargetId = null;
		
		const noteId = event.dataTransfer?.getData('text/plain');
		if (!noteId || !localStorage.token) return;
		
		try {
			// 調用移動筆記 API
			await moveNoteToFolder(localStorage.token, noteId, folderId || undefined);
			
			// 顯示成功提示
			if (folderId) {
				toast.success($i18n.t('筆記已移動到資料夾'));
			} else {
				toast.success($i18n.t('筆記已移動到根目錄'));
			}
			
			// 重新加載筆記列表
			await loadNotes();
			
			// 通知資料夾樹更新
			dispatch('refresh-folders');
			
		} catch (error) {
			console.error('移動筆記失敗:', error);
			toast.error($i18n.t('移動筆記失敗'));
		}
	};

	// 創建虛擬筆記對象供 Chat 組件使用
	const createVirtualNote = () => {
		const notesList = Object.values(filteredNotes).flat();
		const notesContent = notesList
			.map((note) => `## ${note.title}\n${note.data?.content?.md || ''}`)
			.join('\n\n');

		return {
			id: 'virtual-notes',
			title: selectedFolderId ? `資料夾筆記` : '所有筆記',
			data: {
				content: {
					md: notesContent,
					html: '',
					json: null
				},
				files: null
			}
		};
	};

	onMount(async () => {
		await init();
		initModel();
		loaded = true;
	});
</script>

<svelte:head>
	<title>
		{$i18n.t('Notes')} • {$WEBUI_NAME}
	</title>
</svelte:head>

<div id="note-container" class="w-full h-full">
	<PaneGroup direction="horizontal" class="w-full h-full">
		<!-- 左側資料夾樹 -->
		<Pane defaultSize={30} minSize={15} maxSize={50} class="h-full">
			<div class="w-full h-full border-r border-gray-200 dark:border-gray-700">
				<FolderTree
					bind:selectedFolderId
					bind:selectedRootType
					bind:expandedFolders
					dropTargetId={dropTargetId}
					onFolderDragOver={handleFolderDragOver}
					onFolderDragEnter={handleFolderDragEnter}
					onFolderDragLeave={handleFolderDragLeave}
					onFolderDrop={handleFolderDrop}
					on:folderSelected={handleFolderSelected}
					on:expandedChanged={saveTreeState}
				/>
			</div>
		</Pane>

		<!-- 分隔線 -->
		<PaneResizer
			class="w-1 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors cursor-col-resize"
		/>

		<!-- 右側筆記列表 -->
		<Pane defaultSize={70} minSize={30} class="h-full flex flex-col w-full relative">
			<div class="w-full h-full overflow-hidden">
				{#if loaded}
					<!-- 頭部工具欄 -->
					<div class="flex flex-col gap-1 px-4 py-3 border-b border-gray-200 dark:border-gray-700">
						<!-- 面包屑導航 -->
						{#if folderPath.length > 0}
							<div class="flex items-center text-sm text-gray-600 dark:text-gray-400 mb-2">
								<button
									type="button"
									class="hover:text-gray-900 dark:hover:text-gray-200"
									on:click={() =>
										handleFolderSelected({
											detail: { folderId: null, rootType: selectedRootType }
										})}
								>
									{selectedRootType === 'root' ? $i18n.t('Root') : $i18n.t('Shared')}
								</button>
								{#each folderPath as folder, index}
									<ChevronRight className="w-4 h-4 mx-1" />
									<button
										type="button"
										class="hover:text-gray-900 dark:hover:text-gray-200 {index ===
										folderPath.length - 1
											? 'font-medium text-gray-900 dark:text-gray-100'
											: ''}"
										on:click={() => handleFolderSelected({ detail: { folderId: folder.id } })}
									>
										{folder.name}
									</button>
								{/each}
							</div>
						{/if}

						<!-- 搜索和新建按鈕 -->
						<div class="flex items-center space-x-3">
							<div class="flex-1 flex items-center">
								<div class="mr-3">
									<Search className="w-4 h-4" />
								</div>
								<input
									class="w-full text-sm py-1 bg-transparent outline-none"
									bind:value={query}
									placeholder={$i18n.t('Search Notes')}
								/>
								{#if query}
									<button
										class="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
										on:click={() => {
											query = '';
										}}
									>
										<XMark className="w-3 h-3" />
									</button>
								{/if}
							</div>
							<button
								type="button"
								class="flex items-center px-3 py-1.5 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
								on:click={createNoteHandler}
							>
								<Plus className="w-4 h-4 mr-1" />
								{$i18n.t('New Note')}
							</button>
							<Tooltip
								placement="top"
								content={$i18n.t('Chat with Notes')}
								className="cursor-pointer"
							>
								<button
									type="button"
									class="flex items-center px-3 py-1.5 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
									on:click={() => {
										if (showPanel && selectedPanel === 'chat') {
											showPanel = false;
										} else {
											if (!showPanel) {
												showPanel = true;
											}
											selectedPanel = 'chat';
										}
									}}
								>
									<ChatBubbleOval className="w-4 h-4 mr-1" />
									{$i18n.t('Chat')}
								</button>
							</Tooltip>
						</div>
					</div>

					<!-- 筆記列表 -->
					<div class="flex-1 overflow-y-auto p-4">
						{#if Object.keys(filteredNotes).length > 0}
							<div class="pb-10">
								{#each Object.keys(filteredNotes) as timeRange}
									<div class="w-full text-xs text-gray-500 dark:text-gray-500 font-medium pb-2.5">
										{$i18n.t(timeRange)}
									</div>

									<div
										class="mb-5 gap-2.5 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5"
									>
										{#each filteredNotes[timeRange] as note (note.id)}
										<div
											class="flex space-x-4 cursor-pointer w-full px-4.5 py-4 bg-gray-50 dark:bg-gray-850 dark:hover:bg-white/5 hover:bg-black/5 rounded-xl transition {draggedNoteId === note.id ? 'dragging' : ''}"
											draggable="true"
											role="button"
											tabindex="0"
											aria-label="Drag note to move to folder"
											on:dragstart={(event) => handleNoteDragStart(event, note)}
											on:dragend={handleNoteDragEnd}
										>
												<div class="flex flex-1 space-x-4 cursor-pointer w-full">
													<a
														href={`/notes/${note.id}`}
														class="w-full -translate-y-0.5 flex flex-col justify-between"
													>
														<div class="flex-1">
															<div class="flex items-center gap-2 self-center mb-1 justify-between">
																<div class="font-semibold line-clamp-1 capitalize">{note.title}</div>

																<div>
																	<NoteMenu
																		onDownload={(type) => {
																			selectedNote = note;
																			// TODO: Implement download handler
																		}}
																		onCopyLink={async () => {
																			const baseUrl = window.location.origin;
																			const res = await copyToClipboard(`${baseUrl}/notes/${note.id}`);

																			if (res) {
																				toast.success($i18n.t('Copied link to clipboard'));
																			} else {
																				toast.error($i18n.t('Failed to copy link'));
																			}
																		}}
																		onDelete={() => {
																			selectedNote = note;
																			showDeleteConfirm = true;
																		}}
																	>
																		<button
																			class="self-center w-fit text-sm p-1 dark:text-gray-300 dark:hover:text-white hover:bg-black/5 dark:hover:bg-white/5 rounded-xl"
																			type="button"
																		>
																			<EllipsisHorizontal className="size-5" />
																		</button>
																	</NoteMenu>
																</div>
															</div>

															<div
																class="text-xs text-gray-500 dark:text-gray-500 mb-3 line-clamp-3 min-h-10"
															>
																{#if note.data?.content?.md}
																	{note.data?.content?.md}
																{:else}
																	{$i18n.t('No content')}
																{/if}
															</div>
														</div>

														<div class="text-xs px-0.5 w-full flex justify-between items-center">
															<div>
																{dayjs(note.updated_at / 1000000).fromNow()}
															</div>
															<Tooltip
																content={note?.user?.email ?? $i18n.t('Deleted User')}
																className="flex shrink-0"
																placement="top-start"
															>
																<div class="shrink-0 text-gray-500">
																	{$i18n.t('By {{name}}', {
																		name: capitalizeFirstLetter(
																			note?.user?.name ?? note?.user?.email ?? $i18n.t('Deleted User')
																		)
																	})}
																</div>
															</Tooltip>
														</div>
													</a>
												</div>
											</div>
										{/each}
									</div>
								{/each}
							</div>
						{:else}
							<div class="flex flex-col items-center justify-center h-64 text-center">
								<div class="text-gray-500 dark:text-gray-400 mb-4">
									{#if query}
										{$i18n.t('No notes found matching your search')}
									{:else if selectedFolderId}
										{$i18n.t('No notes in this folder')}
									{:else}
										{$i18n.t('No notes yet')}
									{/if}
								</div>
								<button
									type="button"
									class="flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
									on:click={createNoteHandler}
								>
									<Plus className="w-4 h-4 mr-2" />
									{$i18n.t('Create your first note')}
								</button>
							</div>
						{/if}
					</div>
				{:else}
					<div class="flex items-center justify-center h-full">
						<Spinner />
					</div>
				{/if}
			</div>
		</Pane>

		<!-- NotePanel for AI Chat -->
		<NotePanel bind:show={showPanel}>
			{#if selectedPanel === 'chat'}
				<Chat
					bind:show={showPanel}
					bind:selectedModelId
					bind:messages
					note={createVirtualNote()}
					bind:editing
					bind:streaming
					bind:stopResponseFlag
					editor={null}
					inputElement={null}
					selectedContent={null}
					files={[]}
					onInsert={() => {}}
					onStop={() => {
						stopResponseFlag = true;
					}}
					onEdited={() => {}}
					insertNoteHandler={() => {}}
					scrollToBottomHandler={() => {}}
				/>
			{/if}
		</NotePanel>
	</PaneGroup>

	<!-- Delete Confirmation Dialog -->
	<DeleteConfirmDialog
		bind:show={showDeleteConfirm}
		title={$i18n.t('Delete note?')}
		on:confirm={deleteNoteHandler}
	>
		<div class="text-sm text-gray-500">
			{$i18n.t('Are you sure you want to delete this note? This action cannot be undone.')}
		</div>
	</DeleteConfirmDialog>
</div>

<style>
	.line-clamp-1 {
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	.line-clamp-3 {
		display: -webkit-box;
		-webkit-line-clamp: 3;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	/* 拖放相關樣式 */
	.dragging {
		opacity: 0.5;
		transform: scale(0.95);
		transition: all 0.2s ease;
	}

	.drag-over {
		background-color: rgba(59, 130, 246, 0.1);
		border: 2px dashed #3b82f6;
		transition: all 0.2s ease;
	}

	.drop-target {
		background-color: rgba(34, 197, 94, 0.1);
		border: 2px solid #22c55e;
	}
</style>
