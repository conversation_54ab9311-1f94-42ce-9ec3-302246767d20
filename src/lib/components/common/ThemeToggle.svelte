<script lang="ts">
	import { theme } from '$lib/stores';
	import Moon from '$lib/components/icons/Moon.svelte';
	import Sun from '$lib/components/icons/Sun.svelte';
	import Tooltip from '$lib/components/common/Tooltip.svelte';
	import { getContext, onMount } from 'svelte';
	import type { Readable } from 'svelte/store';

	const i18n = getContext('i18n') as unknown as Readable<any>;

	export let className = '';

	let isDark = false;

	const updateIsDark = () => {
		isDark = document.documentElement.classList.contains('dark');
	};

	const themeChangeHandler = () => {
		// Toggle between dark and light mode based on ACTUAL applied theme
		const newTheme = isDark ? 'light' : 'dark';
		theme.set(newTheme);
		localStorage.setItem('theme', newTheme);
		applyTheme(newTheme);
		updateIsDark();
	};

	const applyTheme = (_theme: string) => {
		let themeToApply = _theme === 'oled-dark' ? 'dark' : _theme;

		if (_theme === 'system') {
			themeToApply = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
		}

		if (themeToApply === 'dark' && !_theme.includes('oled')) {
			document.documentElement.style.setProperty('--color-gray-800', '#333');
			document.documentElement.style.setProperty('--color-gray-850', '#262626');
			document.documentElement.style.setProperty('--color-gray-900', '#171717');
			document.documentElement.style.setProperty('--color-gray-950', '#0d0d0d');
		}

		// Remove all theme classes
		document.documentElement.classList.remove('dark', 'light', 'rose-pine', 'rose-pine-dawn', 'oled-dark');

		// Add the new theme class
		themeToApply.split(' ').forEach((e) => {
			document.documentElement.classList.add(e);
		});

		const metaThemeColor = document.querySelector('meta[name="theme-color"]');
		if (metaThemeColor) {
			metaThemeColor.setAttribute(
				'content',
				_theme === 'dark'
					? '#171717'
					: _theme === 'oled-dark'
						? '#000000'
						: _theme === 'her'
							? '#983724'
							: '#ffffff'
			);
		}

		if (typeof window !== 'undefined' && window.applyTheme) {
			window.applyTheme();
		}

		if (_theme.includes('oled')) {
			document.documentElement.style.setProperty('--color-gray-800', '#101010');
			document.documentElement.style.setProperty('--color-gray-850', '#050505');
			document.documentElement.style.setProperty('--color-gray-900', '#000000');
			document.documentElement.style.setProperty('--color-gray-950', '#000000');
			document.documentElement.classList.add('dark');
		}
	};

	onMount(() => {
		updateIsDark();
		// Track root class changes to keep icon in sync even if theme changes elsewhere
		const observer = new MutationObserver(() => updateIsDark());
		observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] });

		// Also react to system theme changes when using system theme
		const mq = window.matchMedia('(prefers-color-scheme: dark)');
		const mqHandler = () => {
			// Only relevant when system theme is selected; however, root class might be updated elsewhere.
			updateIsDark();
		};
		mq.addEventListener?.('change', mqHandler);

		return () => {
			observer.disconnect();
			mq.removeEventListener?.('change', mqHandler);
		};
	});
</script>

<Tooltip content={$i18n.t(isDark ? 'Light mode' : 'Dark mode')}>
	<button
		class="flex items-center gap-1 px-3 py-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors {className}"
		on:click={themeChangeHandler}
		aria-label={$i18n.t('Toggle theme')}
	>
		<div class="self-center">
			{#if isDark}
				<Sun className="size-5" strokeWidth="1.5" />
			{:else}
				<Moon className="size-5" strokeWidth="1.5" />
			{/if}
		</div>
	</button>
</Tooltip>
